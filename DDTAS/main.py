from sys import exit,argv
import os
import pandas as pd
import numpy as np
import string
import XLSXAutoFitColumns

reportName = argv[1]
foldername = argv[2]
dir = argv[3]
os.chdir(os.path.join(dir,foldername))
print(f'current Directory {os.getcwd()}')
def Convert_pect(df,col):
    df[col] = pd.Series(["{0:.2f}%".format(val * 100) for val in df[col]], index = df.index)

# First Report
Decline_Summary = pd.read_csv("DeclineTransactionSummaryAcquirerWise_1.csv")
Decline_Summary.fillna('Blank' , inplace=True)
total = Decline_Summary["Total Transaction"].str.replace(",",'').astype('int64').sum()
Decline_Summary.drop(["Total Transaction"] , axis=1 , inplace=True)
All_series = pd.DataFrame({
    "Acquirer Name":"Grand Total",
    "Success Count":Decline_Summary["Success Count"].sum(),
    "Decline Count" : Decline_Summary["Decline Count"].sum(),
    "% Success Rate" : Decline_Summary["Success Count"].sum()/total,
    "Declined due to Invalid Card" :  Decline_Summary["Declined due to Invalid Card"].sum(),
    "% Declined due to Invalid Card" : Decline_Summary["% Declined due to Invalid Card"].mean(),
    "% Declined due to other Reason" : Decline_Summary["% Declined due to other Reason"].mean()
},index=[0])
Decline_Summary = Decline_Summary[['Acquirer Name', 'Success Count','Decline Count','% Success Rate','Declined due to Invalid Card','% Declined due to Invalid Card', '% Declined due to other Reason',]]

Decline_Summary = pd.concat([Decline_Summary.sort_values("% Success Rate" , ascending=False),All_series])

column_to_pct = ['% Declined due to Invalid Card', '% Declined due to other Reason','% Success Rate']
for col in column_to_pct:
    Convert_pect(Decline_Summary,col)

Decline_Summary.set_index("Acquirer Name",inplace=True)

# Second Report

AcqWiseDecline = pd.read_csv("AcquirerWiseDeclineSummary.csv")
AcqWiseDecline["Acquirer Name"].fillna("Blank",inplace=True)
AcqWiseDecline.loc[AcqWiseDecline["Additional Information"] == "Blank", "Additional Information"] =  "Error in transaction"
AcqWiseDecline=AcqWiseDecline[AcqWiseDecline["Acquirer Name"]!='All']
AcqWiseDecline = AcqWiseDecline[AcqWiseDecline["Additional Information"]!='All']
AcqWiseDecline.rename({"ZN(LOOKUP(COUNTD([Transaction ID]),0))":"Count"},inplace=True,axis=1)
if AcqWiseDecline["Count"].dtype == "object":
     AcqWiseDecline["Count"]=AcqWiseDecline["Count"].str.replace(",",'').astype('int64')
AcqWiseDecline=AcqWiseDecline.pivot_table(index="Additional Information",columns="Acquirer Name",values = "Count",aggfunc='sum',margins=True,margins_name="Grand Total")
AcqWiseDecline = pd.concat([AcqWiseDecline.reset_index(names=["Additional Information"]).sort_values("Grand Total",ascending=False).iloc[1:],
                                    AcqWiseDecline.reset_index(names=["Additional Information"]).sort_values("Grand Total",ascending=False).iloc[:1]])
AcqWiseDecline = AcqWiseDecline.set_index(AcqWiseDecline["Additional Information"]).drop(["Additional Information"],axis=1)
# Third Report
# Third Report
sucess_indigo = pd.read_csv("Success_Count.csv")
sucess_indigo = sucess_indigo[["Indigo / Non Indigo","Success count","Declined Due to TSS (TID Counts)","Declined Due to Other Reason","Decline But Reason Is Blank","Total Decline Count","Success %","Decline Due To Tss%","Decline Due To Other Reason%","Decline But Reason Is Blank%","Total Transaction (Indigo)"]]

if sucess_indigo["Decline Due To Other Reason%"].dtype == "object":
    sucess_indigo["Decline Due To Other Reason%"] = sucess_indigo["Decline Due To Other Reason%"].str.replace("%",'').astype(float)/100
Decline_TSS_Count = 0
Decline_TID = 0
for val in sucess_indigo["Declined Due to TSS (TID Counts)"]:
    res = [word.strip(string.punctuation) for word in val.split() if word.strip(string.punctuation).isalnum()]
    Decline_TSS_Count+=int(res[0])
    Decline_TID+=int(res[1])
    
All_series = pd.DataFrame({
    "Indigo / Non Indigo":"Grand Total",
    "Success count":sucess_indigo["Success count"].sum(),
    "Declined Due to TSS (TID Counts)":f'{Decline_TSS_Count} ({Decline_TID})',
    "Declined Due to Other Reason":sucess_indigo["Declined Due to Other Reason"].sum(),
    "Decline But Reason Is Blank":sucess_indigo["Decline But Reason Is Blank"].sum(),
    "Total Decline Count":sucess_indigo["Total Decline Count"].sum(),
    "Success %":sucess_indigo["Success count"].sum()/sucess_indigo["Total Transaction (Indigo)"].sum() ,
    "Decline Due To Tss%":Decline_TSS_Count/sucess_indigo["Total Decline Count"].sum(),
    "Decline Due To Other Reason%":sucess_indigo["Declined Due to Other Reason"].sum()/sucess_indigo["Total Decline Count"].sum(),
    "Decline But Reason Is Blank%":sucess_indigo["Decline But Reason Is Blank"].sum()/sucess_indigo["Total Decline Count"].sum(),
},index=[0])

sucess_indigo.drop("Total Transaction (Indigo)",axis=1,inplace=True)
sucess_indigo = pd.concat([sucess_indigo,All_series])
sucess_indigo.set_index("Indigo / Non Indigo",inplace=True)

columns_to_convert = ["Success %","Decline Due To Tss%","Decline Due To Other Reason%","Decline But Reason Is Blank%"]
for column in columns_to_convert:
    Convert_pect(sucess_indigo,column)

# Fourth Report

Indigo_Declined_Summary = pd.read_csv("Indigo_Declined_summary.csv")
Indigo_Declined_Summary.loc[Indigo_Declined_Summary["Additional Information"] == "Blank", "Additional Information"] =  "Error in transaction"
Indigo_Declined_Summary.loc[Indigo_Declined_Summary["Additional Information"] == "All", "Additional Information"] =  "Grand Total"
Indigo_Declined_Summary.columns = ["Additional_Information","Transaction_Status","Count"]
if Indigo_Declined_Summary["Count"].dtype == "object":
    Indigo_Declined_Summary["Count"]=Indigo_Declined_Summary["Count"].str.replace(",",'').astype('int64')
Indigo_Declined_Summary = Indigo_Declined_Summary.pivot_table(columns='Transaction_Status',index="Additional_Information",values="Count")
Indigo_Declined_Summary = pd.concat([Indigo_Declined_Summary.reset_index(names=["Additional Information"]).sort_values("All",ascending=False).iloc[1:],
                                    Indigo_Declined_Summary.reset_index(names=["Additional Information"]).sort_values("All",ascending=False).iloc[:1]])

if 'Inprocess' not in  Indigo_Declined_Summary.columns:
    Indigo_Declined_Summary = Indigo_Declined_Summary[["Additional Information","Decline","Declined","All"]]
else :
    Indigo_Declined_Summary = Indigo_Declined_Summary[["Additional Information","Decline","Declined","Inprocess","All"]]
Indigo_Declined_Summary.set_index("Additional Information",inplace=True)
Indigo_Declined_Summary.rename({"All":"Grand Total"},inplace=True,axis=1)

# Writing to Excel
file_name = f"{foldername} Decline_Transaction_Summary_Report_Daily.xlsx"
with pd.ExcelWriter(file_name) as writer:

    # use to_excel function and specify the sheet_name and index
    # to store the dataframe in specified sheet
    Decline_Summary.to_excel(writer, sheet_name=f"Decline TXN Summary ACQ Wise")
    AcqWiseDecline.to_excel(writer, sheet_name=f"Acquirer Wise Decline Summary")
    sucess_indigo.to_excel(writer, sheet_name=f"Success Count & %")
    Indigo_Declined_Summary.to_excel(writer, sheet_name=f"INDIGO Decline Summary")

fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(file_name)
fix_worksheet.process_all_worksheets()

exit()

