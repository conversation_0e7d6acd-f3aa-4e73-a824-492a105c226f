#!/bin/bash

dir=$2
cd $dir
tabcmd login -s http://192.168.4.15:8080 -u tabadmin -p $1
foldername=$(date -d "yesterday" '+%Y%m%d')
echo $foldername
[ ! -d "$foldername" ] && mkdir -p "$foldername"
cd $foldername
tabcmd export "/DeclinedTransactionSummary-Daily/DeclineTransactionSummaryAcquirerWise_1?:refresh=yes" --csv -f "DeclineTransactionSummaryAcquirerWise_1.csv"
tabcmd export "/DeclinedTransactionSummary-Daily/AcquirerWiseDeclineSummary?:refresh=yes" --csv -f "AcquirerWiseDeclineSummary.csv"
tabcmd export "/DeclinedTransactionSummary-Daily/SuccessCount_1?:refresh=yes" --csv -f "Success_Count.csv"
tabcmd export "/DeclinedTransactionSummary-Daily/INDIGODeclineSummary?:refresh=yes" --csv -f "Indigo_Declined_summary.csv"

#tabcmd get "/views/DeclinedTransactionSummary-Daily/DeclineTransactionSummaryAcquirerWise_1.png" -f "01_DeclineSummary.png"
#tabcmd get "/views/DeclinedTransactionSummary-Daily/AcquirerWiseDeclineSummary.png" -f "02_DeclineSummary.png"
#tabcmd get "/views/DeclinedTransactionSummary-Daily/SuccessCount_1.png" -f "03_DeclineSummary.png"
#tabcmd get "/views/DeclinedTransactionSummary-Daily/INDIGODeclineSummary.png" -f "04_DeclineSummary.png"
tabcmd logout

cd ..

reportName="Daily_Declined_txn_acquirer_summary"

python3 /home/<USER>/Reports/Scripts/DDTAS/main.py $reportName $foldername $dir
filename="Decline_Transaction_Summary_Report_Daily.xlsx"

python3 /home/<USER>/Reports/Scripts/DDTAS/mail.py $foldername $filename $dir

cd $dir

cd $foldername
#rm -rf *.csv
#rm -rf *.png
echo "Completed"
