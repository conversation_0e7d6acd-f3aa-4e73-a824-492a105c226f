import os
import sys
import smtplib
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from email.mime.image import MIMEImage
from enviroment import From,server_name,to,Port,Password,Username
from datetime import datetime, timedelta
import glob
import pandas as pd

#print(os.getcwd())
msg = MIMEMultipart( "alternative", None)
#msg = MIMEMultipart()
msg['From'] = From
msg["To"] = ','.join(to)
#msg["Cc"] = to
msg['Subject'] = f"Decline TXN Summary Dated on {(datetime.now() - timedelta(1)).strftime('%d-%m-%Y')}"
folder_name = sys.argv[1]
file_name = sys.argv[2]

htmlEmail = f"""
<p> Dear Sir/Madam, <br/>
    Please find the attached Report dated {(datetime.now() - timedelta(1)).strftime('%d-%m-%Y')}.<br/></p>
"""

#msg.attach(MIMEText(htmlEmail, 'html'))


f = os.path.join(os.getcwd(),folder_name,f"{folder_name} {file_name}")
#html tables data
xl = pd.ExcelFile(f)
sheet_name = xl.sheet_names  # see all sheet names
def generate_html(f,sheet):
    read_file = pd.read_excel(f,sheet_name=sheet)
    html_file = f'''<h5>{sheet}<h5><br/>
    {read_file.to_html()}'''
    msg.attach(MIMEText(html_file, 'html'))

for sheet in sheet_name:
    generate_html(f,sheet)

htmlEmail2 = """
<br/>
<p> Please contact Dheerajkuamr Pal (<EMAIL>) directly if you have any questions. <br/>
    Thank you! <br/><br/>
    Best Regards, <br/>
    Dheerajkumar Pal </p>
<br/><br/>
<font color="red">Please do not reply to this email as it is auto-generated. </font>
"""

msg.attach(MIMEText(htmlEmail2, 'html'))

with open(f, "rb") as attached_file:
    part = MIMEApplication(
            attached_file.read(),
            Name=os.path.basename(f)
            )
# After the file is closed
part['Content-Disposition'] = 'attachment; filename="%s"' % os.path.basename(f)
msg.attach(part)
server = smtplib.SMTP(server_name, Port)
server.starttls()
server.login(Username,Password)
text = msg.as_string()
server.sendmail(From, to, text)
server.quit()

print("Email are sent successfully!")

