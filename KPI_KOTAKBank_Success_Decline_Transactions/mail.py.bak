import os
import sys
import smtplib
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from email.mime.image import MIMEImage
from enviroment import From,server_name,to,Port,Password,Username
from datetime import datetime, timedelta
import glob
import pandas as pd
from DataQuery import output_file
#print(os.getcwd())

msg = MIMEMultipart()
msg['From'] = From
msg["To"] = ','.join(to)
#msg["Cc"] = to
msg['Subject'] = f"KPI Success & Decline Transactions for KOTAK Bank for the month of  {(datetime.now() - timedelta(1)).strftime('%b-%Y')}"
folder_name = sys.argv[1]
file_name = sys.argv[2]

htmlEmail = f"""
<p> Dear Sir/Madam, <br/><br/>
    Please refer below the summary of Success and Decline Transactions for KOTAK Bank for the month of {(datetime.now() - timedelta(1)).strftime('%b-%Y')}.<br/>
</p>
"""

#msg.attach(MIMEText(htmlEmail, 'html'))
f = output_file
#f = os.path.join(os.getcwd(),folder_name,f"{folder_name} {file_name}")
#html tables data
xl = pd.ExcelFile(f)
htmlEmail2 = """
<p> Please contact Enterprise Support (<EMAIL>) directly if you have any questions. <br/>
    Thank you! <br/>
    Best Regards, <br/>
    Enterprise Support </p>
<br/>
<font color="red">Please do not reply to this email as it is auto-generated. </font>
"""
htmlEmail = "<br/>".join([htmlEmail,htmlEmail2])
msg.attach(MIMEText(htmlEmail, 'html'))

with open(f, "rb") as attached_file:
    part = MIMEApplication(
            attached_file.read(),
            Name=os.path.basename(f)
            )
# After the file is closed
part['Content-Disposition'] = 'attachment; filename="%s"' % os.path.basename(f)
msg.attach(part)
server = smtplib.SMTP(server_name, Port)
server.starttls()
server.login(Username,Password)
text = msg.as_string()
server.sendmail(From, to, text)
server.quit()

print("Email are sent successfully!")

