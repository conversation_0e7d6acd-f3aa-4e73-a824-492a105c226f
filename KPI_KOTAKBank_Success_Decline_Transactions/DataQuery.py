import os 
import sys
import datetime as dt
from datetime import date
from datetime import timedelta,datetime

out_dir="/home/<USER>/Reports/KPI_KOTAKBank_Success_Decline_Transactions"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%d%m%Y")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
Historical_data = "/home/<USER>/Reports/Scripts/KPI_KOTAKBank_Success_Decline_Transactions/Historical_Data.xlsx"
output_file_name = f'KPI_Success_Decline_Transactions_for_the_month_of_{(datetime.now() - timedelta(1)).strftime("%b_%y")}.xlsx'
output_file = os.path.join(out_dir,yesterday_file_name,output_file_name)

def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

def date_serial_number(serial_number: int):
    """
    Convert an Excel serial number to a Python datetime object
    :param serial_number: the date serial number
    :return: a datetime object
    """
    # Excel stores dates as "number of days since 1900"
    delta = dt.datetime(1899, 12, 30) + dt.timedelta(days=serial_number)
    return delta

#in_dir = sys.argv[1]
#out_dir = sys.argv[2]
config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}
query= '''
SELECT 
    t.id AS 'Transaction ID',
    DATE(t.transactionTime) AS transactionTime,
    t.txnStatusName,
    t.statusID,
    t.typeID,
    CASE
        WHEN
            t.statusID NOT IN (2 , 7, 10)
                AND t.typeID NOT IN (3 , 10, 11, 16, 7)
        THEN
            CASE
                WHEN
                    t.tgID IS NOT NULL
                        AND tdc.description != ''
                THEN
                    tdc.description
                WHEN t.tgID IS NULL AND tr.description != '' THEN tr.description
            END
        ELSE NULL
    END AS transactionDesc,
    IFNULL(t.responseCode, '91') AS responseCode,
    t.isVoided,
    t.refundStatus
FROM
    transactions t
        LEFT JOIN
    emiData emi ON t.id = emi.transactionId
        LEFT JOIN
    (SELECT 
        id, enterpriseId, status
    FROM
        merchant
    WHERE
        status = 'A') m ON t.merchantID = m.id
        LEFT JOIN
    transaction_status ts ON t.statusID = ts.id
        LEFT JOIN
    txn_decline_codes tdc ON t.tgId = tdc.tgID
        AND t.responseCode = tdc.responseCode
        LEFT JOIN
    txn_decline_codes tr ON t.responseCode = tr.responseCode
        AND tr.tgID IS NULL
WHERE
    t.transactionTime >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
   -- t.transactionTime between '2024-06- 00:00:00' and '2024-06-29 23:59:59'
        AND t.transactionTime < CURDATE()
        AND t.acqId = 21
        ;
'''
