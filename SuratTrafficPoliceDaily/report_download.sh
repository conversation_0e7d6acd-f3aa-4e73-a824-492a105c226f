#!/bin/bash

dir=$2
cd $dir
tabcmd login -s http://************:8080 -u tabadmin -p $1
foldername=$(date -d "yesterday" '+%Y%m%d')
echo $foldername
[ ! -d "$foldername" ] && mkdir -p "$foldername"
cd $foldername
tabcmd export "/SuratTrafficPoliceDaily/Summary?:refresh=yes" --csv -f "Summary.csv"
tabcmd export "/SuratTrafficPoliceDaily/Data?:refresh=yes" --csv -f "Data.csv"
tabcmd logout

cd ..

reportName="SuratTrafficPoliceDaily"

python3 /home/<USER>/Reports/Scripts/SuratTrafficPoliceDaily/main.py $reportName $foldername $dir
filename="Surat_Traffic_Police_Daily.xlsx"

python3 /home/<USER>/Reports/Scripts/SuratTrafficPoliceDaily/mail.py $foldername $filename $dir

cd $dir

cd $foldername
rm -rf *.csv
#rm -rf *.png
echo "Completed"
