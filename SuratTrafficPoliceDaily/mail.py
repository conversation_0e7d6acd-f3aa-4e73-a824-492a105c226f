import os
import sys
import smtplib
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from email.mime.image import MIMEImage
from enviroment import From,server_name,to,Port,Password,Username
from datetime import datetime, timedelta
import glob
import pandas as pd

#print(os.getcwd())

msg = MIMEMultipart()
msg['From'] = From
msg["To"] = ','.join(to)
#msg["Cc"] = to
msg['Subject'] = f"Surat traffic police transaction report for {(datetime.now() - timedelta(1)).strftime('%d-%m-%Y')}"
folder_name = sys.argv[1]
file_name = sys.argv[2]

htmlEmail = f"""
<p> Dear All, <br/><br/>
    We appreciate your association with Mosambee.<br/><br/>             
Please find attached transaction report for  {(datetime.now() - timedelta(1)).strftime('%d-%m-%Y')}.<br/><br/>
</p>
"""

#msg.attach(MIMEText(htmlEmail, 'html'))

f = os.path.join(os.getcwd(),folder_name,file_name)
#html tables data
    
htmlEmail2 = """
<p> Please contact Dheerajkuamr Pal (<EMAIL>) directly if you have any questions. <br/>
    Thank you! <br/>
    Best Regards, <br/>
    Dheerajkumar Pal </p>
<br/>
<font color="red">Please do not reply to this email as it is auto-generated. </font>
"""
htmlEmail = "<br/>".join([htmlEmail,htmlEmail2])
msg.attach(MIMEText(htmlEmail, 'html'))

with open(f, "rb") as attached_file:
    part = MIMEApplication(
            attached_file.read(),
            Name=os.path.basename(f)
            )
# After the file is closed
part['Content-Disposition'] = 'attachment; filename="%s"' % os.path.basename(f)
msg.attach(part)
server = smtplib.SMTP(server_name, Port)
server.starttls()
server.login(Username,Password)
text = msg.as_string()
server.sendmail(From, to, text)
server.quit()

print("Email are sent successfully!")

