from sys import exit,argv
import os
import pandas as pd
import numpy as np
import string
import XLSXAutoFitColumns
from openpyxl.styles import Font, Fill,Border,Alignment
import openpyxl
import datetime as date
from TxnDataPrep import data_retrival,query

reportName = argv[1]
foldername = argv[2]
dir = argv[3]
os.chdir(os.path.join(dir,foldername))

### Summary Data Prep

summary = pd.read_csv("Summary.csv")
summary['Region']=summary.Region.replace('All','Grand Total')
summary['Transaction Type']=summary['Transaction Type'].replace('All','Total')
summary_count =summary.pivot(index=["Region"],columns=["Transaction Type"],values=["Distinct count of id"])
summary_count.columns =  summary_count.columns.droplevel(0)
summary_count=summary_count.reset_index().rename_axis(None,axis=1)
summary_Amount =summary.pivot(index=["Region"],columns=["Transaction Type"],values=["Amount_ByOffence"])
summary_Amount.columns =  summary_Amount.columns.droplevel(0)
summary_Amount=summary_Amount.reset_index().rename_axis(None,axis=1)
if "Online" not in summary_count.columns:
    summary_count.rename({"Cash":"Cash Transaction Count",
                        "Total" : "TOTAL TRANSACTION"
                        },axis=1,inplace=True)
    summary_Amount.rename({"Cash":"Cash Transaction Amount",
                        "Total" : "Amount"
                        },axis=1,inplace=True)
    Summary_final = pd.merge(summary_count,summary_Amount,on="Region")
    order_list = ['Region', 'Cash Transaction Count','Cash Transaction Amount', 'TOTAL TRANSACTION', 'Amount']
elif "Cash" not in summary_count.columns:
    summary_count.rename({"Online":"Online Transation Count",
                        "Total" : "TOTAL TRANSACTION"
                        },axis=1,inplace=True)
    summary_Amount.rename({"Online":"Online Transation Amount",
                        "Total" : "Amount"
                        },axis=1,inplace=True)
    Summary_final = pd.merge(summary_count,summary_Amount,on="Region")
    order_list = ['Region', 'Online Transation Count',
        'Online Transation Amount','TOTAL TRANSACTION', 'Amount']
else :
    summary_count.rename({"Cash":"Cash Transaction Count",
                        "Online":"Online Transation Count",
                        "Total" : "TOTAL TRANSACTION"
                        },axis=1,inplace=True)
    summary_Amount.rename({"Cash":"Cash Transaction Amount",
                        "Online":"Online Transation Amount",
                        "Total" : "Amount"
                        },axis=1,inplace=True)
    Summary_final = pd.merge(summary_count,summary_Amount,on="Region")
    order_list = ['Region', 'Cash Transaction Count','Cash Transaction Amount', 'Online Transation Count',
        'Online Transation Amount','TOTAL TRANSACTION', 'Amount']
Summary_final = Summary_final[order_list]
Summary_final_1= pd.concat([Summary_final.iloc[1:],Summary_final.iloc[:1]]).reset_index(drop=True)


### Data Prep
data = pd.read_csv("Data.csv")
data["DESIGNATION"] = data["DESIGNATION"].where(data["DESIGNATION"] != ' ',data['DESIGNATION_Name'])
data.drop(["DESIGNATION_Name"],axis=1,inplace=True)
data_Summ = data.pivot(index=["Region",'PO ST','DESIGNATION','Name','NO. OF RECEIPT','Amount'],columns=["Offence"],values=["ZN(LOOKUP(SUM([Amount_ByOffence]),0))"])
data_Summ.columns =  data_Summ.columns.droplevel(0)
data_Summ=data_Summ.reset_index().rename_axis(None,axis=1)
data_Summ.DESIGNATION.fillna(" ",inplace=True)
colum_list = data_Summ.columns
colum_list =colum_list.insert(1,"Sr. No")
Data_Final = pd.DataFrame(columns=colum_list)
region_records = {}
region_merge = {}
num = 0
count = 0
for region in data_Summ["Region"].unique():
    data_region = data_Summ[data_Summ["Region"]==region].reset_index(drop=True)
    data_region["Sr. No"]=0
    for i in range(1,data_region.shape[0]+1):
        data_region.loc[i-1,"Sr. No"] = i
    data_region["Sr. No"] = data_region["Sr. No"].astype("int").astype("object")
    data_region = data_region[colum_list]
    total = pd.DataFrame({
        'PO ST':f"{region} TOTAL",
        "NO. OF RECEIPT":data_region["NO. OF RECEIPT"].sum(),
        "Amount":data_region["Amount"].sum()
    },index=[0])
    Data_Final = pd.concat([Data_Final,data_region,total]).reset_index(drop=True)
    Data_Final.fillna(' ',inplace=True)
    region_records[region]=Data_Final.shape[0]-pd.concat([data_region,total]).shape[0]+count
    region_merge[region]=Data_Final.shape[0]+2
    count+=1

Data_Final.drop(["Region"],inplace=True,axis=1)

txnData = data_retrival(query)
## File Prep
with pd.ExcelWriter("Surat_Traffic_Police_Daily.xlsx") as writer:
    Summary_final_1.to_excel(writer, sheet_name="Summary",startcol=0,startrow=1,index=False)
    ws = writer.sheets["Summary"]
    ws['A1'] = f'ONLINE TRANSACTION REPORT for {(date.date.today()-date.timedelta(days =1)).strftime("%d-%m-%Y")}'
    ws['A1'].font = openpyxl.styles.Font(size='11')
    ws.merge_cells(start_row=1,start_column=1,end_row=1,end_column=7)
    for row in ws.iter_rows():
        for cell in row:
            cell.alignment = openpyxl.styles.Alignment(horizontal='center',vertical='center')

    ws.column_dimensions[chr(ord('A'))].width = 12
    ws.column_dimensions[chr(ord('B'))].width = 26
    ws.column_dimensions[chr(ord('C'))].width = 26
    ws.column_dimensions[chr(ord('D'))].width = 26
    ws.column_dimensions[chr(ord('E'))].width = 26
    ws.column_dimensions[chr(ord('F'))].width = 20
    ws.column_dimensions[chr(ord('G'))].width = 15

    Data_Final.to_excel(writer, sheet_name="Data",startcol=0,startrow=1,index=False)
    ws = writer.sheets["Data"]
    ws.row_dimensions[1].height = 50
    ws.row_dimensions[2].height = 125
    # Heading
    ws['A1'] = f'SURAT TRAFFIC POLICE {(date.date.today()-date.timedelta(days =1)).strftime("%d-%m-%Y")}'
    ws['A1'].font = openpyxl.styles.Font(size='36',bold=True)
    ws.merge_cells(start_row=1,start_column=1,end_row=1,end_column=12)
    # Extra row for region number
    for region in region_records.keys():
        ws.insert_rows(region_records[region]+3,amount=1)
        cell_number = f'A{region_records[region]+3}'
        ws[cell_number] = region
    # Center Allignment
    for row in ws.iter_rows():
        for cell in row:
            cell.alignment = openpyxl.styles.Alignment(horizontal='center',vertical='center')
    # Column heading wrap
    for i in range(6,len(colum_list)):
        column_name = chr(ord('A')+i)
        ws.column_dimensions[column_name].width = 30
        ws[f'{column_name}2'].alignment = openpyxl.styles.Alignment(horizontal='center',vertical='center',wrap_text=True)
    ws.merge_cells(start_row=1,start_column=1,end_row=1,end_column=len(colum_list))
    ws.column_dimensions[chr(ord('D'))].width = 40
    ws.column_dimensions[chr(ord('C'))].width = 14
    ws.column_dimensions[chr(ord('B'))].width = 16
    ws.column_dimensions[chr(ord('A'))].width = 10
    ws.column_dimensions[chr(ord('E'))].width = 16
    
    txnData.to_excel(writer, sheet_name="Txn Data",index=False)
#fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(f"EnterpriseSupport{airlines_mapping[i]}.xlsx")
#fix_worksheet.process_all_worksheets()

exit()
