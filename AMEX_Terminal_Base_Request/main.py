from datetime import datetime,timedelta
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import config,input_file,output_file,format_int_with_commas,date_serial_number,query
import XLSXAutoFitColumns

pd.options.display.float_format = '{:,.2f}'.format

Amex_Data =data_retrival(query)
Amex_Data = Amex_Data.astype({"sr no":'int'})
with pd.ExcelWriter(output_file) as writer:
    Amex_Data.to_excel(writer,sheet_name='TID List',index=False)

fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(output_file)
fix_worksheet.process_all_worksheets()
		

print("File Creation is Successfull")
exit()
