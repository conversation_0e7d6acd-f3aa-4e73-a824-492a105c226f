import os 
import sys
import datetime as dt
from datetime import date
from datetime import timedelta

out_dir="/home/<USER>/Reports/AMEX_Terminal_Base_Request/"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%d%m%Y")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
input_file = "/home/<USER>/Reports/Scripts/HDFC_67_First_TXN/HistoricalData_670TID.xlsx"
output_file = os.path.join(out_dir,yesterday_file_name,"AMEX_Terminal_Base_Request.xlsx")

def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

def date_serial_number(serial_number: int):
    """
    Convert an Excel serial number to a Python datetime object
    :param serial_number: the date serial number
    :return: a datetime object
    """
    # Excel stores dates as "number of days since 1900"
    delta = dt.datetime(1899, 12, 30) + dt.timedelta(days=serial_number)
    return delta

#in_dir = sys.argv[1]
#out_dir = sys.argv[2]
config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}

query = ("""
With CTE as (Select * from
(SELECT
    mmut1.terminalID AS 'AMEX_TID',
    mdat1.merchantCode AS Amex_MID,
    m1.businessName AS Merchant_Name,
    mmut1.userName AS UserNumber,
    mat1.acquirerName AS 'Base Acquirer',
    mmut1.recordCreated AS Configuration_date,
    mmut1.divisionID AS divisionID_amex,
    mmut1.status AS Base_TID_Status
FROM
    merchant m1
       inner JOIN
    mapping_merchant_user_terminal mmut1 ON m1.id = mmut1.divisionID
       inner JOIN
    mapping_division_acquirer_tg mdat1 ON m1.id = mdat1.divisionID
        AND mmut1.divAcqTgId = mdat1.id
       inner JOIN
    mapping_acquirer_tg mat1 ON mat1.id = mdat1.acqTgId
        AND mat1.instrumentName = 'CARD'
        AND mdat1.matActiveFlag = 1
WHERE
    mat1.matCode LIKE 'AMEXG%')  as amext_details
	inner join
		(SELECT
    mat.acquirerName,
    mdat.merchantCode,
    mmut.terminalID AS TID,
    mmut.userName AS Username_TID,
    mmut.divisionID AS divisionID,
    mmut.status,
    mmut.recordUpdated
FROM
    merchant m
       inner JOIN
    mapping_merchant_user_terminal mmut ON m.id = mmut.divisionID
       inner JOIN
    mapping_division_acquirer_tg mdat ON m.id = mdat.divisionID
        AND mmut.divAcqTgId = mdat.id
       inner JOIN
    mapping_acquirer_tg mat ON mat.id = mdat.acqTgId
    where mdat.matActiveFlag in (0,1)
     and mmut.programId != 101
                 AND mat.matcode NOT LIKE 'HDFC%US'
            AND mat.matcode NOT LIKE '%DCC'
            AND mat.matcode NOT LIKE 'MOSAMBEEHDFCHDFC'
) terminal on amext_details.divisionID_amex = terminal.divisionID
	and amext_details.UserNumber = terminal.Username_TID
    and amext_details.AMEX_TID != terminal.TID
    and amext_details.Amex_MID != terminal.merchantCode
 where TID not like 'HDFC%'
)
Select
    @sr:=@sr+1 as `sr no`,
	merchantCode as 'MID',
    Merchant_Name as 'MERCHANT NAME',
    UserNumber as 'User Number',
    TID ,
    Amex_MID as 'Amex MID',
    AMEX_TID  as 'AMEX TID',
    acquirerName as 'Acquirer',
    Base_TID_Status as 'Base TID Status',
    Configuration_date as  'Configuration date'
from CTE , (SELECT @sr:= 0) AS sr
order by Configuration_date;
""")
