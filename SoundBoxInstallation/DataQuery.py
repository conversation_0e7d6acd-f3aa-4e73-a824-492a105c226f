import os 
import sys
import datetime as dt
from datetime import date
from datetime import timedelta

out_dir="/home/<USER>/Reports/SoundBoxInstallation/"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%d%m%Y")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
input_file = "/home/<USER>/Reports/Scripts/SoundBoxInstallation/HistoricalData_Soundbox.xlsx"
output_file = os.path.join(out_dir,yesterday_file_name,"SoundBox_Installation_Report.xlsx")

def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

def date_serial_number(serial_number: int):
    """
    Convert an Excel serial number to a Python datetime object
    :param serial_number: the date serial number
    :return: a datetime object
    """
    # Excel stores dates as "number of days since 1900"
    delta = dt.datetime(1899, 12, 30) + dt.timedelta(days=serial_number)
    return delta

#in_dir = sys.argv[1]
#out_dir = sys.argv[2]
config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}

Query = """SELECT
	"" as 'Base shared date',
    mmut.terminalID as TID,
    s.name AS State,
    a.pin AS pin,
    'Installed' as InstallationStatus,
    MAX(mmut.firstTxnDate) AS 'Installed Date',
    '' as Remarks
FROM
    merchant m
        INNER JOIN
    mapping_division_acquirer_tg mdat ON mdat.divisionId = m.id
        INNER JOIN
    mapping_merchant_user_terminal mmut ON mmut.divAcqTgId = mdat.id
        INNER JOIN
    address a ON a.id = m.addressID
        INNER JOIN
    city c ON c.id = a.cityId
        INNER JOIN
    state s ON a.stateId = s.id
        INNER JOIN
    mapping_acquirer_tg mat ON mdat.acqTgId = mat.id
WHERE
    mmut.programId IN (95 , 37)
        AND mmut.firstTxnDate >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        AND mmut.firstTxnDate < CURDATE()
        AND mat.acquirerID = 3
GROUP BY mmut.terminalID;"""
