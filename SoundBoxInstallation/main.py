from datetime import datetime,timed<PERSON>ta
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import config,input_file,output_file,format_int_with_commas,date_serial_number,Query
import XLSXAutoFitColumns

pd.options.display.float_format = '{:,.2f}'.format

SoundBoxTodays = data_retrival(Query)
test_tid = pd.read_csv("/home/<USER>/Reports/Scripts/AllTransactions/Test_TID_List.csv")
SoundBoxHistory = pd.read_excel(
    input_file,
    sheet_name="Data",
    parse_dates=False,
)
data = SoundBoxTodays[~SoundBoxTodays["TID"].isin(test_tid["TID"])].reset_index(
    drop=True
)
SoundBoxHistory = SoundBoxHistory.astype(
    {"Base shared date": "object", "TID": "object", "Remarks": "object"}
).fillna("")
todays = data[~data["TID"].isin(SoundBoxHistory["TID"])].reset_index(drop=True)
SoundBoxHistory["pin"] = (
    SoundBoxHistory["pin"]
    .astype("object")
    .map(lambda x: str(x).strip().rstrip("nan")[:-2])
)

final = (
    pd.concat([SoundBoxHistory, todays], ignore_index=True)
    .fillna("")
    .reset_index()
    .drop(["Sr No"], axis=1)
    .rename({"index": "Sr No"}, axis=1)
)
final["Installed Date"] = pd.to_datetime(final["Installed Date"],format='%d-%m-%Y')
final["year"] = final["Installed Date"].dt.strftime("%Y")
final["month"] = final["Installed Date"].dt.strftime("%b")
final["dayMonth"] = final["Installed Date"].dt.strftime("%d-%b")
yearWise = (
    final[final["year"] != datetime.now().strftime("%Y")]
    .groupby(["year"])
    .count()
    .reset_index()
)
month_wise = (
    final[
        (final["year"] == datetime.now().strftime("%Y"))
        & (final["month"] != datetime.now().strftime("%b"))
    ]
    .groupby(["month"])
    .count()
    .reset_index()
)
Day_Wise = (
    final[
        (final["year"] == datetime.now().strftime("%Y"))
        & (final["month"] == datetime.now().strftime("%b"))
    ]
    .groupby(["dayMonth"])
    .count()
    .reset_index()
)


yearWise = yearWise.drop(
    [
        "Base shared date",
        "TID",
        "State",
        "pin",
        "InstallationStatus",
        "Installed Date",
        "Remarks",
        "month",
        "dayMonth",
    ],
    axis=1,
).rename({"Sr No": "Count", "year": "Years"}, axis=1)
yearWise["Months"] = ""
month_wise["Years"] = datetime.now().strftime("%Y")
month_wise = month_wise.drop(
    [
        "Base shared date",
        "TID",
        "State",
        "pin",
        "InstallationStatus",
        "Installed Date",
        "Remarks",
        "year",
        "dayMonth",
    ],
    axis=1,
).rename({"Sr No": "Count", "month": "Months"}, axis=1)
months = pd.DataFrame(
    {
        "Months": [
            "Jan",
            "Feb",
            "Mar",
            "Apr",
            "May",
            "Jun",
            "Jul",
            "Aug",
            "Sep",
            "Oct",
            "Nov",
            "Dec",
        ],
        "Order": [x for x in range(1, 13)],
    },
    columns=["Months", "Order"],
)
month_wise = pd.merge(
    left=month_wise, right=months, left_on=["Months"], right_on=["Months"], how="inner"
)
month_wise = (
    month_wise.sort_values("Order").drop("Order", axis=1).reset_index(drop=True)
)


Day_Wise["Years"] = datetime.now().strftime("%Y")
Day_Wise = Day_Wise.drop(
    [
        "Base shared date",
        "TID",
        "State",
        "pin",
        "InstallationStatus",
        "Installed Date",
        "Remarks",
        "year",
        "month",
    ],
    axis=1,
).rename({"Sr No": "Count", "dayMonth": "Months"}, axis=1)
LTD = (
    pd.concat([yearWise,month_wise,Day_Wise],ignore_index=True)
)
LTD = LTD.iloc[:,[0,2,1]]

final["Installed Date"] = final["Installed Date"].dt.strftime("%d-%m-%Y")
final.drop(["year","month","dayMonth"],axis=1,inplace=True)

final["Sr No"] = final["Sr No"]+1
grand_total = pd.DataFrame({
    "Years" : "Grand Total",
    "Months" : "  ",
    "Count" : LTD.Count.sum()
},index=[0])
LTD = pd.concat([LTD,grand_total]).reset_index(drop=True)

with pd.ExcelWriter(output_file) as Writer:
    final_to_write = final.astype({"Sr No":'int32',"InstallationStatus":'category',"Remarks":"category","Base shared date":"category","TID":"int32"})
    final_to_write.to_excel(Writer,sheet_name="Data",index=False)
    LTD.to_excel(Writer,sheet_name="LTD Installation",index=False)

with pd.ExcelWriter(input_file) as Writer:
    final.to_excel(Writer,sheet_name="Data",index=False)

fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(output_file)
fix_worksheet.process_all_worksheets()
		

print("File Creation is Successfull")
exit()
