import os
import sys
import smtplib
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ip<PERSON>
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from email.mime.image import MIMEImage
from enviroment import From,server_name,to,Port,Password,Username
from datetime import datetime, timedelta
import glob
import pandas as pd
from DataQuery import output_file
#print(os.getcwd())

msg = MIMEMultipart()
msg['From'] = From
msg["To"] = ','.join(to)
#msg["Cc"] = to
msg['Subject'] = f"Store list for MosambeeStore list for Mosambee dated {(datetime.now() - timedelta(1)).strftime('%d-%m-%Y')}"
folder_name = sys.argv[1]
file_name = sys.argv[2]
if datetime.now().strftime("%d") == '05':
    if datetime.now().month == 1:
        year = datetime.now().year-1
        month = 12
    else:
        year = datetime.now().year
        month = datetime.now().month-1
    StartDate = datetime(year,month, 25)
else:
    StartDate = datetime(datetime.now().year, datetime.now().month,5)

htmlEmail = f"""
<p> Dear Sir/Madam, <br/><br/>
    Please find the attached store list report for the period of {StartDate.strftime('%d-%m-%Y')} and {(datetime.now() - timedelta(1)).strftime('%d-%m-%Y')}.<br/>
</p>
"""

#msg.attach(MIMEText(htmlEmail, 'html'))
f = output_file
#f = os.path.join(os.getcwd(),folder_name,f"{folder_name} {file_name}")
#html tables data
xl = pd.ExcelFile(f)
htmlEmail2 = """
<p> Please contact Dheerajkuamr Pal (<EMAIL>) directly if you have any questions. <br/>
    Thank you! <br/>
    Best Regards, <br/>
    Dheerajkumar Pal </p>
<br/>
<font color="red">Please do not reply to this email as it is auto-generated. </font>
"""
htmlEmail = "<br/>".join([htmlEmail,htmlEmail2])
msg.attach(MIMEText(htmlEmail, 'html'))

with open(f, "rb") as attached_file:
    part = MIMEApplication(
            attached_file.read(),
            Name=os.path.basename(f)
            )
# After the file is closed
part['Content-Disposition'] = 'attachment; filename="%s"' % os.path.basename(f)
msg.attach(part)
server = smtplib.SMTP(server_name, Port)
server.starttls()
server.login(Username,Password)
text = msg.as_string()
server.sendmail(From, to, text)
server.quit()

print("Email are sent successfully!")

