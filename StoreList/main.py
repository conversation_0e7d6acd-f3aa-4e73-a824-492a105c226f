from datetime import datetime,timed<PERSON>ta
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import config,input_file,output_file,format_int_with_commas,date_serial_number,OFFUS_query,ONUS_query
import XLSXAutoFitColumns

pd.options.display.float_format = '{:,.2f}'.format

OffUS_Data = data_retrival(OFFUS_query)
ONUS_Data = data_retrival(ONUS_query)

with pd.ExcelWriter(output_file) as writer:
    OffUS_Data.to_excel(writer,sheet_name='OFFUS',index=False)
    ONUS_Data.to_excel(writer,sheet_name='ONUS',index=False)
fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(output_file)
fix_worksheet.process_all_worksheets()
		

print("File Creation is Successfull")
exit()
