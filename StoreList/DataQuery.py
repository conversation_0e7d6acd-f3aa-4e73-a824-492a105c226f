import os 
import sys
import datetime as dt
from datetime import date,datetime,timedelta

out_dir="/home/<USER>/Reports/StoreList/"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%d%m%Y")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
output_file_name =  f'Store_List_Mosambee_{(datetime.now()).strftime("%d_%b_%y")}.xlsx'
input_file = "/home/<USER>/Reports/Scripts/HDFC_64Series/Historical_Data_64Series.xlsx"
output_file = os.path.join(out_dir,yesterday_file_name,output_file_name)

def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

def date_serial_number(serial_number: int):
    """
    Convert an Excel serial number to a Python datetime object
    :param serial_number: the date serial number
    :return: a datetime object
    """
    # Excel stores dates as "number of days since 1900"
    delta = dt.datetime(1899, 12, 30) + dt.timedelta(days=serial_number)
    return delta

#in_dir = sys.argv[1]
#out_dir = sys.argv[2]
config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}

ONUS_query = """
SELECT
	distinct
    Base.terminalID AS 'Bank TID',
    m.businessName as  Merchant_Name,
    m.dbaName as Store_name,
    a.line1 as 'Store Address',
    c.name as 'STORE_CITY',
    a.pin as 'PIN Code',
    s.name as 'State',
     'NA' as 'Hardware ID',
     mat.acquirerName as 'Bank Name',
     'HDFC' as 'Brand names which are activated for this store',
     m.mcc as MCC
FROM
    merchant m
        INNER JOIN
    mapping_merchant_user_terminal mmut ON m.id = mmut.divisionId
        LEFT JOIN
    mapping_division_acquirer_tg mdat ON m.id = mdat.divisionID
        AND mmut.divAcqTgId = mdat.id
        LEFT JOIN
    mapping_acquirer_tg mat ON mat.id = mdat.acqTgId
        LEFT JOIN
    (SELECT
        mmut1.terminalID, mmut1.divisionID,mmut1.status,mmut1.recordCreated,mmut1.recordUpdated
    FROM
        merchant m1
    JOIN mapping_merchant_user_terminal mmut1 ON m1.id = mmut1.divisionID
    JOIN mapping_division_acquirer_tg mdat1 ON m1.id = mdat1.divisionID
        AND mmut1.divAcqTgId = mdat1.id
    JOIN mapping_acquirer_tg mat1 ON mat1.id = mdat1.acqTgId
        AND mat1.instrumentName = 'CARD'
        AND mdat1.matActiveFlag = 1
	where mmut1.programId in (66,40,39) ) Base ON Base.divisionID = mmut.divisionId
	Left Join address a
    on m.addressID = a.id
    Left Join city c
		on a.cityID = c.id
	Left JOin state s
		on s.id = a.stateID
WHERE mat.matCode LIKE 'HDFC%ONUS' and mmut.programId in (24) and acquirerID = '3'
and Base.terminalID is not null
and mmut.recordCreated between Case when day(curdate()) <= 5 then DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m-25')
	 when day(curdate()) <= 25 and day(curdate())  >= 5 then DATE_FORMAT(CURDATE() , '%Y-%m-05')
     end and CURDATE()
;
"""

#OFFUS
OFFUS_query = """
SELECT
	distinct
    Base.terminalID AS 'Bank TID',
    m.businessName as  Merchant_Name,
    m.dbaName as Store_name,
    a.line1 as 'Store Address',
    c.name as 'STORE_CITY',
    a.pin as 'PIN Code',
    s.name as 'State',
     'NA' as 'Hardware ID',
     mat.acquirerName as 'Bank Name',
     'HDFC' as 'Brand names which are activated for this store',
     m.mcc as MCC
FROM
    merchant m
        INNER JOIN
    mapping_merchant_user_terminal mmut ON m.id = mmut.divisionId
        LEFT JOIN
    mapping_division_acquirer_tg mdat ON m.id = mdat.divisionID
        AND mmut.divAcqTgId = mdat.id
        LEFT JOIN
    mapping_acquirer_tg mat ON mat.id = mdat.acqTgId
        LEFT JOIN
    (SELECT
        mmut1.terminalID, mmut1.divisionID,mmut1.status,mmut1.recordCreated,mmut1.recordUpdated
    FROM
        merchant m1
    JOIN mapping_merchant_user_terminal mmut1 ON m1.id = mmut1.divisionID
    JOIN mapping_division_acquirer_tg mdat1 ON m1.id = mdat1.divisionID
        AND mmut1.divAcqTgId = mdat1.id
    JOIN mapping_acquirer_tg mat1 ON mat1.id = mdat1.acqTgId
        AND mat1.instrumentName = 'CARD'
        AND mdat1.matActiveFlag = 1
	where mmut1.programId in (66,40,39) ) Base ON Base.divisionID = mmut.divisionId
	Left Join address a
    on m.addressID = a.id
    Left Join city c
		on a.cityID = c.id
	Left JOin state s
		on s.id = a.stateID
WHERE mat.matCode LIKE 'HDFC%OFFUS' and mmut.programId in (24) and acquirerID = '3'
and Base.terminalID is not null
and mmut.recordCreated between Case when day(curdate()) <= 5 then DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m-25')
	 when day(curdate()) <= 25 and day(curdate())  >= 5 then DATE_FORMAT(CURDATE() , '%Y-%m-05')
     end and CURDATE()
;
"""
