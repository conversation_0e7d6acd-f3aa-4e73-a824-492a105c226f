from datetime import datetime
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from DataQuery import config

def connect_to_mysql(config, attempts=3, delay=2):
    attempt = 1
    # Implement a reconnection routine
    while attempt < attempts + 1:
        try:
            return mysql.connector.connect(**config)
        except (mysql.connector.Error, IOError) as err:
            if (attempts is attempt):
                # Attempts to reconnect failed; returning None
                print("Failed to connect, exiting without a connection: %s", err)
                return None
            print(
                "Connection failed: %s. Retrying (%d/%d)...",
                err,
                attempt,
                attempts-1,
            )
            # progressive reconnect delay
            time.sleep(delay ** attempt)
            attempt += 1
    return None

def data_retrival(query):
    cnx = connect_to_mysql(config, attempts=3)
    if cnx and cnx.is_connected():
        with cnx.cursor() as cursor:
            result = cursor.execute(query)
            rows = cursor.fetchall()
            df = pd.DataFrame(rows, columns=cursor.column_names)
            cnx.close()
        return df
    else:
        return None
		


