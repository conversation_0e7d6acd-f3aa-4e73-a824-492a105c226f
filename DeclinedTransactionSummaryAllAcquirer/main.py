from datetime import datetime,timed<PERSON>ta
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import India_config,NI_config,output_file,format_int_with_commas,date_serial_number,query
import XLSXAutoFitColumns

pd.options.display.float_format = '{:,.2f}'.format

NI_transactions = data_retrival(NI_config, query)
India_transactions = data_retrival(India_config, query)
data = pd.concat([NI_transactions, India_transactions], ignore_index=True)
data["Status"] = np.where(
    data["txnStatusName"].isin(["Approved", "Signature Pending", "Refund Approved"]),
    "Success",
    "Declined",
)
data["acquirerName"] = data.acquirerName.fillna("Blank")
data["transactionDesc"] = data.transactionDesc.fillna("Error in Transaction")
data = data.astype(
    {
        "txnStatusName": "category",
        "transactionDesc": "category",
        "acquirerName": "category",
        "responseCode": "category",
        "typeID": "int8",
        "isIndigo": "int8",
        "enterpriseId": "float16",
        "Declined_TSS": "int8",
        "Status": "category",
    }
)
data = data.drop_duplicates(subset=["Transaction ID"], ignore_index=True)
test_tid = pd.read_csv("/home/<USER>/Reports/Scripts/AllTransactions/Test_TID_List.csv")
data = data[~data["terminalID"].isin(test_tid["TID"])].reset_index(drop=True)

data = data[data.typeID.isin([1, 7, 8, 9, 14, 15, 27])].reset_index(drop=True)
data["transactionDesc"] = data["transactionDesc"].str.upper()
data["acquirerName"] = data.acquirerName.str.upper()
summary = (
    data.pivot_table(
        index="acquirerName",
        columns="Status",
        values="Transaction ID",
        aggfunc="nunique",
        margins=True,
        margins_name="Grand Total",
    )
    .reset_index()
    .fillna(0)
    .sort_values(by=["Success"], ascending=False)
    .reset_index(drop=True)
    .loc[:, ["acquirerName", "Success", "Declined", "Grand Total"]]
    .rename(
        {
            "acquirerName": "Acquirer",
            "Success": "Success Count",
            "Declined": "Decline Count",
        },
        axis=1,
    )
)
Decline_due_to_inavlid_card = (
    data.groupby("acquirerName")
    .aggregate({"Declined_TSS": "sum"})
    .reset_index()
    .rename(
        {"acquirerName": "Acquirer", "Declined_TSS": "Declined due to Invalid Card"},
        axis=1,
    )
)
Decline_due_to_inavlid_card.loc[len(Decline_due_to_inavlid_card.index)] = [
    "Grand Total",
    Decline_due_to_inavlid_card["Declined due to Invalid Card"].sum(),
]
summary = pd.merge(
    left=summary, right=Decline_due_to_inavlid_card, on="Acquirer", how="left"
)
summary = summary.assign(
    Success_Rate=((summary["Success Count"] / summary["Grand Total"]) * 100)
    .round(2)
    .map(lambda x: f"{x}%"),
    Declined_due_to_Invalid_Card=(
        (summary["Declined due to Invalid Card"] / summary["Decline Count"]) * 100
    )
    .round(2)
    .map(lambda x: f"{x}%"),
    Declined_due_to_other_Reason=(
        (
            (summary["Decline Count"] - summary["Declined due to Invalid Card"])
            / summary["Decline Count"]
        )
        * 100
    )
    .round(2)
    .map(lambda x: f"{x}%"),
)
summary["Declined_due_to_Invalid_Card"] = summary.Declined_due_to_Invalid_Card.map(
    lambda x: x if x != "nan%" else "0.0%"
)
summary["Declined_due_to_other_Reason"] = summary.Declined_due_to_other_Reason.map(
    lambda x: x if x != "nan%" else "0.0%"
)
summary = summary.rename(
    {
        "Success_Rate": "% Success Rate",
        "Declined_due_to_Invalid_Card": "% Declined due to Invalid Card",
        "Declined_due_to_other_Reason": "% Declined due to other Reason",
    },
    axis=1,
)

total = summary.loc[
    :0,
    [
        "Acquirer",
        "Success Count",
        "Decline Count",
        "% Success Rate",
        "Declined due to Invalid Card",
        "% Declined due to Invalid Card",
        "% Declined due to other Reason",
        "Grand Total",
    ],
].reset_index(drop=True)
summary = summary.loc[
    1:,
    [
        "Acquirer",
        "Success Count",
        "Decline Count",
        "% Success Rate",
        "Declined due to Invalid Card",
        "% Declined due to Invalid Card",
        "% Declined due to other Reason",
        "Grand Total",
    ],
].reset_index(drop=True)
Acquirer_Wise_Summary = pd.concat([summary, total], ignore_index=True)

summary = (
    data[data["Status"] == "Declined"]
    .pivot_table(
        index="transactionDesc",
        columns="acquirerName",
        values="Transaction ID",
        aggfunc="nunique",
        margins=True,
        margins_name="Grand Total",
    )
    .reset_index()
    .fillna(0)
    .rename({"transactionDesc": "Additional Information"}, axis=1)
    .sort_values(by="Grand Total", ascending=False)
    .reset_index(drop=True)
)

total = summary.loc[:0, :].reset_index(drop=True)
summary = summary.loc[1:, :].reset_index(drop=True)
Acquirer_Wise_Declined_Summary = pd.concat([summary, total], ignore_index=True)

summary = (
    data.pivot_table(
        index="isIndigo",
        columns="Status",
        values="Transaction ID",
        aggfunc="nunique",
        margins=True,
        margins_name="Grand Total",
    )
    .reset_index()
    .fillna(0)
    .rename(
        {
            "isIndigo": "Indigo / Non Indigo",
            "Success": "Success Count",
            "Declined": "Decline Count",
        },
        axis=1,
    )
    .sort_values(by="Grand Total", ascending=False)
    .reset_index(drop=True)
)
mapping_dict = {0: "Non Indigo", 1: "Indigo", "Grand Total": "Grand Total"}
summary["Indigo / Non Indigo"] = summary["Indigo / Non Indigo"].map(mapping_dict)
summary.sort_values(by="Indigo / Non Indigo", ignore_index=True, inplace=True)
total = summary.loc[:0, :].reset_index(drop=True)
summary = summary.loc[1:, :].reset_index(drop=True)
Indigo_summary = pd.concat([summary, total], ignore_index=True)

choices = [
    "Success",
    "Declined Due to TSS",
    "Decline But Reason Is Blank",
]
conditions = [
    data["Status"] == "Success",
    (data["Status"] == "Declined") & (data["Declined_TSS"] == 1),
    (data["Status"] == "Declined")
    & (data["transactionDesc"] == "ERROR IN TRANSACTION")
    & (data["Declined_TSS"] != 1),
]
data["Indigo_Status"] = np.select(
    conditions, choices, default="Declined Due to Other Reason"
)
summary = (
    data.pivot_table(
        index="isIndigo",
        columns="Indigo_Status",
        values="Transaction ID",
        aggfunc="nunique",
        margins=True,
        margins_name="Grand Total",
    )
    .reset_index()
    .fillna(0)
    .rename(
        {
            "isIndigo": "Indigo / Non Indigo",
            "Success": "Success Count",
            "Declined": "Decline Count",
        },
        axis=1,
    )
    .sort_values(by="Grand Total", ascending=False)
    .reset_index(drop=True)
)
mapping_dict = {0: "Non Indigo", 1: "Indigo", "Grand Total": "Grand Total"}
summary["Indigo / Non Indigo"] = summary["Indigo / Non Indigo"].map(mapping_dict)
summary.sort_values(by="Indigo / Non Indigo", ignore_index=True, inplace=True)
total = summary.loc[:0, :].reset_index(drop=True)
summary = summary.loc[1:, :].reset_index(drop=True)
Indigo_summary_declined_reason = pd.concat([summary, total], ignore_index=True)
Indigo_summary = pd.merge(
    left=Indigo_summary,
    right=Indigo_summary_declined_reason,
    on="Indigo / Non Indigo",
    how="left",
)
Indigo_summary = Indigo_summary[
    [
        "Indigo / Non Indigo",
        "Success Count_x",
        "Declined Due to TSS",
        "Declined Due to Other Reason",
        "Decline But Reason Is Blank",
        "Decline Count",
    ]
]
Indigo_summary = Indigo_summary.rename(
    {"Success Count_x": "Success Count", "Decline Count": "Total Decline Count"}, axis=1
)
Indigo_summary["Success %"] = (
    (
        Indigo_summary["Success Count"]
        / (Indigo_summary["Success Count"] + Indigo_summary["Total Decline Count"])
        * 100
    )
    .round(2)
    .map(lambda x: f"{x}%")
)
Indigo_summary["Declined Due to TSS %"] = (
    (
        (Indigo_summary["Declined Due to TSS"] / Indigo_summary["Total Decline Count"])
        * 100
    )
    .round(2)
    .map(lambda x: f"{x}%")
)
Indigo_summary["Decline Due To Other Reason %"] = (
    (
        (
            Indigo_summary["Declined Due to Other Reason"]
            / Indigo_summary["Total Decline Count"]
        )
        * 100
    )
    .round(2)
    .map(lambda x: f"{x}%")
)
Indigo_summary["Decline But Reason Is Blank %"] = (
    (
        (
            Indigo_summary["Decline But Reason Is Blank"]
            / Indigo_summary["Total Decline Count"]
        )
        * 100
    )
    .round(2)
    .map(lambda x: f"{x}%")
)

TID_Count = pd.DataFrame(
    {
        "Indigo / Non Indigo": ["Indigo", "Non Indigo", "Grand Total"],
        "TID": [
            data[
                (data["Status"] == "Declined")
                & (data["Declined_TSS"] == 1)
                & (data["isIndigo"] == 1)
            ]["terminalID"].nunique(),
            data[
                (data["Status"] == "Declined")
                & (data["Declined_TSS"] == 1)
                & (data["isIndigo"] == 0)
            ]["terminalID"].nunique(),
            data[(data["Status"] == "Declined") & (data["Declined_TSS"] == 1)][
                "terminalID"
            ].nunique(),
        ],
    }
)
Indigo_summary = pd.merge(
    left=Indigo_summary, right=TID_Count, on="Indigo / Non Indigo", how="left"
)
Indigo_summary["Declined Due to TSS"] = Indigo_summary["Declined Due to TSS"].map(
    lambda x: f"{x} ("
) + Indigo_summary["TID"].map(lambda x: f"{x})")
Indigo_summary = Indigo_summary.drop(["TID"], axis=1)

summary = (
    data[(data["Status"] == "Declined") & (data["isIndigo"] == 1)]
    .pivot_table(
        index="transactionDesc",
        columns="txnStatusName",
        values="Transaction ID",
        aggfunc="nunique",
        margins=True,
        margins_name="Grand Total",
    )
    .reset_index()
    .fillna(0)
    .rename({"transactionDesc": "Additional Information"}, axis=1)
    .sort_values(by="Grand Total", ascending=False)
    .reset_index(drop=True)
)
summary = summary.loc[
    :, ["Additional Information", "Declined", "Decline", "Grand Total"]
]
total = summary.loc[:0, :].reset_index(drop=True)
summary = summary.loc[1:, :].reset_index(drop=True)
INDIGO_Decline_Summary = pd.concat([summary, total], ignore_index=True)

with pd.ExcelWriter(output_file) as writer:
    Acquirer_Wise_Summary.to_excel(writer,sheet_name="Decline TXN Summary ACQ Wise",index=False)
    Acquirer_Wise_Declined_Summary.to_excel(writer,sheet_name="Acquirer Wise Decline Summary",index=False)
    Indigo_summary.to_excel(writer,sheet_name="Success Count & %",index=False)
    INDIGO_Decline_Summary.to_excel(writer,sheet_name="INDIGO Decline Summary",index=False)

fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(output_file)
fix_worksheet.process_all_worksheets()
		

print("File Creation is Successfull")
exit()
