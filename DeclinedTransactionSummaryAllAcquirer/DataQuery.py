import os 
import sys
import datetime as dt
from datetime import date
from datetime import timedelta

out_dir="/home/<USER>/Reports/DeclinedTransactionSummaryAllAcquirer/"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%d%m%Y")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
output_file = os.path.join(out_dir,yesterday_file_name,f'{yesterday_file_name}_Decline_Transaction_Summary_Report_Daily.xlsx')

def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

def date_serial_number(serial_number: int):
    """
    Convert an Excel serial number to a Python datetime object
    :param serial_number: the date serial number
    :return: a datetime object
    """
    # Excel stores dates as "number of days since 1900"
    delta = dt.datetime(1899, 12, 30) + dt.timedelta(days=serial_number)
    return delta

India_config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}

NI_config = {
    "host": "************",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234",
    "database": "sfn_transaction",
}

query = """
SELECT
    t.id AS 'Transaction ID',
    t.txnStatusName,
    CASE
        WHEN
            t.statusID NOT IN (2 , 7, 10)
                AND t.typeID NOT IN (3 , 10, 11, 16, 7)
        THEN
            CASE
                WHEN
                    t.tgID IS NOT NULL
                        AND tdc.description != ''
                THEN
                    tdc.description
                WHEN t.tgID IS NULL AND tr.description != '' THEN tr.description
            END
        ELSE NULL
    END AS transactionDesc,
    CASE
        WHEN
            t.tempTID IS NOT NULL AND t.tempTID != 0
        THEN
            CASE
                WHEN t.tempTID != t.terminalID THEN t.tempTID
                ELSE t.terminalID
            END
        ELSE t.terminalID
    END AS terminalID,
    t.acquirerName,
    IFNULL(t.responseCode, '91') AS responseCode,
    CASE
        WHEN t.typeID = 1 AND t.isTip IS NOT NULL THEN 7
        ELSE t.typeID
    END AS typeID,
    CASE
        WHEN
            t.merchantID IN (SELECT
                    m.id
                FROM
                    merchant m
                WHERE
                    m.status = 'A' AND m.enterpriseId = 5737)
        THEN
            1
        ELSE 0
    END AS isIndigo,
    m.enterpriseId,
    CASE
        WHEN
            tdc.responseCode IN ('12' , '14',
                'U0',
                'U1',
                'U2',
                'U3',
                'U4',
                'MD0011')
        THEN
            1
        ELSE 0
    END AS Declined_TSS
FROM
    transactions t
        LEFT JOIN
    emiData emi ON t.id = emi.transactionId
        LEFT JOIN
    (SELECT
        id, enterpriseId, status
    FROM
        merchant
    WHERE
        status = 'A') m ON t.merchantID = m.id
        LEFT JOIN
    transaction_status ts ON t.statusID = ts.id
        LEFT JOIN
    txn_decline_codes tdc ON t.tgId = tdc.tgID
        AND t.responseCode = tdc.responseCode
        LEFT JOIN
    txn_decline_codes tr ON t.responseCode = tr.responseCode
        AND tr.tgID IS NULL
WHERE
    t.transactionTime >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        AND t.transactionTime < CURDATE();
        """
