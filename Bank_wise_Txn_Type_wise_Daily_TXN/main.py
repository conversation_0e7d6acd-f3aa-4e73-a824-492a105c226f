from datetime import datetime,timed<PERSON>ta
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import config,input_file,output_file,format_int_with_commas,date_serial_number,PayByLink_query,query
import XLSXAutoFitColumns

pd.options.display.float_format = '{:,.2f}'.format
data = data_retrival(query)
PayByLinkData = data_retrival(PayByLink_query)
data = pd.concat([data,PayByLinkData]).reset_index(drop=True)
test_tid = pd.read_csv("/home/<USER>/Reports/Scripts/AllTransactions/Test_TID_List.csv")
data = data[~data['terminalID'].isin(test_tid['TID'])].reset_index(drop=True)
data.program_id.fillna(0,inplace=True)

def f(x):
    card_type = x['cardTypeName']
    program_id = int(x['program_id'])
    transaction_name = x['transactionTypeName']
    if transaction_name in ["BQR","UPI"]:
        if x['acqId'] in (3,4):
            if card_type == 'BQR':
                return f"{card_type} Sound Box" if program_id in (37,38, 95) else f"{card_type} POS"
            elif card_type == 'UPI':
                return f"{card_type} Sound Box" if program_id in (37,38, 95) else f"{card_type} POS"
        else:
            return f"{transaction_name} POS"
    else:
        return f"{transaction_name}"

data['transactionTypeName'] = data.apply(f,axis=1)

data_grouped = (data
 .groupby(['transactionTypeName','acquirerName','acqId'])
 .aggregate({'id':'count','amount':'sum'}).reset_index()
)
acquirerTxnTypeWise = pd.read_csv('/home/<USER>/Reports/Scripts/Bank_wise_Txn_Type_wise_Daily_TXN/AcquirerSortingOrder.csv')
data_grouped = pd.merge(acquirerTxnTypeWise.iloc[:,:3], data_grouped,  how='left', left_on=["AcqID"],
                  right_on = ['acqId'])

data_grouped.fillna(0,inplace=True)
data_grouped.sort_values(['Order'],inplace=True)
data_grouped = data_grouped[['Order','AcqName','transactionTypeName','id','amount']]

final = (data_grouped
         .set_index(['Order','AcqName','transactionTypeName'],drop=True)
         .unstack(['transactionTypeName'])
         .fillna(0)
        )

final.columns = pd.MultiIndex.from_tuples(final.set_axis(final.columns.values, axis=1)
                                       .rename(columns={(    'id',               0): ('id', 'DROP Txn Count'),
                                                        (    'id',       'BQR POS'): ('id', 'BQR POS Txn Count'),
                                                        (    'id', 'BQR Sound Box'):('id', 'BQR SoundBox Txn Count'),
                                                        (    'id',          'CBWP'):('id', 'CBWP Txn Count'),
                                                       ('id','EMI'):('id','EMI Txn Count'),
                                                        ('id','PayByLink'):('id','Paybylink Txn Count'),
                                                        ('id','Sale'):('id','Sale Txn Count'),
                                                        ('id','SaleTip'):('id','SaleTip Txn Count'),
                                                        ('id', 'Salecomplete'):('id', 'Salecomplete Txn Count'),
                                                        (    'id',       'UPI POS'):('id','UPI POS Txn Count'),
                                                        (    'id', 'UPI Sound Box'):('id','UPI SoundBox Txn Count'),
                                                        (    'id',          'Void'):('id','Void Txn Count'),
                                                        ('amount',       'BQR POS'):('amount','BQR POS TXN Amount'),
                                                        ('amount', 'BQR Sound Box'):('amount','BQR SoundBox TXN Amount'),
                                                        ('amount',          'CBWP'):('amount','CBWP TXN Amount'),
                                                        ('amount','EMI'):('amount','EMI TXN Amount'),
                                                        ('amount','PayByLink'):('amount','Paybylink TXN Amount'),
                                                        ('amount','Sale'):('amount','Sale TXN Amount'),
                                                        ('amount','SaleTip'):('amount','SaleTip TXN Amount'),
                                                        ('amount', 'Salecomplete'):('amount', 'Salecomplete TXN Amount'),
                                                        ('amount',       'UPI POS'):('amount','UPI POS TXN Amount'),
                                                        ('amount', 'UPI Sound Box'):('amount','UPI SoundBox TXN Amount'),
                                                        ('amount',               0):('amount','DROP TXN Amount'),
                                                        ('amount',          'Void'):('amount','Void TXN Amount')

                                                       }))

final = final.droplevel(level=0,axis=1)

column_list = ['BQR POS Txn Count', 'BQR SoundBox Txn Count',
       'CBWP Txn Count', 'EMI Txn Count', 'Paybylink Txn Count' ,'Wallet Txn Count',
       'Sale Txn Count', 'SaleTip Txn Count', 'Salecomplete Txn Count',
       'UPI POS Txn Count', 'UPI SoundBox Txn Count','Void Txn Count', 
       'BQR POS TXN Amount', 'BQR SoundBox TXN Amount', 'CBWP TXN Amount',
       'EMI TXN Amount', 'Paybylink TXN Amount', 'Wallet TXN Amount', 'Sale TXN Amount',
       'SaleTip TXN Amount', 'Salecomplete TXN Amount', 'UPI POS TXN Amount',
       'UPI SoundBox TXN Amount', 'Void TXN Amount']
for column in column_list:
    if column not in final.columns:
        final[column] = 0
final=final[column_list]
count_column_list = ['Sale Txn Count','BQR POS Txn Count', 'BQR SoundBox Txn Count',
       'UPI POS Txn Count', 'UPI SoundBox Txn Count',
       'CBWP Txn Count', 'EMI Txn Count', 'Paybylink Txn Count','Wallet Txn Count',
        'SaleTip Txn Count', 'Salecomplete Txn Count']
Amt_column_list = ['Sale TXN Amount','BQR POS TXN Amount', 'BQR SoundBox TXN Amount', 'UPI POS TXN Amount',
       'UPI SoundBox TXN Amount', 'CBWP TXN Amount',
       'EMI TXN Amount', 'Paybylink TXN Amount', 'Wallet TXN Amount',
       'SaleTip TXN Amount', 'Salecomplete TXN Amount']
final.reset_index(inplace=True)
final['Total Txn Count'] = final[count_column_list].sum(axis=1)
final['Total TXN Amount'] = final[Amt_column_list].sum(axis=1)
finalToWrite = (final
        .drop('Order',axis=1)
        .rename({'AcqName':'Acquirer'},axis=1))
with pd.ExcelWriter(output_file) as writer:
    finalToWrite.to_excel(writer,index=False)

fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(output_file)
fix_worksheet.process_all_worksheets()
print("File Creation is Successfull")
