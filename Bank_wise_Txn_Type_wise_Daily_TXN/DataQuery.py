import os 
import sys
import datetime as dt
from datetime import date
from datetime import timedelta

out_dir="/home/<USER>/Reports/Bank_wise_Txn_Type_wise_Daily_TXN"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%d%m%Y")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
input_file = "/home/<USER>/Reports/Scripts/HDFC_DCC/Historical_Data_DCC.xlsx"
output_file = os.path.join(out_dir,yesterday_file_name,"Bank_Wise_TXN_Type_Wise_Daily_Summary.xlsx")

def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

def date_serial_number(serial_number: int):
    """
    Convert an Excel serial number to a Python datetime object
    :param serial_number: the date serial number
    :return: a datetime object
    """
    # Excel stores dates as "number of days since 1900"
    delta = dt.datetime(1899, 12, 30) + dt.timedelta(days=serial_number)
    return delta

#in_dir = sys.argv[1]
#out_dir = sys.argv[2]
config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}
query = """
SELECT
    t.id,
    t.transactionTime,
    t.typeID,
    CASE
        WHEN t.typeID = 1 AND t.isTip IS NOT NULL THEN 'Tip Adjusted'
        ELSE t.transactionTypeName
    END AS transactionTypeName,
    t.acqId,
    t.acquirerName,
    CASE
        WHEN t.typeId = 15 AND t.emiType = 'debit' THEN t.additionalAmount / POWER(10, a.decimalAmountLength)
        ELSE t.amount / POWER(10, a.decimalAmountLength)
    END AS amount,
    t.tipAmount / POWER(10, a.decimalAmountLength) AS tipAmount,
    t.additionalAmount / POWER(10, a.decimalAmountLength) AS additionalAmount,
    t.authAmount,
    t.brandCode,
    t.modeName,
    t.cardTypeName,
    tp.program_name,
    t.statusID,
    t.terminalID,
    tp.program_id
FROM
    transactions t
        LEFT JOIN
    acquirer a ON t.acqId = a.id
        LEFT JOIN
    merchant m ON m.id = t.merchantID
        LEFT JOIN
    mapping_merchant_user_terminal mmut ON mmut.divisionId = m.id
        AND t.terminalID = mmut.terminalID
        LEFT JOIN
        mapping_division_acquirer_tg mdat on mmut.divacqtgid = mdat.id
        inner JOIN
        mapping_acquirer_tg mat on mdat.acqTGID = mat.id
        LEFT JOIN
    tbl_program tp ON mmut.programId = tp.program_id
WHERE
    t.acqID IN (3,4,7,8,9,10,11,14,16,17,18,21,26,37,48,51,56,62,63,64,65,68,69,70) and
    t.transactionTime >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        AND t.transactionTime < CURDATE()
        AND t.userName IS NOT NULL
       AND t.typeID IN (1,24,15,22,21,14,9,27,2)
       AND t.statusId IN (2 , 7,10)
       AND case when mmut.status is Null then 'null'
            when mmut.status = 'D' and mmut.recordUpdated >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        AND mmut.recordUpdated < CURDATE() then 'A'
                        else mmut.status end in ('A','null')
       AND (CASE
        WHEN
            t.acquirerName = 'GPIN'
        THEN
            t.tgid = mat.tgid
        ELSE 1
    END)
;

"""

PayByLink_query = """
SELECT
    t.id,
    t.transactionTime,
    t.typeID,
    CASE
        WHEN t.typeID = 1 AND t.isTip IS NOT NULL THEN 'Tip Adjusted'
        ELSE t.transactionTypeName
    END AS transactionTypeName,
    t.acqId,
    t.acquirerName,
    Case when t.typeid = 15 then
                t.additionalAmount / POWER(10, a.decimalAmountLength)
        else t.amount / POWER(10, a.decimalAmountLength)
        END AS amount,
    t.tipAmount / POWER(10, a.decimalAmountLength) AS tipAmount,
    t.additionalAmount / POWER(10, a.decimalAmountLength) AS additionalAmount,
    t.authAmount,
    t.brandCode,
    t.modeName,
    t.cardTypeName,
    t.statusID,
    t.terminalID,
    'PayByLink' as program_name,
    0 as program_id
FROM
    transactions t
        LEFT JOIN
    acquirer a ON t.acqId = a.id
        LEFT JOIN
    merchant m ON m.id = t.merchantID
WHERE
    t.transactionTime >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        AND t.transactionTime < CURDATE()
        AND t.userName IS NOT NULL
        AND t.acqID IN (3,4,7,8,9,10,11,14,16,17,18,21,26,37,48,51,56,62,63,64,65,68,69,70)
        AND t.typeID IN (19)
        AND t.statusId IN (2 , 7)
;
"""

