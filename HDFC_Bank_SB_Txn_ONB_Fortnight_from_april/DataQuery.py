import os 
import sys
import datetime as dt
from datetime import date
from datetime import timedelta

out_dir="/home/<USER>/Reports/HDFC_Bank_SB_Txn_ONB_Fortnight_from_april/"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%d%m%Y")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
output_file = os.path.join(out_dir,yesterday_file_name,f'HDFC_BANK_SB_TXN_ONB_{date.today().strftime("%d%m%Y")}.xlsx')

def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

def date_serial_number(serial_number: int):
    """
    Convert an Excel serial number to a Python datetime object
    :param serial_number: the date serial number
    :return: a datetime object
    """
    # Excel stores dates as "number of days since 1900"
    delta = dt.datetime(1899, 12, 30) + dt.timedelta(days=serial_number)
    return delta

#in_dir = sys.argv[1]
#out_dir = sys.argv[2]
config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}
TID_SETUP="""
WITH TID_SETUP_SUMMARY AS
(SELECT
    mmut.programId,
	terminalID,
    userName,
    DATE(recordCreated) AS Setup_date
FROM
    mapping_merchant_user_terminal mmut
WHERE
    mmut.programId IN ('95','37')
    and mmut.recordCreated between '{stDate}' and '{enDate}'
GROUP BY terminalID,userName)
SELECT
    DATE_FORMAT(Setup_date, '%d/%m/%Y') AS Date,programId, COUNT(*) AS TID_Setup
FROM
    TID_SETUP_SUMMARY
GROUP BY Setup_date,programId
ORDER BY 1
;"""

TID_INST = """
WITH TID_SETUP_SUMMARY AS
(SELECT
    mmut.programId,
	terminalID,
    userName,
    DATE(firstTxnDate) AS Setup_date
FROM
    mapping_merchant_user_terminal mmut
WHERE
    mmut.programId IN ('95','37')
    and mmut.recordCreated between '{stDate}' and '{enDate}'
    AND firstTxnDate is not null AND firstTxnDate  between '{stDate}' and '{enDate}'
GROUP BY terminalID,userName)
SELECT
    DATE_FORMAT(Setup_date, '%d/%m/%Y') AS Date,programId, COUNT(*) AS TID_Setup
FROM
    TID_SETUP_SUMMARY
GROUP BY Setup_date,programId
ORDER BY 1
;"""


