from datetime import datetime,timedelta
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
import dateutil
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import config,output_file,format_int_with_commas,date_serial_number,TID_SETUP,TID_INST
import XLSXAutoFitColumns

pd.options.display.float_format = '{:,.2f}'.format
def QueryCreation():
    if datetime.now().day == 1:
        startDate =  datetime((datetime.now() + dateutil.relativedelta.relativedelta(months=-1)).year,(datetime.now() + dateutil.relativedelta.relativedelta(months=-1)).month,16)
        lastDate = datetime(datetime.now().year,datetime.now().month,datetime.now().day)
    else:
        startDate =  datetime((datetime.now()- timedelta(days=1)).year,(datetime.now()- timedelta(days=1)).month,1)
        lastDate = datetime(datetime.now().year,datetime.now().month,datetime.now().day)
    counter = 0
    data_till_date = pd.DataFrame(columns=['Month','terminalID','FirstTransactionDate','TransactionCount','TotalAmount'])
    while lastDate > startDate:
        stDate = startDate.strftime("%Y-%m-%d 00:00:00")
        enDate = startDate.strftime("%Y-%m-%d 23:59:59")
        if startDate > lastDate:
            enDate = lastDate.strftime("%Y-%m-%d 23:59:59")
        startDate += timedelta(1)
        Summary_query =f"""
                            SELECT 
                            DATE_FORMAT(t.transactionTime, '%d/%m/%Y') AS Month,
                            t.terminalID,
                            mmut.programId,
                            mmut.firstTxnDate AS FirstTransactionDate,
                            COUNT(t.id) AS TransactionCount,
                            SUM(COALESCE(t.amount, 0) / 100) AS TotalAmount
                        FROM
                            transactions t
                                inner JOIN
                            mapping_merchant_user_terminal mmut ON t.divisionID = mmut.id
                                AND mmut.programId IN ('95','37')
                                AND mmut.recordCreated >= '2024-04-01 00:00:00'
                        WHERE
                            t.transactionTime between '{stDate}' and '{enDate}'
                                AND t.acqId = 3
                                AND t.statusId IN (2 , 7)
                                AND t.typeID IN ('22' , '24')
                        GROUP BY 1,2,3
                        ORDER BY 1,2,3;
"""
        data = data_retrival(Summary_query)
        if data.shape[0] > 0:
            data_till_date = pd.concat([data_till_date,data],ignore_index=True)
            del data
        counter+=1
    print(counter)
    return data_till_date

data = QueryCreation()
data.rename({
    'Month':"Date"
},inplace=True , axis=1)
group = data.groupby(["Date","programId"]).aggregate({"TransactionCount":"sum","TotalAmount":"sum"}).reset_index().sort_values(["programId","Date"])
group["Day"], group["Month_sort"],group["Year"] = group["Date"].apply(lambda x : x[0:2]) , group["Date"].apply(lambda x : x[3:5]) ,  group["Date"].apply(lambda x : x[6:])
group_sorted = group.sort_values(['programId','Year','Month_sort','Day'],ignore_index=True)
INSTA_SOUNDBOX , SOUNDBOX = group_sorted[group_sorted["programId"] == 95].reset_index(drop=True), group_sorted[group_sorted["programId"] == 37].reset_index(drop=True)
INSTA_SOUNDBOX.rename({
    "TransactionCount" : "Txn Count-Insta SB",
    "TotalAmount" : 'Txn Amount-Insta SB'
},inplace=True,axis=1)
INSTA_SOUNDBOX = INSTA_SOUNDBOX[["Date","Txn Count-Insta SB","Txn Amount-Insta SB"]]
SOUNDBOX.rename({
    "TransactionCount" : "Txn Count-Non Insta SB",
    "TotalAmount" : 'Txn Amount-Non Insta SB'
},inplace=True,axis=1)
SOUNDBOX = SOUNDBOX[["Date","Txn Count-Non Insta SB","Txn Amount-Non Insta SB"]]
Data_Sb_Insta = pd.merge(left=SOUNDBOX,right=INSTA_SOUNDBOX,on='Date')

def Insta_Setup(data_query):
    if datetime.now().day == 1:
        startDate =  datetime((datetime.now() + dateutil.relativedelta.relativedelta(months=-1)).year,(datetime.now() + dateutil.relativedelta.relativedelta(months=-1)).month,16)
        lastDate = datetime(datetime.now().year,datetime.now().month,datetime.now().day)
    else:
        startDate =  datetime((datetime.now()- timedelta(days=1)).year,(datetime.now()- timedelta(days=1)).month,1)
        lastDate = datetime(datetime.now().year,datetime.now().month,datetime.now().day)
    stDate = startDate.strftime("%Y-%m-%d 00:00:00")
    enDate = lastDate.strftime("%Y-%m-%d 00:00:00")
    print(stDate,enDate)
    data = data_retrival(data_query.format(stDate = stDate,enDate = enDate))
    return data

Setup_data = Insta_Setup(TID_SETUP)
Setup_data["Day"], Setup_data["Month_sort"] , Setup_data["Year"] = Setup_data["Date"].apply(lambda x : x[0:2]) , Setup_data["Date"].apply(lambda x : x[3:5]) , Setup_data["Date"].apply(lambda x : x[6:])

Setup_data = Setup_data.sort_values(['programId','Year','Month_sort','Day'],ignore_index=True)
SoundBox_Setup ,Insta_soundbox_Setup = Setup_data[Setup_data["programId"] == 37] , Setup_data[Setup_data["programId"] == 95]
SoundBox_Setup = SoundBox_Setup.drop(["programId",'Year','Month_sort','Day'],axis=1)
Insta_soundbox_Setup = Insta_soundbox_Setup.drop(["programId",'Year','Month_sort','Day'],axis=1)
INST_data = Insta_Setup(TID_INST)
INST_data["Day"], INST_data["Month_sort"], INST_data["Year"] = INST_data["Date"].apply(lambda x : x[0:2]) , INST_data["Date"].apply(lambda x : x[3:5]),INST_data["Date"].apply(lambda x : x[6:])

INST_data = INST_data.sort_values(['programId','Year','Month_sort','Day'],ignore_index=True)
INST_data = INST_data.drop(['Year','Month_sort','Day'],axis=1)
SoundBox_INST ,Insta_soundbox_INST = INST_data[INST_data["programId"] == 37] , INST_data[INST_data["programId"] == 95]
SoundBox_INST = SoundBox_INST.drop(["programId"],axis=1)
Insta_soundbox_INST = Insta_soundbox_INST.drop(["programId"],axis=1)
SoundBox_Setup.rename({
    "TID_Setup" : "TID Setup-Non Insta SB"
},axis=1,inplace=True)

Insta_soundbox_Setup.rename({
    "TID_Setup" : "TID Setup-Insta SB"
},axis=1,inplace=True)

SoundBox_INST.rename({
    "TID_Setup" : "TID (Inst)-Non Insta SB"
},axis=1,inplace=True)

Insta_soundbox_INST.rename({
    "TID_Setup" : "TID (Inst)-Insta SB"
},axis=1,inplace=True)
Data_Sb_Insta["Txn Count-Overall SB"] = Data_Sb_Insta["Txn Count-Non Insta SB"].astype('int')+Data_Sb_Insta["Txn Count-Insta SB"].astype('int')
Data_Sb_Insta["Txn Amount-Overall SB"] = Data_Sb_Insta["Txn Amount-Non Insta SB"].astype('float')+Data_Sb_Insta["Txn Amount-Insta SB"].astype('float')
SoundBox_Inst_Setup = pd.merge(left=SoundBox_Setup,right=SoundBox_INST,on='Date',how='outer')
SoundBox_Inst_Setup = SoundBox_Inst_Setup.fillna(0).astype({"TID Setup-Non Insta SB":"int","TID (Inst)-Non Insta SB":"int"})
InstaSoundBox_Inst_Setup = pd.merge(left=Insta_soundbox_Setup,right=Insta_soundbox_INST,on='Date',how='outer')
InstaSoundBox_Inst_Setup = InstaSoundBox_Inst_Setup.fillna(0).astype({"TID Setup-Insta SB":"int","TID (Inst)-Insta SB":"int"})
Data_Sb_setup =  pd.merge(left=SoundBox_Inst_Setup,right=Data_Sb_Insta,on='Date',how='outer')
Data_Sb_setup = Data_Sb_setup.fillna(0)
Data_Sb_insta_setup =  pd.merge(left=Data_Sb_setup,right=InstaSoundBox_Inst_Setup,on='Date',how='outer')
Data_Sb_insta_setup = Data_Sb_insta_setup.fillna(0)
Data_Sb_insta_setup["TID Setup-Overall SB"] = Data_Sb_insta_setup['TID Setup-Non Insta SB'].astype('int')+Data_Sb_insta_setup['TID Setup-Insta SB'].astype('int')
Data_Sb_insta_setup["TID (Inst)-Overall SB"] = Data_Sb_insta_setup['TID (Inst)-Non Insta SB'].astype('int')+Data_Sb_insta_setup['TID (Inst)-Insta SB'].astype('int')
Data_Sb_insta_setup = Data_Sb_insta_setup.fillna(0).astype({"TID Setup-Non Insta SB":"int","TID (Inst)-Non Insta SB":"int", "Txn Count-Overall SB":"int" })
Data_Sb_insta_setup["Day"], Data_Sb_insta_setup["Month_sort"] , Data_Sb_insta_setup["Year"] = Data_Sb_insta_setup["Date"].apply(lambda x : x[0:2]) , Data_Sb_insta_setup["Date"].apply(lambda x : x[3:5]) , Data_Sb_insta_setup["Date"].apply(lambda x : x[6:])
Data_Sb_insta_setup = Data_Sb_insta_setup.sort_values(['Year','Month_sort','Day'],ignore_index=True)
Data_Sb_insta_setup = Data_Sb_insta_setup[['Date', 'TID Setup-Non Insta SB', 'TID (Inst)-Non Insta SB','Txn Count-Non Insta SB', 'Txn Amount-Non Insta SB', 'TID Setup-Insta SB',
 'TID (Inst)-Insta SB','Txn Count-Insta SB', 'Txn Amount-Insta SB','TID Setup-Overall SB', 'TID (Inst)-Overall SB', 'Txn Count-Overall SB',
 'Txn Amount-Overall SB']]

data["Month"] = pd.to_datetime(data['Date'],errors='coerce',format='%d/%m/%Y').dt.strftime("%b'%y")
data["FirstTransactionDate_1"] = pd.to_datetime(data['FirstTransactionDate'],errors='coerce',format='%Y-%m/%d %H:%M%S').dt.strftime("%d/%m/%Y %H:%M:%S")

TID_Wise_Group_By = data.groupby(["Month",'terminalID']).aggregate({'FirstTransactionDate':"min",'TransactionCount':'sum','TotalAmount':'sum'}).reset_index().sort_values(['Month','FirstTransactionDate'],ignore_index=True)
TID_Wise_Group_By.columns = ['Month' ,"TID","Date (1st Approved Trn)","Value","Volumn"]

with pd.ExcelWriter(output_file) as writer:
    Data_Sb_insta_setup.to_excel(writer,sheet_name="Daily Summary",index=False)
    TID_Wise_Group_By.to_excel(writer,sheet_name="TID_wise_Value_Volume_Data",index = False)

# Data extraction from SQL
# Data extraction from SQL
fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(output_file)
fix_worksheet.process_all_worksheets()
		

print("File Creation is Successfull")
exit()
