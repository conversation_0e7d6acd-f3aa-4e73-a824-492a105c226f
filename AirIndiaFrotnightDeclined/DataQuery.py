import os 
import sys
import datetime as dt
from datetime import timedelta,datetime,date

out_dir="/home/<USER>/Reports/AirIndiaFrotnightDeclined"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%d%m%Y")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
Transacting_output_FileName = f'AIX_Success_and_Decline_Data_for_the_Month_of_{(datetime.now() - timedelta(1)).strftime("%b%Y")}.xlsx'

Transacting_output = os.path.join(out_dir,yesterday_file_name,Transacting_output_FileName)

def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

def date_serial_number(serial_number: int):
    """
    Convert an Excel serial number to a Python datetime object
    :param serial_number: the date serial number
    :return: a datetime object
    """
    # Excel stores dates as "number of days since 1900"
    delta = dt.datetime(1899, 12, 30) + dt.timedelta(days=serial_number)
    return delta

#in_dir = sys.argv[1]
#out_dir = sys.argv[2]
config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}
query = """
SELECT 
    t.id AS ID,
    DATE(t.transactionTime) AS 'Transaction Created Date',
    t.clientId AS User,
    t.typeID,
    concat("'",t.userName) AS Username,
    CASE
        WHEN t.typeID = 1 AND t.isTip IS NOT NULL THEN 'Tip Adjusted'
        ELSE t.transactionTypeName
    END AS Type,
    t.modeName AS Mode,
    CASE
        WHEN t.typeID = 15 AND t.emiType = 'debit' THEN t.additionalAmount / 100
        ELSE t.amount / 100
    END AS Amount,
    t.authCode AS 'Auth Code',
    t.maskedCardNumber AS Card,
    t.drCrCode AS 'Card Type',
    t.cardTypeName AS 'Brand Type',
    t.rrn AS RRN,
    t.invoiceNumber AS 'Invoice#',
    t.serialNumber AS 'Device Serial',
    CASE
        WHEN t.settlementStatusName IS NULL THEN 'UNSETTLED'
        ELSE t.settlementStatusName
    END AS Status,
    t.settlementTxnTime AS 'Settled On',
    CASE
        WHEN
            t.tempMerchantCode IS NOT NULL
                AND t.tempMerchantCode != 0
        THEN
            CASE
                WHEN t.tempMerchantCode != t.merchantCode THEN t.tempMerchantCode
                ELSE t.merchantCode
            END
        WHEN t.merchantCode IS NULL THEN mdat.merchantCode
        ELSE t.merchantCode
    END AS MID,
    CASE
        WHEN
            t.tempTID IS NOT NULL AND t.tempTID != 0
        THEN
            CASE
                WHEN t.tempTID != t.terminalID THEN t.tempTID
                ELSE t.terminalID
            END
        WHEN t.terminalID IS NULL THEN mmut.terminalID
        ELSE t.terminalID
    END AS TID,
    t.batchNumber AS Batch,
    t.billNumber AS 'Reference#',
    CASE
        WHEN
            tdc.responseCode IN ('12' , '14',
                'U0',
                'U1',
                'U2',
                'U3',
                'U4',
                'MD0011')
        THEN
            1
        ELSE 0
    END AS Declined_TSS,
    CASE
        WHEN
            t.statusID NOT IN (2 , 7, 10)
                AND t.typeID NOT IN (3 , 10, 11, 16, 7)
        THEN
            CASE
                WHEN
                    t.tgID IS NOT NULL
                        AND tdc.description != ''
                THEN
                    tdc.description
                WHEN t.tgID IS NULL AND tr.description != '' THEN tr.description
            END
        ELSE NULL
    END AS 'Additional Information',
    t.latitude AS Latitude,
    t.longitude AS Longitude,
    t.cardHolderName AS Payer,
    t.securityToken AS 'TID Location',
    TIME(t.transactionTime) AS 'Transaction Created Time',
    t.txnStatusName AS 'Transaction Status',
    t.merchantName AS 'ME Name',
    t.acquirerName AS Acquirer,
    IFNULL(t.responseCode, '91') AS 'Response Code',
    t.currencyCode AS Currency,
    t.deviceID AS 'Device No',
    t.referenceTxnID AS 'Reference Txn Id',
    t.tgName AS tg,
    t.dbaName AS 'DBA Name',
    t.refundStatus AS 'Refund Status',
    DATE(t.txnResponseTime) AS 'Transaction Response Date',
    TIME(t.txnResponseTime) AS 'Transaction Response Time',
    m.enterpriseId,
    CASE
        WHEN
            settlementStatusID != 4
        THEN
            CASE
                WHEN t.forSettlement = 1 THEN 'Auto Settlement'
                WHEN t.forSettlement = 0 THEN 'Manual Settlement'
                WHEN t.forSettlement = 2 THEN 'Customized Settlement'
            END
        ELSE 'NA'
    END AS 'Settlement Flag',
    t.responseCode,
    otr.offlineTxnTime
FROM
    offline_txn_request otr
        LEFT JOIN
    transactions t ON otr.billNumber = t.billNumber
    left join
    merchant m ON t.merchantID = m.id
        LEFT JOIN
    emiData emi ON t.id = emi.transactionId
        LEFT JOIN
    transaction_status ts ON t.statusID = ts.id
        LEFT JOIN
    transaction_type tt ON tt.id = t.typeID
        LEFT JOIN
    txn_decline_codes tdc ON t.tgId = tdc.tgID
        AND t.responseCode = tdc.responseCode
        LEFT JOIN
    txn_decline_codes tr ON t.responseCode = tr.responseCode
        AND tr.tgID IS NULL
        LEFT JOIN
    mapping_merchant_user_terminal mmut ON mmut.id = t.divisionID
        AND m.id = mmut.divisionID
        LEFT JOIN
    mapping_division_acquirer_tg mdat ON mdat.id = mmut.divAcqTgId
WHERE
	otr.offlineTxnTime BETWEEN CONCAT(DATE_FORMAT(NOW() - INTERVAL 1 DAY, '%Y-%m-01'), ' 00:00:00') AND CONCAT(DATE_FORMAT(NOW() - INTERVAL 1 DAY, '%Y-%m-%d'), ' 23:59:59')
        -- and otr.statusId !=2
        AND m.enterpriseId = '11264'
	;
"""

updatedSuccessQuery = """
WITH date_range AS (
    SELECT
        CONCAT(DATE_FORMAT(NOW() - INTERVAL 1 DAY, '%Y-%m-01'), ' 00:00:00') AS start_date,
        CONCAT(DATE_FORMAT(NOW() - INTERVAL 1 DAY, '%Y-%m-%d'), ' 23:59:59') AS end_date
)
SELECT
    t.id AS ID,
    DATE(t.transactionTime) AS 'Transaction Created Date',
    t.clientId AS User,
    t.typeID,
    t.userName AS Username,
    CASE
        WHEN t.typeID = 1 AND t.isTip IS NOT NULL THEN 'Tip Adjusted'
        ELSE t.transactionTypeName
    END AS Type,
    t.modeName AS Mode,
    CASE
        WHEN t.typeID = 15 AND t.emiType = 'debit' THEN t.additionalAmount / 100
        ELSE t.amount / 100
    END AS Amount,
    t.authCode AS 'Auth Code',
    t.maskedCardNumber AS Card,
    t.drCrCode AS 'Card Type',
    t.cardTypeName AS 'Brand Type',
    t.rrn AS RRN,
    t.invoiceNumber AS 'Invoice#',
    t.serialNumber AS 'Device Serial',
    COALESCE(t.settlementStatusName, 'UNSETTLED') AS Status,
    t.settlementTxnTime AS 'Settled On',
    COALESCE(NULLIF(t.tempMerchantCode, 0), t.merchantCode, mdat.merchantCode) AS MID,
    COALESCE(NULLIF(t.tempTID, 0), t.terminalID, mmut.terminalID) AS TID,
    t.batchNumber AS Batch,
    t.billNumber AS 'Reference#',
    CASE
        WHEN tdc.responseCode IN ('12', '14', 'U0', 'U1', 'U2', 'U3', 'U4', 'MD0011') THEN 1
        ELSE 0
    END AS Declined_TSS,
    COALESCE(
        CASE
            WHEN t.statusID NOT IN (2, 7, 10) AND t.typeID NOT IN (3, 10, 11, 16, 7)
            THEN COALESCE(NULLIF(tdc.description, ''), NULLIF(tr.description, ''))
        END, NULL
    ) AS 'Additional Information',
    t.latitude AS Latitude,
    t.longitude AS Longitude,
    t.cardHolderName AS Payer,
    t.securityToken AS 'TID Location',
    TIME(t.transactionTime) AS 'Transaction Created Time',
    t.txnStatusName AS 'Transaction Status',
    t.merchantName AS 'ME Name',
    t.acquirerName AS Acquirer,
    COALESCE(t.responseCode, '91') AS 'Response Code',
    t.currencyCode AS Currency,
    t.deviceID AS 'Device No',
    t.referenceTxnID AS 'Reference Txn Id',
    t.tgName AS tg,
    t.dbaName AS 'DBA Name',
    t.refundStatus AS 'Refund Status',
    DATE(t.txnResponseTime) AS 'Transaction Response Date',
    TIME(t.txnResponseTime) AS 'Transaction Response Time',
    m.enterpriseId,
    CASE
        WHEN settlementStatusID != 4 THEN
            CASE
                WHEN t.forSettlement = 1 THEN 'Auto Settlement'
                WHEN t.forSettlement = 0 THEN 'Manual Settlement'
                WHEN t.forSettlement = 2 THEN 'Customized Settlement'
            END
        ELSE 'NA'
    END AS 'Settlement Flag',
    tdc.responseCode
FROM transactions t
JOIN merchant m ON t.merchantID = m.id
LEFT JOIN txn_decline_codes tdc ON t.tgId = tdc.tgID AND t.responseCode = tdc.responseCode
LEFT JOIN txn_decline_codes tr ON t.responseCode = tr.responseCode AND tr.tgID IS NULL
LEFT JOIN mapping_merchant_user_terminal mmut ON mmut.id = t.divisionID AND m.id = mmut.divisionID
LEFT JOIN mapping_division_acquirer_tg mdat ON mdat.id = mmut.divAcqTgId
JOIN date_range dr ON t.transactionTime BETWEEN dr.start_date AND dr.end_date
WHERE m.enterpriseId = '11264' AND t.statusID IN (2, 7);
"""
