from datetime import datetime,timedelta
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import config,format_int_with_commas,date_serial_number,query,Transacting_output,updatedSuccessQuery
import XLSXAutoFitColumns

pd.options.display.float_format = '{:,.2f}'.format
updated_data = data_retrival(query)
declineTxn = updated_data[~updated_data['Reference#'].isin(updated_data[updated_data['Transaction Status'].isin(['Approved','Signature Pending'])]['Reference#'].unique())]
declineTxn.reset_index(drop=True,inplace=True)
finalDecline = declineTxn.sort_values(['Reference#','ID'],ascending=[False,False]).drop_duplicates(subset=['Reference#','typeID'])
finalDecline.reset_index(drop=True,inplace=True)
finalDecline = finalDecline[['ID', 'Transaction Created Date', 'User', 'typeID', 'Username', 'Type',
       'Mode', 'Amount', 'Auth Code', 'Card', 'Card Type', 'Brand Type', 'RRN',
       'Invoice#', 'Device Serial', 'Status', 'Settled On', 'MID', 'TID',
       'Batch', 'Reference#', 'Declined_TSS', 'Additional Information',
       'Latitude', 'Longitude', 'Payer', 'TID Location',
       'Transaction Created Time', 'Transaction Status', 'ME Name', 'Acquirer',
       'Response Code', 'Currency', 'Device No', 'Reference Txn Id', 'tg',
       'DBA Name', 'Refund Status', 'Transaction Response Date',
       'Transaction Response Time', 'enterpriseId', 'Settlement Flag','offlineTxnTime']]

finalDecline = finalDecline.sort_values(['Reference#','ID'],ascending=[False,False]).drop_duplicates(subset=['Reference#','typeID'])
finalDecline.reset_index(drop=True,inplace=True)
finalDecline = finalDecline.rename({
    'Declined_TSS':"Declined Due to TSS",
    'final date' : "Base Transaction Date"
},axis=1)

Decline_tss_dict = {1:'Yes',0:'No'}
finalDecline['Declined Due to TSS'] = finalDecline['Declined Due to TSS'].map(Decline_tss_dict)
finalDecline["Additional Information"] = np.where((finalDecline['Brand Type']=='AMEX') & (finalDecline['Response Code']=='MD0014') ,"AMEX Transactions",finalDecline["Additional Information"])
finalDecline["Additional Information"] = finalDecline["Additional Information"].fillna("Error in transaction")

# Success Txn 
succres_transction = data_retrival(updatedSuccessQuery)
voidTxn = succres_transction["Reference#"].value_counts().reset_index()
voidTxn = voidTxn[voidTxn["count"]>1]
successTxn = (succres_transction[~succres_transction["Reference#"].isin(voidTxn["Reference#"])]
             .reset_index(drop=True)
             )
voidTxn = successTxn["Reference Txn Id"].value_counts().reset_index()
voidTxn = voidTxn[voidTxn["count"]>1]
successTxn = (successTxn[~successTxn["Reference Txn Id"].isin(voidTxn["Reference Txn Id"])]
             .reset_index(drop=True)
             )

successTxn["Additional Information"] =successTxn["Additional Information"].fillna("Approved or completed successfullyi")
successTxn = successTxn[['ID', 'Transaction Created Date', 'User', 'typeID', 'Username', 'Type',
       'Mode', 'Amount', 'Auth Code', 'Card', 'Card Type', 'Brand Type', 'RRN',
       'Invoice#', 'Device Serial', 'Status', 'Settled On', 'MID', 'TID',
       'Batch', 'Reference#', 'Declined_TSS', 'Additional Information',
       'Latitude', 'Longitude', 'Payer', 'TID Location',
       'Transaction Created Time', 'Transaction Status', 'ME Name', 'Acquirer',
       'Response Code', 'Currency', 'Device No', 'Reference Txn Id', 'tg',
       'DBA Name', 'Refund Status', 'Transaction Response Date',
       'Transaction Response Time', 'enterpriseId', 'Settlement Flag']]
with pd.ExcelWriter(Transacting_output) as writer:
    successTxn.to_excel(writer,sheet_name="Success Txn",index=False)
    finalDecline.to_excel(writer,sheet_name="Declined Txn",index=False)

fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(Transacting_output)
fix_worksheet.process_all_worksheets()

#fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(Non_Transacting_output)
#fix_worksheet.process_all_worksheets()
print("File Creation is Successfull")
exit()
