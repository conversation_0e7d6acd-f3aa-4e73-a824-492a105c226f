from datetime import datetime,timed<PERSON>ta
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import config,input_file,output_file,format_int_with_commas,date_serial_number,Query
import XLSXAutoFitColumns

pd.options.display.float_format = '{:,.2f}'.format

VyaparTodays = data_retrival(Query)
test_tid = pd.read_csv("/home/<USER>/Reports/Scripts/AllTransactions/Test_TID_List.csv")

VyaparHistory = pd.read_excel(
    input_file,
    sheet_name="Data",
    parse_dates=False,
)
data = VyaparTodays[~VyaparTodays["TID"].isin(test_tid["TID"])].reset_index(
    drop=True
)
VyaparHistory = VyaparHistory.astype(
    {"Base shared date": "object", "TID": "object", "Remarks": "object"}
).fillna("")
todays = data[~data["TID"].isin(VyaparHistory["TID"])].reset_index(drop=True)
VyaparHistory["pin"] = (
    VyaparHistory["pin"]
    .astype("object")
    .map(lambda x: str(x).strip().rstrip("nan"))
)

final = (
    pd.concat([VyaparHistory, todays], ignore_index=True)
    .fillna("")
    .reset_index()
    .drop(["Sr No"], axis=1)
    .rename({"index": "Sr No"}, axis=1)
)
final["Installed Date"] = pd.to_datetime(final["Installed Date"],format='%d-%m-%Y')

final["year"] = final["Installed Date"].dt.strftime("%Y")
final["month"] = final["Installed Date"].dt.strftime("%b")
final["dayMonth"] = final["Installed Date"].dt.strftime("%d-%b")
yearWise = (
    final[final["year"] != datetime.now().strftime("%Y")]
    .groupby(["year"])
    .count()
    .reset_index()
)
month_wise = (
    final[
        (final["year"] == datetime.now().strftime("%Y"))
        & (final["month"] != datetime.now().strftime("%b"))
    ]
    .groupby(["month"])
    .count()
    .reset_index()
)
Day_Wise = (
    final[
        (final["year"] == datetime.now().strftime("%Y"))
        & (final["month"] == datetime.now().strftime("%b"))
    ]
    .groupby(["dayMonth"])
    .count()
    .reset_index()
)


yearWise = yearWise.drop(
    [
        "Base shared date",
        "TID",
        "State",
        "pin",
        "InstallationStatus",
        "Installed Date",
        "Remarks",
        "month",
        "dayMonth",
        "Old Tid"
    ],
    axis=1,
).rename({"Sr No": "Count", "year": "Years"}, axis=1)
yearWise["Months"] = ""
month_wise["Years"] = datetime.now().strftime("%Y")
month_wise = month_wise.drop(
    [
        "Base shared date",
        "TID",
        "State",
        "pin",
        "InstallationStatus",
        "Installed Date",
        "Remarks",
        "year",
        "dayMonth",
        "Old Tid"
    ],
    axis=1,
).rename({"Sr No": "Count", "month": "Months"}, axis=1)
months = pd.DataFrame(
    {
        "Months": [
            "Jan",
            "Feb",
            "Mar",
            "Apr",
            "May",
            "Jun",
            "Jul",
            "Aug",
            "Sep",
            "Oct",
            "Nov",
            "Dec",
        ],
        "Order": [x for x in range(1, 13)],
    },
    columns=["Months", "Order"],
)
month_wise = pd.merge(
    left=month_wise, right=months, left_on=["Months"], right_on=["Months"], how="inner"
)
month_wise = (
    month_wise.sort_values("Order").drop("Order", axis=1).reset_index(drop=True)
)


Day_Wise["Years"] = datetime.now().strftime("%Y")
Day_Wise = Day_Wise.drop(
    [
        "Base shared date",
        "TID",
        "State",
        "pin",
        "InstallationStatus",
        "Installed Date",
        "Remarks",
        "year",
        "month",
        "Old Tid"
    ],
    axis=1,
).rename({"Sr No": "Count", "dayMonth": "Months"}, axis=1)
LTD = (
    pd.concat([yearWise,month_wise,Day_Wise],ignore_index=True)
)
LTD = LTD.iloc[:,[0,2,1]]

final["Installed Date"] = final["Installed Date"].dt.strftime("%d-%m-%Y")
final.drop(["year","month","dayMonth"],axis=1,inplace=True)
final["Sr No"] = final["Sr No"]+1
grand_total = pd.DataFrame({
    "Years" : "Grand Total",
    "Months" : "  ",
    "Count" : LTD.Count.sum()
},index=[0])
LTD = pd.concat([LTD,grand_total]).reset_index(drop=True)
final["State"] = final["State"].str.upper()
State = (
    final
    .groupby(["State"],sort=True)
    .agg({"TID":"count"})
    .reset_index()
)
State = State.sort_values("TID",ascending=False).reset_index(drop=True)
Total = pd.DataFrame({"State":"Grand Total",'TID' : State.TID.sum()},index=[0])
State = pd.concat([State,Total],ignore_index=True)

with pd.ExcelWriter(output_file) as Writer:
    final_to_write = final.astype({"Sr No":'int32',"InstallationStatus":'category',"Remarks":"category","Base shared date":"category","TID":"int32"})
    final_to_write.to_excel(Writer,sheet_name="Data",index=False)
    LTD.to_excel(Writer,sheet_name="LTD Installation",index=False)
    State.to_excel(Writer,sheet_name="State",index=False)
with pd.ExcelWriter(input_file) as Writer:
    final.to_excel(Writer,sheet_name="Data",index=False)

fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(output_file)

fix_worksheet.process_all_worksheets()
		

print("File Creation is Successfull")
exit()
