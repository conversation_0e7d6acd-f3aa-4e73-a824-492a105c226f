import os 
import sys

from datetime import date
from datetime import timedelta

out_dir="/home/<USER>/Reports/NI_Dubai_Weekly/"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%d%m%Y")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
output_file = os.path.join(out_dir,yesterday_file_name,"MAI_Dubai_Weekly.xlsx")

def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"


config = {
    "host": "************",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234",
    "database": "sfn_transaction",
    }

Txn_Data = """SELECT
    t.id AS ID,
    CONCAT(' ', DATE(t.transactionTime)) AS 'Transaction Created Date',
    t.clientId AS User,
    t.userName AS Username,
    CASE
        WHEN t.typeID = 1 AND t.isTip IS NOT NULL THEN 'Tip Adjusted'
        ELSE t.transactionTypeName
    END AS Type,
    t.modeName AS Mode,
    CASE
        WHEN t.typeID = 15 AND t.emiType = 'debit' THEN t.additionalAmount / POWER(10, a.decimalAmountLength)
        ELSE t.amount / POWER(10, a.decimalAmountLength)
    END AS Amount,
    t.authCode AS 'Auth Code',
    t.maskedCardNumber AS Card,
    t.drCrCode AS 'Card Type',
    t.cardTypeName AS 'Brand Type',
    CONCAT(' ', t.rrn) AS RRN,
    t.invoiceNumber AS 'Invoice#',
    t.serialNumber AS 'Device Serial',
    CASE
        WHEN t.settlementStatusName IS NULL THEN 'UNSETTLED'
        ELSE t.settlementStatusName
    END AS Status,
    CONCAT(' ', t.settlementTxnTime) AS 'Settled On',
    CASE
        WHEN
            t.tempMerchantCode IS NOT NULL
                AND t.tempMerchantCode != 0
        THEN
            CASE
                WHEN t.tempMerchantCode != t.merchantCode THEN CONCAT(' ', t.tempMerchantCode)
                ELSE CONCAT(' ', t.merchantCode)
            END
        WHEN t.merchantCode IS NULL THEN CONCAT(' ', mmut.merchantCode)
        ELSE CONCAT(' ', t.merchantCode)
    END AS MID,
    CASE
        WHEN
            t.tempTID IS NOT NULL AND t.tempTID != 0
        THEN
            CASE
                WHEN t.tempTID != t.terminalID THEN t.tempTID
                ELSE t.terminalID
            END
        WHEN t.terminalID IS NULL THEN mmut.terminalID
        ELSE t.terminalID
    END AS TID,
    t.batchNumber AS Batch,
    t.billNumber AS 'Reference#',
    CASE
        WHEN
            t.statusID NOT IN (2 , 7, 10)
                AND t.typeID NOT IN (3 , 10, 11, 16, 7)
        THEN
            CASE
                WHEN
                    t.tgID IS NOT NULL
                        AND tdc.description != ''
                THEN
                    tdc.description
                WHEN t.tgID IS NULL AND tr.description != '' THEN tr.description
            END
        ELSE NULL
    END AS 'Additional Information',
    t.latitude AS Latitude,
    t.longitude AS Longitude,
    t.cardHolderName AS Payer,
    CONCAT(' ', TIME(t.transactionTime)) AS 'Transaction Created Time',
    t.txnStatusName AS 'Transaction Status',
    t.merchantName AS 'ME Name',
    t.acquirerName AS Acquirer,
    IFNULL(t.responseCode, '91') AS 'Response Code',
    t.currencyCode AS Currency,
    t.deviceID AS 'Device No',
    t.referenceTxnID AS 'Reference Txn Id',
    t.tgName AS tg

FROM
    transactions t
        LEFT JOIN
    acquirer a ON t.acquirerName = a.name
        LEFT JOIN
    (SELECT
        id, enterpriseId, status
    FROM
        merchant
    WHERE
        status = 'A') m ON t.merchantID = m.id
        LEFT JOIN
    emiData emi ON t.id = emi.transactionId
        LEFT JOIN
    transaction_status ts ON t.statusID = ts.id
        LEFT JOIN
    transaction_type tt ON tt.id = t.typeID
        LEFT JOIN
    txn_decline_codes tdc ON t.tgId = tdc.tgID
        AND t.responseCode = tdc.responseCode
        LEFT JOIN
    txn_decline_codes tr ON t.responseCode = tr.responseCode
        AND tr.tgID IS NULL
        LEFT JOIN
    (SELECT
        mmut.id, mmut.userName, mmut.terminalID, mdat.merchantCode
    FROM
        mapping_merchant_user_terminal mmut
    INNER JOIN mapping_division_acquirer_tg mdat ON mdat.id = mmut.divAcqTgId
    WHERE
        mmut.status = 'A'
    GROUP BY mmut.userName
    HAVING mmut.id = MIN(mmut.id)) mmut ON mmut.id = t.divisionID
WHERE
    t.transactionTime >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        AND t.transactionTime < CURDATE()
AND t.typeID in ( 1,2)
       And t.statusID in (2,7) AND t.merchantName IN ('MAI DUBAI LLC');
    """
