from datetime import datetime
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import Txn_Data,config,output_file,format_int_with_commas
import XLSXAutoFitColumns

pd.options.display.float_format = '{:,.2f}'.format

NI_Transaction = data_retrival(Txn_Data)

with pd.ExcelWriter(output_file) as writer:
    NI_Transaction.to_excel(writer,sheet_name='Data',index=False)

fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(output_file)
fix_worksheet.process_all_worksheets()
print("File Creation is Successfull")
exit()
