from datetime import datetime,timed<PERSON>ta
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import query,config,PayByLink_query,input_file,output_file,format_int_with_commas
import XLSXAutoFitColumns

data = data_retrival(query)
PayByLinkData = data_retrival(PayByLink_query)
data = pd.concat([data,PayByLinkData]).reset_index(drop=True)
test_tid = pd.read_csv("/home/<USER>/Reports/Scripts/AllTransactions/Test_TID_List.csv")
data = data[~data['terminalID'].isin(test_tid['TID'])].reset_index(drop=True)
data_grouped = (data
 .groupby(['transactionTypeName','acquirerName'])
 .aggregate({'id':'count','amount':'sum'}).reset_index()
)

data_grouped.replace({"Cash Withdrawal":"MATM"},inplace=True)
acquirerTxnTypeWise = pd.read_csv('/home/<USER>/Reports/Scripts/AllTransactions/AcquirerListTXNWise.csv')
Result = pd.merge(acquirerTxnTypeWise, data_grouped,  how='left', left_on=["transactionTypeName"],
                  right_on = ['transactionTypeName'])

Result.fillna(0,inplace=True)
TxnTypeList = list(acquirerTxnTypeWise.transactionTypeName.unique())
Result.sort_values(['TransactionTypeOrder','id','amount'],ascending=True,inplace=True)
if (datetime.now() - timedelta(1)).strftime("%d") != '01':
    if os.path.exists("/home/<USER>/Reports/Scripts/AllTransactions/History_AllTransaction.xlsx"):
        History_data = pd.read_excel("/home/<USER>/Reports/Scripts/AllTransactions/History_AllTransaction.xlsx",sheet_name='Sheet1')
        final = pd.concat([History_data,Result]).reset_index(drop=True)
    else:
        final = Result.copy()
else:
    final = Result.copy()

with pd.ExcelWriter("/home/<USER>/Reports/Scripts/AllTransactions/History_AllTransaction.xlsx") as Historywriter:
    final.to_excel(Historywriter,'Sheet1',index=False)

with pd.ExcelWriter(output_file) as writer:
    for type in TxnTypeList:
        if type == 'Grand Total':
            finalToWrite = pd.DataFrame({
            "Acquirer" : "Grand Total",
            "Volume"  : int(final[final["transactionTypeName"]!='MATM']["id"].sum()),
            "Value" : final[final["transactionTypeName"]!='MATM']["amount"].sum()
        },index=[0])
            
        else:
            finalToWrite = (final
                    .loc[final["transactionTypeName"]==type]
                    .drop(["transactionTypeName"],axis=1)
                    .astype({"acquirerName":"object","id":"int","amount":"float"})
                    .rename({"acquirerName":"Acquirer","id":"Volume","amount":"Value"},axis=1)
                    .reset_index(drop=True)
                    )
            finalToWrite = finalToWrite.groupby(['Acquirer']).aggregate({'Volume':'sum','Value':'sum'}).reset_index().sort_values(['Volume','Value'],ascending=False)
            grand_total_dict = {
                "Acquirer" : "Grand Total",
                "Volume"  : finalToWrite["Volume"].sum(),
                "Value" : finalToWrite["Value"].sum()
            }
            grand_total = pd.DataFrame(grand_total_dict,index=[0])
            finalToWrite = pd.concat([finalToWrite,grand_total]).reset_index(drop=True)
        finalToWrite["Value"] = finalToWrite.Value.map(format_int_with_commas)
        finalToWrite["Volume"] = finalToWrite.Volume.map(format_int_with_commas)
        finalToWrite.to_excel(writer,type,index=False)

print("File Creation is Successfull")
exit()
