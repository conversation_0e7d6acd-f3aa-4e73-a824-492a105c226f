#!/bin/bash

dir=$2
cd $dir
tabcmd login -s http://************:8080 -u tabadmin -p $1
foldername=$(date -d "yesterday" '+%Y%m%d')
echo $foldername
[ ! -d "$foldername" ] && mkdir -p "$foldername"
cd $foldername
tabcmd get "/views/AllTransactionDaily/AcquirerWiseTransactionSummary.csv"
#tabcmd get "/views/AllTransactionDaily/SoundBox.csv"

tabcmd logout

cd ..

reportName="All_Transactions_Daily_Summary"

python3 /home/<USER>/Reports/Scripts/AllTransactions/main.py $reportName $foldername $dir
filename="All_Transactions.xlsx"

python3 /home/<USER>/Reports/Scripts/AllTransactions/mail.py $foldername $filename $dir

cd $dir

cd $foldername
rm -rf *.csv
rm -rf *.png
echo "Completed"
