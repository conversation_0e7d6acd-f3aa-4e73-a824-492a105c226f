import os 
import sys
from datetime import date,datetime
from datetime import timedelta

out_dir="/home/<USER>/Reports/MPGSNI_Monthly/"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%d%m%Y")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
output_file_name = f"TOP_KPI_Report_MPGSNI_{(datetime.now() - timedelta(5)).strftime('%b_%Y')}.xlsx"
output_file = os.path.join(out_dir,yesterday_file_name,output_file_name)
input_file = "/home/<USER>/Reports/Scripts/MPGSNI_Monthly/HistoricalFile_MPGSNIMonthly.xlsx"
def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

config = {
    "host": "************",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234",
    "database": "sfn_transaction",
    }

query = '''
SELECT
    t.id AS ID,
    CONCAT(' ', DATE(t.transactionTime)) AS 'Transaction Created Date',
    CASE
        WHEN t.typeID = 15 AND t.emiType = 'debit' THEN t.additionalAmount / POWER(10, a.decimalAmountLength)
        ELSE t.amount / POWER(10, a.decimalAmountLength)
    END AS Amount,
    t.acquirerName AS Acquirer,
    m.mcc,
    t.statusID,
    t.typeID,
    t.cardTypeName AS 'Brand Type',
    t.isVoided,
    t.referenceTxnID
FROM
    transactions t
        LEFT JOIN
    acquirer a ON t.acquirerName = a.name
        LEFT JOIN
    (SELECT
        id, enterpriseId, mcc, status
    FROM
        merchant
    WHERE
        status = 'A') m ON t.merchantID = m.id
WHERE
     t.transactionTime >= DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m-01')
        AND t.transactionTime < DATE_FORMAT(NOW() ,'%Y-%m-01')
        AND CASE
        WHEN
            typeID = 1 AND isVoided = 1
                AND t.id = (SELECT
                    t1.referenceTxnID
                FROM
                    transactions t1
                WHERE
                    t1.typeID = 2
                        AND t.id = t1.referenceTxnID)
        THEN
            0
        ELSE 1
    END = 1
        AND t.acqId = '62'
        AND t.typeID = 1;
'''
