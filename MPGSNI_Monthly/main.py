from datetime import datetime,timedelta
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import query,input_file,config,output_file,format_int_with_commas
import XLSXAutoFitColumns
import shutil
pd.options.display.float_format = '{:,.2f}'.format

data = data_retrival(query)
Merchant_Related_Metrics = pd.read_excel(input_file,sheet_name="Merchant Related Metrics")
Merchant_Related_Metrics_Dummy = pd.DataFrame(
    {"Merchant Related Metrics":['Total merchants onboarded till date','New merchants onboarded','New onboarded merchants that were not accepting cards','Active Merchants (processing at least a transaction per week)'],
     (datetime.now() - timedelta(5)).strftime("%b-%y") : [0,0,0,0]
    }
)
Merchant_Related_Metrics = pd.merge(left=Merchant_Related_Metrics,right= Merchant_Related_Metrics_Dummy,on="Merchant Related Metrics",how='inner')

IC_MPOS_Devices = pd.read_excel(input_file,sheet_name="IC-MPOS Devices")
IC_MPOS_Devices_Dummy = pd.DataFrame(
    {"IC-MPOS Devices":['IC-MPOS Devices Deployed ( Terminal ID created)','IC-MPOS Devices Active','Total number of IC-MPOS Devices Registered ( Terminal ID created )','Total unique number of IC-MPOS Devices Active','Total number of Apps Downloaded '],
     (datetime.now() - timedelta(5)).strftime("%b-%y") : [0,0,0,0,0]
    }
)
IC_MPOS_Devices = pd.merge(left=IC_MPOS_Devices,right= IC_MPOS_Devices_Dummy,on="IC-MPOS Devices",how='inner')

Monthly_Transaction_information = pd.read_excel(input_file,sheet_name="Monthly-Transaction information")
Monthly_Transaction_information_Dummy = pd.DataFrame(
    {"Transaction information (all card brands)":['Total Transaction Volume (in USD)'
                                                  ,'Total Transaction Count - Successful'
                                                  ,'Number of Declined Transactions'
                                                  ,'Number of Failed Transactions'
                                                  ,'Number of Digital Wallet Transactions'
                                                  ,'Number of Card Transactions (Credit and Debit)'
                                                  ,'Number of cross-border transactions'
                                                  ,'Number of transactions Below CVM'
                                                  ,'Number of transactions Above CVM'
                                                 ],
     (datetime.now() - timedelta(10)).strftime("%b-%y") : [data[data['statusID'].isin([2,7,10])]["Amount"].sum()/3.68
                                                          ,data[data['statusID'].isin([2,7,10])]['ID'].nunique()
                                                          ,data[~data['statusID'].isin([2,7,10])]['ID'].nunique()
                                                          ,0
                                                          ,0
                                                          ,data[data['statusID'].isin([2,7,10])]['ID'].nunique()
                                                          ,0
                                                          ,data[(data['statusID'].isin([2,7,10])) & (data["Amount"] <= 500)]['ID'].nunique()
                                                          ,data[(data['statusID'].isin([2,7,10])) & (data["Amount"] > 500)]['ID'].nunique()
                                                         ]
    }
)
Monthly_Transaction_information = pd.merge(left=Monthly_Transaction_information,right= Monthly_Transaction_information_Dummy,on="Transaction information (all card brands)",how='inner')
MasterCardByMcc = (data[(data['statusID'].isin([2,7,10])) & (data["Brand Type"] == 'MASTERCARD')] 
 .groupby('mcc')
 .aggregate({
     "ID":"nunique",
     "Amount":"sum"
 })
 .reset_index()
 .rename({
     'mcc' : 'MCC Code',
     'ID' : 'Number of transactions',
     'Amount' : 'Total transaction value (USD)'
 },axis=1)
 .sort_values(by='MCC Code')
)
VisaByMcc = (data[(data['statusID'].isin([2,7,10])) & (data["Brand Type"] == 'VISA')]
    .groupby('mcc')
    .aggregate({
     "ID":"nunique",
     "Amount":"sum"
 })
 .reset_index()
 .rename({
     'mcc' : 'MCC Code',
     'ID' : 'Number of transactions',
     'Amount' : 'Total transaction value (USD)'
 },axis=1)
 .sort_values(by='MCC Code')
)
VisaByMcc['Total transaction value (USD)'] = VisaByMcc.loc[:,'Total transaction value (USD)']/3.68
MasterCardByMcc['Total transaction value (USD)'] = MasterCardByMcc.loc[:,'Total transaction value (USD)']/3.68
with pd.ExcelWriter(input_file,engine='openpyxl',mode='a',if_sheet_exists='replace') as writer:
    Merchant_Related_Metrics.to_excel(writer,sheet_name='Merchant Related Metrics',index=False)
    IC_MPOS_Devices.to_excel(writer,sheet_name="IC-MPOS Devices",index=False)
    Monthly_Transaction_information.to_excel(writer,sheet_name="Monthly-Transaction information",index=False)
    MasterCardByMcc.to_excel(writer,sheet_name="Txn by MCC- MasterCard",index=False)
    VisaByMcc.to_excel(writer,sheet_name="Txn by MCC- Visa",index=False)

shutil.copy(input_file,output_file)

fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(output_file)
fix_worksheet.process_all_worksheets()
print("File Creation is Successfull")
exit()
