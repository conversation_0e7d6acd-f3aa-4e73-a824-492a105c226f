from datetime import datetime
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os

reportName = argv[1]
foldername = argv[2]
dir = argv[3]
os.chdir(os.path.join(dir,foldername))
config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}

# Set up logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
formatter = logging.Formatter(
    "%(asctime)s - %(name)s - %(levelname)s - %(message)s")

# Log to console
handler = logging.StreamHandler()
handler.setFormatter(formatter)
logger.addHandler(handler)

# Also log to a file
file_handler = logging.FileHandler("cpy-errors.log")
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)

def connect_to_mysql(config, attempts=3, delay=2):
    attempt = 1
    # Implement a reconnection routine
    while attempt < attempts + 1:
        try:
            return mysql.connector.connect(**config)
        except (mysql.connector.Error, IOError) as err:
            if (attempts is attempt):
                # Attempts to reconnect failed; returning None
                logger.info(
                    "Failed to connect, exiting without a connection: %s", err)
                return None
            logger.info(
                "Connection failed: %s. Retrying (%d/%d)...",
                err,
                attempt,
                attempts-1,
            )
            # progressive reconnect delay
            time.sleep(delay ** attempt)
            attempt += 1
    return None

query = """
     SELECT
     	m.id as POSID,
    mdat.merchantCode as MID,
    mmut.terminalId as TerminalID,
    CONCAT(' ',mmut.recordCreated) as recordCreated
FROM
    merchant m
        INNER JOIN
    mapping_division_acquirer_tg mdat ON mdat.divisionId = m.id
        INNER JOIN
    mapping_merchant_user_terminal mmut ON mmut.divAcqTgId = mdat.id
        AND mmut.divisionId = m.id
WHERE
    mmut.programId = 95
        AND mmut.recordCreated < CURRENT_DATE();
"""

def data_retrival(query):
    cnx = connect_to_mysql(config, attempts=3)
    if cnx and cnx.is_connected():
        with cnx.cursor() as cursor:
            result = cursor.execute(query)
            rows = cursor.fetchall()
            df = pd.DataFrame(rows, columns=cursor.column_names)
        cnx.close()
        return df
    else:
        return None

data = data_retrival(query)

with pd.ExcelWriter(f"InstaOnboarding.xlsx") as writer:
        data.to_excel(writer,index=False)

print("Process Completed")
