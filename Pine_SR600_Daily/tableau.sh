#!/bin/bash

# Script Name: HDFC File Split
# Description: Split HDFC file and upload to the NAS ************* Server
# Author: <PERSON><PERSON>
# Created: 2023-07-03
# Last Modified: 2023-07-03 by <PERSON>chin


base_directory="/home/<USER>/Reports/TMS_Report_Daily"
previous_date=$(date -d "yesterday" +"%d%m%Y")
current_date=$(date +"%Y_%m_%d")
#source_directory="$base_directory/$previous_date"
remote_path="/MVISA_MOSAMBEE/"
#cd "$source_directory"
sftp.host=efg.hdfcbank.com
sftp.username=CMOSAMBEE
password="M0$5n#dmin43290219"
sftp.port=22
sftp.dir.location=/MVISA_MOSAMBEE/
###Uploading the file to the NAS server *************####
sshpass -p $<NAME_EMAIL>:22 "mkdir -p $remote_path/$previous_date"
sshpass -p $password scp $base_directory/$previous_date/*  <EMAIL>:22:$remote_path/$previous_date
sshpass -p $password ssh CMOSAMBEE@************** "chmod 777 $remote_path/$previous_date/*"
