import os 
import sys
import datetime as dt
from datetime import timedelta,datetime,date

out_dir="/home/<USER>/Reports/Pine_SR600_Daily/"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%d%m%Y")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
output_FileName = f'TEST_Pine_SR600_Card_transaction_list_{(datetime.now() - timedelta(1)).strftime("%d_%m_%Y")}.csv'

output = os.path.join(out_dir,yesterday_file_name,output_FileName)

def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

def date_serial_number(serial_number: int):
    """
    Convert an Excel serial number to a Python datetime object
    :param serial_number: the date serial number
    :return: a datetime object
    """
    # Excel stores dates as "number of days since 1900"
    delta = dt.datetime(1899, 12, 30) + dt.timedelta(days=serial_number)
    return delta

#in_dir = sys.argv[1]
#out_dir = sys.argv[2]
config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}
query = """
SELECT 
    t.id AS 'Transaction ID',
    t.transactionTime AS 'Transaction Time',
    t.merchantCode AS 'MID',
    t.terminalID AS 'TID',
    t.rrn AS 'RRN',
    t.transactionTypeName AS 'Transaction Type Name',
    t.caUserId,
    round(t.amount / 100,2) as 'Amount'
FROM
    transactions t force index(transactions_trantime)
        left JOIN
    transaction_type tt ON t.typeID = tt.id 
WHERE
    t.transactionTime > DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        AND t.transactionTime <= CURDATE()
        AND t.acqId = 72
        AND t.statusID IN (2 , 7)
        AND t.caUserId = 2
        AND tt.forCard = 1
        ;
"""
