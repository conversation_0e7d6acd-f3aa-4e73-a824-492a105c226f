import os 
import sys
from datetime import date,datetime
from datetime import timedelta

out_dir="/home/<USER>/Reports/NI_Daily_Declined_Transaction/"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%d%m%Y")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
output_file_name = f"NI_Approve_Decline_TXN_Summary_for_{(datetime.now() - timedelta(1)).strftime('%b_%Y')}.xlsx"
output_file = os.path.join(out_dir,yesterday_file_name,output_file_name)

txn_output_file_name = f"Transaction_Data_for_Dated_{(datetime.now() - timedelta(1)).strftime('%d_%b_%Y')}.csv"
txn_output_file = os.path.join(out_dir,yesterday_file_name,txn_output_file_name)

def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

config = {
    "host": "************",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234",
    "database": "sfn_transaction",
    }

query = '''
SELECT
    t.id,
    t.terminalID,
    t.transactionTime,
    t.email AS Email,
    t.clientId AS user,
    t.userName,
    t.transactionTypeName,
    t.amount/100,
    t.tipAmount,
    CASE when  t.statusID IN (2,7) then "Approved"
    else  t.txnStatusName end as txnStatusName,
    t.authCode,
    t.maskedCardNumber,
    t.cardTypeName,
    t.modeName,
    t.rrn,
    t.invoiceNumber,
    Case when t.responseDescription is null then 'Blank'
    else t.responseDescription end as responseDescription ,
    t.settlementStatusID,
    t.settlementStatusName,
    t.settlementTxnTime,
    CASE
        WHEN t.merchantCode IS NULL THEN m.merchantCode
        ELSE t.merchantCode
    END AS merchantCode,
    t.batchNumber,
    t.latitude,
    t.longitude,
    t.cardHolderName,
    t.billNumber,
    t.securityToken AS userL,
    t.merchantName,
    t.acquirerName,
    t.drCrCode,
    t.aid,
    t.tvr,
    t.tsi,
    t.acquirerName,
    t.currencyCode,
    t.bankRefId,
    t.terminalType,
    t.serialNumber,
    t.tgName,
    t.referenceTxnID,
    t.tgTransactionId,
    t.tenureUnit,
    t.dbaname,
    t.processingMerchantCode,
    t.txnResponseTime,
    t.forSettlement,
    t.responseCode,
    DATE_FORMAT(t.transactionTime,"%d-%m-%Y") as Date_value
FROM
    transactions t
    left join 
    mapping_division_acquirer_tg m on t.merchantID = m.divisionID
WHERE
    t.acqid IN (50 , 51, 62, 63, 64, 65, 67)
        AND t.statusID IN (2 , 3, 7)
        AND t.transactionTime >= DATE_FORMAT(CURDATE() - INTERVAL 1 day, '%Y-%m-01')
        AND t.transactionTime <  CURDATE()
ORDER BY t.id DESC;
'''
