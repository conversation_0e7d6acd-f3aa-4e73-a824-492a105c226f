from datetime import datetime,timedelta
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import query,config,output_file,format_int_with_commas,txn_output_file
import XLSXAutoFitColumns
import shutil
pd.options.display.float_format = '{:,.2f}'.format

txn_initial = data_retrival(query)
approve_declined_Count = (txn_initial
                        .pivot_table(index = ["txnStatusName"],
                                     values=['id'],
                                     columns=["Date_value"],
                                     aggfunc='nunique',
                                     margins=True,
                                     margins_name='Grand Total',
                                     fill_value=0)
                        .reset_index()
                        .droplevel(axis=1,level=0)
                        .rename({'':"Status"},axis=1)
                       )
decline_reason_summary = (txn_initial[txn_initial["txnStatusName"] == 'Declined']
                        .pivot_table(index = ["responseDescription"],
                                     values=['id'],
                                     columns=["Date_value"],
                                     aggfunc='nunique',
                                     margins=True,
                                     margins_name='Grand Total',
                                     fill_value=0)
                        .reset_index()
                        .droplevel(axis=1,level=0)
                        .rename({'':"Decline Reason"},axis=1)
                        .sort_values('Grand Total',ascending=True)
                         .reset_index(drop=True)
                       )
merchant_summary = (txn_initial
                        .pivot_table(index = ["merchantName","merchantCode"],
                                     values=['id'],
                                     columns=["txnStatusName"],
                                     aggfunc='nunique',
                                     fill_value=0)
                        .reset_index()
                       )
merchant_summary.columns = pd.MultiIndex.from_tuples(merchant_summary.set_axis(merchant_summary.columns.values, axis=1).rename(columns={
                            ('merchantName',         '') : ("","Merchant Name"),
                            ('merchantCode',         '') : ("","Merchant ID"),
                            (          'id', 'Approved') : ("","Approved"),
                            (          'id', 'Declined') : ("","Declined")})).droplevel(level=0)
merchant_summary.sort_values(["Merchant Name","Approved","Declined"],ascending=True,inplace=True)
merchant_summary["Grand Total"] = merchant_summary["Approved"]+merchant_summary["Declined"]
merchant_summary_summary  = pd.DataFrame(
    {
        "Merchant Name" : "Grand Total",
        "Merchant ID" : " ",
        "Approved" : merchant_summary["Approved"].sum(),
        "Declined" : merchant_summary["Declined"].sum(),
        "Grand Total" : merchant_summary["Grand Total"].sum()
    },index=[0]
)

merchant_summary = pd.concat([merchant_summary,merchant_summary_summary]).reset_index(drop=True)
txn = txn_initial[txn_initial['Date_value']==(datetime.now() - timedelta(1)).strftime("%d-%m-%Y")].drop(["Date_value"],axis=1)
with pd.ExcelWriter(output_file) as writer:
    approve_declined_Count.to_excel(writer,sheet_name="Approve Decline Summary",index=False)
    decline_reason_summary.to_excel(writer,sheet_name="Decline Reason Summary",index=False)
    merchant_summary.to_excel(writer,sheet_name="Merchant Wise Summary",index=False)
txn.to_csv(txn_output_file,index=False)


fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(output_file)
fix_worksheet.process_all_worksheets()

print("File Creation is Successfull")
exit()
