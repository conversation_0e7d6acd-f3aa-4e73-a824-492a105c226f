import os
import sys
import smtplib
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from email.mime.image import MIMEImage
from enviroment import From,server_name,to,Port,Password,Username
from datetime import datetime, timedelta
import glob
import pandas as pd
from DataQuery import output_file,txn_output_file
#print(os.getcwd())

msg = MIMEMultipart()
msg['From'] = From
msg["To"] = ','.join(to)
#msg["Cc"] = to
msg['Subject'] = f"SoftPOS Sale/Declined Transactions- {(datetime.now() - timedelta(2)).strftime('%b-%Y')}"

htmlEmail = f"""
<p> Dear Sir/Madam, <br/><br/>
    Please find the attached Transaction details and summary of Sale/Declined Transaction with merchant ID for the month {(datetime.now() - timedelta(2)).strftime('%b-%Y')}.<br/>
</p>
"""

#msg.attach(MIMEText(htmlEmail, 'html'))
f = output_file
t = txn_output_file
#f = os.path.join(os.getcwd(),folder_name,f"{folder_name} {file_name}")
#html tables data
    
htmlEmail2 = """
<p> Please contact Mosambee Support Team (<EMAIL>) directly if you have any questions. <br/>
    Thank you! <br/>
    Best Regards, <br/>
    Mosambee Support Team </p>
<br/>
<font color="red">Please do not reply to this email as it is auto-generated. </font>
"""
htmlEmail = "<br/>".join([htmlEmail,htmlEmail2])
msg.attach(MIMEText(htmlEmail, 'html'))

with open(f, "rb") as attached_file:
    part = MIMEApplication(
            attached_file.read(),
            Name=os.path.basename(f)
            )
# After the file is closed
part['Content-Disposition'] = 'attachment; filename="%s"' % os.path.basename(f)
msg.attach(part)

with open(t, "rb") as attached_file:
    part = MIMEApplication(
            attached_file.read(),
            Name=os.path.basename(t)
            )
# After the file is closed
part['Content-Disposition'] = 'attachment; filename="%s"' % os.path.basename(t)
msg.attach(part)

server = smtplib.SMTP(server_name, Port)
server.starttls()
server.login(Username,Password)
text = msg.as_string()
server.sendmail(From, to, text)
server.quit()

print("Email are sent successfully!")

