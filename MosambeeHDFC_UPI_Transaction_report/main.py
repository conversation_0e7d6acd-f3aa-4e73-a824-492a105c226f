from datetime import datetime,timed<PERSON><PERSON>
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import config,format_int_with_commas,date_serial_number,TransactingQuery,Transacting_output
import XLSXAutoFitColumns

pd.options.display.float_format = '{:,.2f}'.format

txnData = data_retrival(TransactingQuery)
with pd.ExcelWriter(Transacting_output) as writer:
    print(Transacting_output)
    txnData.to_excel(writer,index=False)
fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(Transacting_output)
fix_worksheet.process_all_worksheets()

#fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(Non_Transacting_output)
#fix_worksheet.process_all_worksheets()
print("File Creation is Successfull")
exit()
