import os 
import sys
import datetime as dt
from datetime import timedelta,datetime,date

out_dir="/home/<USER>/Reports/MosambeeHDFC_UPI_Transaction_report"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = today.strftime("%Y%m%d")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
Transacting_output_FileName = f'MosambeeHDFC_UPI_Transaction_report_{datetime.now().strftime("%Y%m%d%H%M00")}.xlsx'

Transacting_output = os.path.join(out_dir,yesterday_file_name,Transacting_output_FileName)

def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

def date_serial_number(serial_number: int):
    """
    Convert an Excel serial number to a Python datetime object
    :param serial_number: the date serial number
    :return: a datetime object
    """
    # Excel stores dates as "number of days since 1900"
    delta = dt.datetime(1899, 12, 30) + dt.timedelta(days=serial_number)
    return delta

#in_dir = sys.argv[1]
#out_dir = sys.argv[2]
config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}

def get_time_range(now=None):
    if not now:
        now = datetime.now()

    current_time = now.time()

    # Define your time brackets
    bracket1_start = datetime.strptime("08:30:00", "%H:%M:%S").time()
    bracket1_end   = datetime.strptime("13:14:59", "%H:%M:%S").time()

    bracket2_start = datetime.strptime("13:15:00", "%H:%M:%S").time()
    bracket2_end   = datetime.strptime("16:14:59", "%H:%M:%S").time()

    if bracket1_start <= current_time <= bracket1_end:
        start_time = datetime.combine(now.date() - timedelta(days=1), datetime.strptime("16:00:00", "%H:%M:%S").time())
        end_time = datetime.combine(now.date(), datetime.strptime("07:59:59", "%H:%M:%S").time())
    elif bracket2_start <= current_time <= bracket2_end:
        start_time = datetime.combine(now.date(), datetime.strptime("08:00:00", "%H:%M:%S").time())
        end_time = datetime.combine(now.date(), datetime.strptime("12:59:59", "%H:%M:%S").time())
    else:
        start_time = datetime.combine(now.date(), datetime.strptime("13:00:00", "%H:%M:%S").time())
        end_time = datetime.combine(now.date(), datetime.strptime("15:59:59", "%H:%M:%S").time())

    return start_time, end_time

start_time, end_time = get_time_range()

TransactingQuery = """
SELECT 
    t.id AS ID,
    DATE(t.transactionTime) AS 'Transaction Created Date',
    t.clientId AS User,
    t.typeID,
    t.userName AS Username,
    CASE
        WHEN t.typeID = 1 AND t.isTip IS NOT NULL THEN 'Tip Adjusted'
        ELSE t.transactionTypeName
    END AS Type,
    t.modeName AS Mode,
    CASE
        WHEN t.typeID = 15 AND t.emiType = 'debit' THEN t.additionalAmount / 100
        ELSE t.amount / 100
    END AS Amount,
    t.authCode AS 'Auth Code',
    t.maskedCardNumber AS Card,
    t.drCrCode AS 'Card Type',
    t.cardTypeName AS 'Brand Type',
    t.rrn AS RRN,
    t.invoiceNumber AS 'Invoice#',
    t.serialNumber AS 'Device Serial',
    COALESCE(t.settlementStatusName, 'UNSETTLED') AS Status,
    t.settlementTxnTime AS 'Settled On',
    COALESCE(NULLIF(t.tempMerchantCode, 0), t.merchantCode, mdat.merchantCode) AS MID,
    COALESCE(NULLIF(t.tempTID, 0), t.terminalID, mmut.terminalID) AS TID,
    t.batchNumber AS Batch,
    t.billNumber AS 'Reference#',
    COALESCE(
        CASE
            WHEN t.statusID NOT IN (2, 7, 10)
              AND t.typeID NOT IN (3, 10, 11, 16, 7)
            THEN COALESCE(NULLIF(tdc.description, ''), NULLIF(tr.description, ''))
        END,
        NULL
    ) AS 'Additional Information',
    t.latitude AS Latitude,
    t.longitude AS Longitude,
    t.cardHolderName AS Payer,
    t.securityToken AS 'TID Location',
    TIME(t.transactionTime) AS 'Transaction Created Time',
    t.txnStatusName AS 'Transaction Status',
    t.merchantName AS 'ME Name',
    t.acquirerName AS Acquirer,
    COALESCE(t.responseCode, '91') AS 'Response Code',
    t.currencyCode AS Currency,
    t.deviceID AS 'Device No',
    t.referenceTxnID AS 'Reference Txn Id',
    t.tgName AS tg,
    t.dbaName AS 'DBA Name',
    t.refundStatus AS 'Refund Status',
    DATE(t.txnResponseTime) AS 'Transaction Response Date',
    TIME(t.txnResponseTime) AS 'Transaction Response Time',
    m.enterpriseId,
    CASE
        WHEN t.settlementStatusID != 4 THEN
            CASE t.forSettlement
                WHEN 1 THEN 'Auto Settlement'
                WHEN 0 THEN 'Manual Settlement'
                WHEN 2 THEN 'Customized Settlement'
            END
        ELSE 'NA'
    END AS 'Settlement Flag'
FROM
    transactions t
JOIN
    merchant m ON t.merchantID = m.id
LEFT JOIN
    txn_decline_codes tdc ON t.tgId = tdc.tgID AND t.responseCode = tdc.responseCode
LEFT JOIN
    txn_decline_codes tr ON t.responseCode = tr.responseCode AND tr.tgID IS NULL
LEFT JOIN
    mapping_merchant_user_terminal mmut ON mmut.id = t.divisionID AND m.id = mmut.divisionID
LEFT JOIN
    mapping_division_acquirer_tg mdat ON mdat.id = mmut.divAcqTgId
WHERE
    t.transactionTime BETWEEN
        CASE
            WHEN CURTIME() BETWEEN '08:30:00' AND '13:14:59' THEN TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL 1 DAY), '16:00:00')
            WHEN CURTIME() BETWEEN '13:15:00' AND '16:14:59' THEN TIMESTAMP(CURDATE(), '08:00:00')
            ELSE TIMESTAMP(CURDATE(), '13:00:00')
        END
    AND
        CASE
            WHEN CURTIME() BETWEEN '08:30:00' AND '13:14:59' THEN TIMESTAMP(CURDATE(), '07:59:59')
            WHEN CURTIME() BETWEEN '13:15:00' AND '16:14:59' THEN TIMESTAMP(CURDATE(), '12:59:59')
            ELSE TIMESTAMP(CURDATE(), '15:59:59')
        END
    AND t.typeID = 22
    AND t.acqId = 23;
"""
