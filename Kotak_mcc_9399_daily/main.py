from datetime import datetime
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
from enviroment import query,config
reportName = argv[1]
foldername = argv[2]
dir = argv[3]
os.chdir(os.path.join(dir,foldername))

# Set up logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
formatter = logging.Formatter(
    "%(asctime)s - %(name)s - %(levelname)s - %(message)s")

# Log to console
handler = logging.StreamHandler()
handler.setFormatter(formatter)
logger.addHandler(handler)

# Also log to a file
file_handler = logging.FileHandler("cpy-errors.log")
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)

def connect_to_mysql(config, attempts=3, delay=2):
    attempt = 1
    # Implement a reconnection routine
    while attempt < attempts + 1:
        try:
            return mysql.connector.connect(**config)
        except (mysql.connector.Error, IOError) as err:
            if (attempts is attempt):
                # Attempts to reconnect failed; returning None
                logger.info(
                    "Failed to connect, exiting without a connection: %s", err)
                return None
            logger.info(
                "Connection failed: %s. Retrying (%d/%d)...",
                err,
                attempt,
                attempts-1,
            )
            # progressive reconnect delay
            time.sleep(delay ** attempt)
            attempt += 1
    return None
def data_retrival(query):
    cnx = connect_to_mysql(config, attempts=3)
    if cnx and cnx.is_connected():
        with cnx.cursor() as cursor:
            result = cursor.execute(query)
            rows = cursor.fetchall()
            df = pd.DataFrame(rows, columns=cursor.column_names)
        cnx.close()
        return df
    else:
        return None

data = data_retrival(query)
Summary = (data
          .pivot_table(index=["ME Name"],columns="Type",values="Amount",aggfunc = "sum",fill_value=' ',margins=True,margins_name='Grand Total'))
file_name = "Kotak_Transaction_Report.xlsx"
with pd.ExcelWriter(file_name) as writer:
    Summary.to_excel(writer,sheet_name='Summary')
    data.to_excel(writer,sheet_name='Data',index=False)


print("Process Completed")
