#!/bin/bash

dir=$1
cd $dir
foldername=$(date -d "yesterday" '+%Y%m%d')
echo $foldername
[ ! -d "$foldername" ] && mkdir -p "$foldername"
cd $foldername

cd ..

reportName="Kotak_Transaction_Report"

python3 /home/<USER>/Reports/Scripts/Kotak_mcc_9399_daily/main.py $reportName $foldername $dir
filename="Kotak_Transaction_Report.xlsx"

python3 /home/<USER>/Reports/Scripts/Kotak_mcc_9399_daily/mail.py $foldername $filename $dir

cd $dir

cd $foldername
#rm -rf *.csv
#rm -rf *.png
echo "Completed"
