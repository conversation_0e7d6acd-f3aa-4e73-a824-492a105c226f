From="<EMAIL>"
server_name = "*************"
Port=587
Username="<EMAIL>"
Password= "Ms1&iUoOoYgMj"
config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}

to = ["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"]

#to=["<EMAIL>"]

query = """SELECT 
    t.id AS ID,
    CONCAT(' ', DATE(t.transactionTime)) AS 'Transaction Created Date',
    t.clientId AS User,
    t.userName AS Username,
    CASE
        WHEN t.typeID = 1 AND t.isTip IS NOT NULL THEN 'Tip Adjusted'
        ELSE t.transactionTypeName
    END AS Type,
    t.modeName AS Mode,
    round(CASE
        WHEN t.typeID = 15 AND t.emiType = 'debit' THEN t.additionalAmount/100
        ELSE t.amount/100
    END,2) AS Amount,
    t.authCode AS 'Auth Code',
    t.maskedCardNumber AS Card,
    t.drCrCode AS 'Card Type',
    t.cardTypeName AS 'Brand Type',
    CONCAT(' ', t.rrn) AS RRN,
    t.invoiceNumber AS 'Invoice#',
    t.serialNumber AS 'Device Serial',
    CASE
        WHEN t.settlementStatusName IS NULL THEN 'UNSETTLED'
        ELSE t.settlementStatusName
    END AS Status,
    CONCAT(' ', t.settlementTxnTime) AS 'Settled On',
    CASE
        WHEN
            t.tempMerchantCode IS NOT NULL
                AND t.tempMerchantCode != 0
        THEN
            CASE
                WHEN t.tempMerchantCode != t.merchantCode THEN CONCAT(' ', t.tempMerchantCode)
                ELSE CONCAT(' ', t.merchantCode)
            END
        WHEN t.merchantCode IS NULL THEN CONCAT(' ', mmut.merchantCode)
        ELSE CONCAT(' ', t.merchantCode)
    END AS MID,
    CASE
        WHEN
            t.tempTID IS NOT NULL AND t.tempTID != 0
        THEN
            CASE
                WHEN t.tempTID != t.terminalID THEN t.tempTID
                ELSE t.terminalID
            END
        WHEN t.terminalID IS NULL THEN mmut.terminalID
        ELSE t.terminalID
    END AS TID,
    t.batchNumber AS Batch,
    t.billNumber AS 'Reference#',
    CASE
        WHEN
            t.statusID NOT IN (2 , 7, 10)
                AND t.typeID NOT IN (3 , 10, 11, 16, 7)
        THEN
            CASE
                WHEN
                    t.tgID IS NOT NULL
                        AND tdc.description != ''
                THEN
                    tdc.description
                WHEN t.tgID IS NULL AND tr.description != '' THEN tr.description
            END
        ELSE NULL
    END AS 'Additional Information',
    t.latitude AS Latitude,
    t.longitude AS Longitude,
    t.cardHolderName AS Payer,
    t.securityToken AS 'TID Location',
    CONCAT(' ', TIME(t.transactionTime)) AS 'Transaction Created Time',
    t.txnStatusName AS 'Transaction Status',
    t.merchantName AS 'ME Name',
    t.acquirerName AS Acquirer,
    IFNULL(t.responseCode, '91') AS 'Response Code',
    t.currencyCode AS Currency,
    t.deviceID AS 'Device No',
    t.referenceTxnID AS 'Reference Txn Id',
    t.tgName AS tg,
    t.dbaName AS 'DBA Name',
    t.refundStatus AS 'Refund Status',
    CONCAT(' ', DATE(t.txnResponseTime)) AS 'Transaction Response Date',
    CONCAT(' ', TIME(t.txnResponseTime)) AS 'Transaction Response Time',
    m.enterpriseId,
    CASE
        WHEN
            settlementStatusID != 4
        THEN
            CASE
                WHEN t.forSettlement = 1 THEN 'Auto Settlement'
                WHEN t.forSettlement = 0 THEN 'Manual Settlement'
                WHEN t.forSettlement = 2 THEN 'Customized Settlement'
            END
        ELSE 'NA'
    END AS 'Settlement Flag'
FROM
    transactions t
        LEFT JOIN
    acquirer a ON t.acquirerName = a.name
        LEFT JOIN
    (SELECT 
        id, enterpriseId ,status
    FROM
        merchant
    WHERE
        status = 'A') m ON t.merchantID = m.id
        LEFT JOIN
    emiData emi ON t.id = emi.transactionId
        LEFT JOIN
    transaction_status ts ON t.statusID = ts.id
        LEFT JOIN
    transaction_type tt ON tt.id = t.typeID
        LEFT JOIN
    txn_decline_codes tdc ON t.tgId = tdc.tgID
        AND t.responseCode = tdc.responseCode
        LEFT JOIN
    txn_decline_codes tr ON t.responseCode = tr.responseCode
        AND tr.tgID IS NULL
        LEFT JOIN
    (SELECT 
        mmut.id, mmut.userName, mmut.terminalID, mdat.merchantCode
    FROM
        mapping_merchant_user_terminal mmut
    INNER JOIN mapping_division_acquirer_tg mdat ON mdat.id = mmut.divAcqTgId
    WHERE
        mmut.status = 'A'
    GROUP BY mmut.userName
    HAVING mmut.id = MIN(mmut.id)) mmut ON mmut.userName = t.userName
WHERE
    t.transactionTime >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        AND t.transactionTime < CURDATE()
        AND
        m.id in (430762,439365,517843,519007,522987,525615,525617,525618,540857,543419,543420,543421,543424,543426,543428,543429,543431,543432,543433,543434,543435,543436,543437,543439,543441,543442,543443,543444,543445,543448,543449,543454,543455,543459,543460,543461,543463,543466,548707,548708,548709,548710,548711,548712,548713,548714,548715,548716,548717,548718,548719,548720,548721,548722,548723,548724,548725,548726,548727,548728,579043,579044,579045,579046,579047,579048,579049,579050,579051,579052,579053,579054,579055,579056,579057,579058,579059,579060,579061,579062,579063,579064,579065,579066,579067,579068,579069,579070,579071,579072,579073,579074,579075,579076,579077,579078,579079,579080,579081,579082,579083,579084,579085,579086,579087,579088,579089,579090,579091,579092,579093,579094,579095,579096,579097,579098,579099,579100,579101,579102,579103,579104,579105,579106,579107,579108,579109,579110,579111,579112,579113,579114,579115,579116,579117,579118,579119,579120,579121,579122,579123,579124,579125,579126,579127,610549,610550,610551,610552,610553,610554,610555,610556,610557,610558,610559,610560,610561,610562,610563,610564,610565,610566,610567,610568,610569,610570,610571,610572,610573,617123,619735,619736,619737,619746,619747,619748,619749,619750,619751,619752,619764,667439,667440,667441,667442,667443,667444,667445,667446,667447,667448,667449,667450,667451,667452,667453,696143,696145,696146,696147,696148,696149,696150,696151,973065,973066)
        AND t.txnStatusName not in ('Declined')
        ;"""
