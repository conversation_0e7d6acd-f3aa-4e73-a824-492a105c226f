import os 
import sys
import datetime as dt
from datetime import timedelta,datetime,date

out_dir="/home/<USER>/Reports/InfiBeamMIS"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%d%m%Y")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
Transacting_output_FileName = f'MIS_CCAvenue_{(datetime.now() - timedelta(1)).strftime("%Y%m%d")}.csv'

Transacting_output = os.path.join(out_dir,yesterday_file_name,Transacting_output_FileName)

def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

def date_serial_number(serial_number: int):
    """
    Convert an Excel serial number to a Python datetime object
    :param serial_number: the date serial number
    :return: a datetime object
    """
    # Excel stores dates as "number of days since 1900"
    delta = dt.datetime(1899, 12, 30) + dt.timedelta(days=serial_number)
    return delta

#in_dir = sys.argv[1]
#out_dir = sys.argv[2]
config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}

TransactingQuery = """
SELECT 
--	Case when JSON_VALID(t.merchantSpecificResp) then merchantSpecificResp->>"$.merchant_refTxnId"
 --   ELSE null end
 JSON_UNQUOTE(
    JSON_EXTRACT(
        JSON_UNQUOTE(JSON_EXTRACT(merchantSpecificResp, '$.merchant.responseStatusBody')),
        '$.merchant_refTxnId'
    )
) as "CCAvenue Ref No.",
    t.id AS 'Partner Ref No',
    mdat.retailerGroup as 'REGID',
    t.terminalID as 'TID',
    round(t.amount / 100,2) AS 'Amount',
    t.rrn AS 'Bank QSI No',
    t.transactionTypeName AS 'Transaction Type',
    t.drCrName AS 'Card Type',
    t.cardTypeName AS 'Card Name',
    t.maskedCardNumber AS 'Card Number',
    t.custMaskedCardNumber as 'Card Hash',
    t.authCode AS 'Bank Auth Code',
    t.transactionTime AS 'Transaction Date',
    t.statusID AS 'Status Code',
    t.txnStatusName AS 'Transaction Status',
    CASE
        WHEN
            t.statusID NOT IN (2 , 7, 10)
                AND t.typeID NOT IN (3 , 10, 11, 16, 7)
        THEN
            CASE
                WHEN
                    t.tgID IS NOT NULL
                        AND tdc.description != ''
                THEN
                    tdc.description
                WHEN t.tgID IS NULL AND tr.description != '' THEN tr.description
            END
        ELSE t.responseDescription
    END AS 'Remark',
    tgTransactionId ,
    CASE
        WHEN t.txnEntryModeId = 1
        THEN 'Physical'
        WHEN t.txnEntryModeId = 2
        THEN 'Tokenized '
        ELSE t.txnEntryModeId
    END AS `txnRef4`
FROM
    transactions t
        LEFT JOIN
    txn_decline_codes tdc ON t.tgId = tdc.tgID
        AND t.responseCode = tdc.responseCode
        LEFT JOIN
    txn_decline_codes tr ON t.responseCode = tr.responseCode
        AND tr.tgID IS NULL
		inner join
	mapping_merchant_user_terminal mmut on mmut.terminalID = t.terminalID
		inner join 
	mapping_division_acquirer_tg mdat on mdat.id = mmut.divAcqTgId
WHERE
 t.transactionTime >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
     AND t.transactionTime < CURDATE()
      AND t.acqId in (76)
     AND t.settlementStatusID = 2
ORDER BY t.id DESC;
"""
