#!/bin/bash

# Variables
REMOTE_USER="mosambee01.12345"        # Replace with the remote username
REMOTE_PASS="YBI+XW9=y^bY"   # Replace with the remote password
REMOTE_HOST="**************"        # Replace with the remote host or IP
REMOTE_PORT="11000"                    # Replace with the remote port (default is 22)
REMOTE_BASE_PATH="/Mosambee/MIS/Input"  # Base path on the remote server
LOCAL_BASE_PATH="/home/<USER>/Reports/InfiBeamMIS" # Base folder on the local system

# Get yesterday's date in YYYY-MM-DD format
yesterday_date=$(date --date yesterday "+%d%m%Y")
#yesterday_date=11012025
#echo "$yesterday_date"
# Local folder and file path
LOCAL_FOLDER="$LOCAL_BASE_PATH/$yesterday_date"
LOCAL_FILE=$(find "$LOCAL_FOLDER" -type f -name '*') # Find any file in the folder

# Remote folder path
REMOTE_FOLDER="$REMOTE_BASE_PATH/$yesterday_date"

# Log file for tracking
LOG_FILE="/home/<USER>/Reports/InfiBeamMIS/daily_file_transfer.log"
# Function to transfer file using sftp
transfer_file() {
  echo "$(date '+%Y-%m-%d %H:%M:%S') - Starting file transfer for $yesterday_date..." >> "$LOG_FILE"

  # Run sftp commands
  sshpass -p "$REMOTE_PASS" sftp -P "$REMOTE_PORT" -oBatchMode=no -b - "$REMOTE_USER@$REMOTE_HOST" << EOF
-mkdir $REMOTE_FOLDER
cd $REMOTE_FOLDER
put $LOCAL_FILE
EOF

  if [ $? -eq 0 ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - File transfer to $REMOTE_FOLDER successful." >> "$LOG_FILE"
  else
    echo "$(date '+%Y-%m-%d %H:%M:%S') - File transfer failed. Check the logs for details." >> "$LOG_FILE"
    exit 1
  fi
}

# Check if the local folder exists
if [ -d "$LOCAL_FOLDER" ]; then
  transfer_file
else
  echo "$(date '+%Y-%m-%d %H:%M:%S') - Local folder $LOCAL_FOLDER does not exist. Skipping file transfer." >> "$LOG_FILE"
fi

