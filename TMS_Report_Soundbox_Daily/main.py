from datetime import datetime,timed<PERSON>ta
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import config,format_int_with_commas,date_serial_number,NonTransactingQuery,TransactingQuery,Transacting_output,Non_Transacting_output
import XLSXAutoFitColumns

pd.options.display.float_format = '{:,.2f}'.format

Transacting_data = data_retrival(TransactingQuery)
NonTransacting_data = data_retrival(NonTransactingQuery)

Transacting_data = Transacting_data.astype({"sr no":'int'})
NonTransacting_data = NonTransacting_data.astype({"sr no":'int'})

NonTransacting_data.to_csv(Non_Transacting_output,index = False)
Transacting_data.to_csv(Transacting_output,index = False)

#fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(Transacting_output)
#fix_worksheet.process_all_worksheets()

#fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(Non_Transacting_output)
#fix_worksheet.process_all_worksheets()


print("File Creation is Successfull")
exit()
