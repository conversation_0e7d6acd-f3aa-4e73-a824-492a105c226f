import os 
import sys
import datetime as dt
from datetime import timedelta,datetime,date

out_dir="/home/<USER>/Reports/TMS_Report_Soundbox_Daily/"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%d%m%Y")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
Transacting_output_FileName = f'TEST_MOSAMBEE_TMS_SOUNDBOX_TRANSACTION_REPORT_{(datetime.now() - timedelta(1)).strftime("%d%m%Y")}.csv'
Non_Transacting_output_FileName = f'TEST_MOSAMBEE_TMS_SOUNDBOX_NON_TRANSACTION_REPORT_{(datetime.now() - timedelta(1)).strftime("%d%m%Y")}.csv'

Transacting_output = os.path.join(out_dir,yesterday_file_name,Transacting_output_FileName)
Non_Transacting_output = os.path.join(out_dir,yesterday_file_name,Non_Transacting_output_FileName)

def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

def date_serial_number(serial_number: int):
    """
    Convert an Excel serial number to a Python datetime object
    :param serial_number: the date serial number
    :return: a datetime object
    """
    # Excel stores dates as "number of days since 1900"
    delta = dt.datetime(1899, 12, 30) + dt.timedelta(days=serial_number)
    return delta

#in_dir = sys.argv[1]
#out_dir = sys.argv[2]
config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}

TransactingQuery = """
SELECT
            @sr:=@sr+1 as `sr no`,
                terminalID as Tid,
                COALESCE(Device_serial, 0) as Device_serial ,
                device_version as 'Machine Name/device_version',
                Application_Version as 'Application Version',
                mdat.merchantCode as 'mid',
                businessName as 'name',
                acquirerName as 'acquirer',
                type as 'type',
                mcc as 'mcc',
                `city` as 'city',
                `state` as 'state',
                installation_date as 'Installation date',
                last_txn_date AS 'Heartbeat/Last login date',
                last_txn_date as 'last_txn_date',
                latitude,
                longitude,
                upi_enabled as 'upi_enabled',
                bqr_enabled as 'bqr_enabled',
                smspay_enabled as 'smspay_enabled',
                nfc_enabled as 'nfc_enabled',
                emi_enabled as 'emi_enabled',
                integrated as 'integrated',
                total_txn_count as 'total_txn_count',
                total_txn_volume as 'total_txn_volume',
                dc_txn_count as 'dc_txn_count',
                dc_txn_volume as 'dc_txn_volume',
                cc_txn_count as 'cc_txn_count',
                cc_txn_volume as 'cc_txn_volume',
                upi_count as 'upi_txn_count',
                upi_count_volume as 'upi_txn_volume',
                bqr_count as 'bqr_txn_count',
                bqr_count_volume as 'bqr_txn_volume',
                emi_count as 'emi_txn_count',
                emi_count_volume as 'emi_txn_volume',
                sms_count as 'sms_txn_count',
                sms_count_volume as 'sms_txn_count_volume',
                `total_txn_count>=5000` as 'txn_count_>5000',
               `total_txn_volume>=5000` as 'txn_volume_>5000',
                `total_txn_count>=1000` as 'txn_count_>1000',
                `total_txn_volume>=1000` as 'txn_volume_>1000'
            from (SELECT @sr:= 0) AS sr,(
            SELECT
            t.terminalID as 'terminalID',
            concat("'",t.serialNumber) as 'Device_serial',
            t.terminalType as 'device_version' ,
            t.appVersion as 'Application_Version',
            t.merchantCode AS 'MID',
            m.businessName AS 'businessName',
            t.acquirerName AS 'acquirerName',
            '' as 'type',
            m.mcc AS 'mcc',
            c.name AS `city`,
            s.name AS `state`,
            date_format(m.recordCreated, '%Y-%m-%d %H:%i:%s') AS 'installation_date',
            date_format(t.transactionTime, '%Y-%m-%d %H:%i:%s') as 'last_login_date',
            date_format(max(t.transactionTime), '%Y-%m-%d %H:%i:%s') AS 'last_txn_date',
            case when t.latitude is null or 0 then m.latitude else t.latitude end as 'latitude',
            case when t.longitude is null or 0 then m.longitude else t.longitude end  as 'longitude',
            CASE WHEN SUM(CASE WHEN typeId = 22 THEN 1 ELSE 0 END) > 0 THEN 'Y' ELSE 'N' END as upi_enabled,
            CASE WHEN SUM(CASE WHEN typeId = 24 THEN 1 ELSE 0 END) > 0 THEN 'Y' ELSE 'N' END as bqr_enabled,
            CASE WHEN SUM(CASE WHEN typeId = 19 THEN 1 ELSE 0 END) > 0 THEN 'Y' ELSE 'N' END as smspay_enabled,
            'Y' as nfc_enabled,
            CASE WHEN SUM(CASE WHEN typeId = 15 THEN 1 ELSE 0 END) > 0 THEN 'Y' ELSE 'N' END as emi_enabled,
            CASE WHEN isIntegrated = '1' THEN 'Integrated' ELSE 'non-Integrated' END  as integrated,
            count(t.id) as total_txn_count,
            ROUND(sum(t.amount/100),2)  as total_txn_volume,
            SUM(CASE WHEN drCrCode IN ('DD' , 'FD') THEN 1 ELSE 0 END) AS dc_txn_count,
            ROUND(SUM(CASE WHEN drCrCode IN ('DD' , 'FD') THEN amount ELSE 0 END) / 100,2) dc_txn_volume,
            SUM(CASE WHEN drCrCode IN ('DC' , 'FC') THEN 1 ELSE 0 END) AS cc_txn_count,
            ROUND((CASE WHEN drCrCode IN ('DC' , 'FC') THEN amount ELSE 0 END) / 100,2) AS cc_txn_volume,
            SUM(CASE WHEN typeId = 22 THEN 1 ELSE 0 END) AS upi_count,
            ROUND(SUM(CASE WHEN typeId = 22 THEN amount ELSE 0 END) / 100, 2) AS upi_count_volume,
            SUM(CASE WHEN typeId = 24 THEN 1 ELSE 0 END) AS bqr_count,
            ROUND(SUM(CASE WHEN typeId = 24 THEN amount ELSE 0 END) / 100, 2) AS bqr_count_volume,
            SUM(CASE WHEN typeId = 15 THEN 1 ELSE 0 END) AS emi_count,
            ROUND(SUM(CASE WHEN typeId = 15 THEN amount ELSE 0 END) / 100, 2) AS emi_count_volume,
            SUM(CASE WHEN typeId = 19 THEN 1 ELSE 0 END) AS sms_count,
            ROUND(SUM(CASE WHEN typeId = 19 THEN amount ELSE 0 END) / 100, 2) AS sms_count_volume,
            SUM(CASE WHEN t.amount >= 500000 THEN 1 ELSE 0 END) AS `total_txn_count>=5000`,
            ROUND(SUM(CASE WHEN t.amount >= 500000 THEN amount ELSE 0 END) / 100,2) AS `total_txn_volume>=5000`,
            SUM(CASE WHEN t.amount >= 100000 THEN 1 ELSE 0 END) AS `total_txn_count>=1000`,
            ROUND(SUM(CASE WHEN t.amount >= 100000 THEN amount ELSE 0 END) / 100,2) AS `total_txn_volume>=1000`,
            t.userID as userIDs,
            t.merchantID
            FROM transactions t force index (transactions_trantime)
            INNER JOIN merchant m ON m.id = t.merchantID
            inner join city c
            inner join state s
            inner join address a on a.cityId = c.id
            WHERE m.addressId = a.id and a.stateID = s.id
            and t.statusID IN (2 , 7) AND t.tgID IN (4 , 21, 32, 33, 34, 35, 36, 45, 46, 47, 48, 49, 79, 80, 81) and t.acqId !=23
            and t.terminalID not like '68%'
            and t.transactionTime between  concat ( DATE_FORMAT(NOW() - interval 1 day ,'%Y-%m-01') ,' 00:00:00') and concat ( DATE_FORMAT(NOW() - interval 1 day ,'%Y-%m-%d') ,' 23:59:59')
            GROUP BY t.userID,t.merchantID
            ORDER BY t.merchantID
            ) a
            inner join mapping_division_acquirer_tg mdat on mdat.divisionID= a.merchantId
            group by a.userIDs , merchantId
            order by  `sr no`;
"""

NonTransactingQuery = """
select
@sr:=@sr+1 as `sr no`,
terminals as 'Tid' ,
'NA' as 'Device_serial',
'NA' as 'Machine Name/device_version' ,
'NA' as 'Application Version',
MID AS 'mid',
businessName AS 'name',
'HDFC' AS 'acquirer',
'' as 'type',
mcc as 'mcc',
`city` as 'city',
`state` as 'state',
last_login_date AS 'Heartbeat/Last login date',
last_login_date AS 'last_txn_date',
'' as 'latitude',
'' as 'longitude',
'N' as 'upi_enabled',
'N' as 'bqr_enabled',
'N' as 'smspay_enabled',
'N' as 'nfc_enabled',
'N' as 'emi_enabled',
integrated as 'integrated',
'0' as 'total_txn_count',
'0' as 'total_txn_volume',
'0' as 'dc_txn_count',
'0' as 'dc_txn_volume',
'0' as 'cc_txn_count',
'0' as 'cc_txn_volume',
'0' as 'upi_txn_count',
'0' as 'upi_txn_volume',
'0' as 'bqr_txn_count',
'0' as 'bqr_txn_volume',
'0' as 'emi_txn_count',
'0' as 'emi_txn_volume',
'0' as 'sms_txn_count',
'0' as 'sms_txn_count_volume',
'0' as `total_txn_count>=5000`,
'0' as `total_txn_volume>=5000`,
'0' as `total_txn_count>=1000`,
'0' as `total_txn_volume>=1000`
from (SELECT @sr:= 0) AS sr,(
select
  terminals,userId,last_login_date,merchantsId,userIds,mcc,city,state,mid,businessName,recordCreated,integrated
from
(SELECT
        mmut.terminalID AS 'terminals',
            mdat.merchantCode AS 'MID',
            mmut.divisionId AS 'merchantsId',
             mmut.userName as 'userNames',
             u.id as userIds,
             m.businessName as businessName,
             m.mcc as mcc,
             c.name AS `city`,
s.name AS `state`,
        m.recordCreated,
        u.id as userId,
        '' as recordcreatedLogs1,
        CASE
                WHEN MAX(l.recordcreated) IS NULL OR 0 THEN mmut.recordUpdated
                ELSE MAX(l.recordCreated)
            END AS last_login_date,
        CASE WHEN isIntegrated = '1' THEN 'Integrated' ELSE 'non-Integrated' END  as integrated
    FROM mapping_merchant_user_terminal mmut
    INNER JOIN mapping_division_acquirer_tg mdat ON mmut.divacqTgId = mdat.id
    INNER JOIN mapping_acquirer_tg mat ON mat.id = mdat.acqTgId
    INNER JOIN tg tt ON mat.tgID = tt.id
    INNER JOIN users u ON u.userName=mmut.userName
inner join merchant m on m.id = mmut.divisionId
    inner join address a on m.addressID = a.id
    inner join city c on a.cityID = c.id
    inner join state s on a.stateID = s.id
    INNER JOIN login_logs l ON l.userId = u.id
    WHERE
        mmut.status = 'A'
            AND tt.name LIKE 'HDFC' and tt.typeID=1
    GROUP BY mmut.terminalID -- , l.userID
    ) a
WHERE a.terminals IN (SELECT t.terminalId FROM transactions t force index (transactions_trantime)  WHERE t.statusID IN (2 , 7)
AND t.tgID IN (4 , 21, 32, 33, 34, 35, 36, 45, 46, 47, 48, 49, 79, 80, 81)
AND t.acqId != 23
and t.terminalID not like '68%'
AND t.transactionTime BETWEEN CONCAT(DATE_FORMAT(NOW()- interval 1 day, '%Y-%m-01'),
' 00:00:00') AND CONCAT(DATE_FORMAT(NOW() - INTERVAL 1 DAY, '%Y-%m-%d'),
' 23:59:59')
GROUP BY t.terminalId
) IS NOT TRUE
 group by userIds

UNION
 (
 select
  terminals,userId,last_login_date,merchantsId,userIds,mcc,city,state,mid,businessName,recordCreated,integrated
from
(SELECT
        mmut.terminalID AS 'terminals',
            mdat.merchantCode AS 'MID',
            mmut.divisionId AS 'merchantsId',
             mmut.userName as 'userNames',
             u.id as userIds,
             m.businessName as businessName,
             m.mcc as mcc,
             c.name AS `city`,
s.name AS `state`,
        m.recordCreated,
        u.id as userId,
         CASE
                WHEN MAX(l.recordcreated) IS NULL OR 0 THEN mmut.recordUpdated
                ELSE MAX(l.recordCreated)
            END AS last_login_date,
        CASE WHEN isIntegrated = '1' THEN 'Integrated' ELSE 'non-Integrated' END  as integrated
    FROM mapping_merchant_user_terminal mmut
    INNER JOIN mapping_division_acquirer_tg mdat ON mmut.divacqTgId = mdat.id
    INNER JOIN mapping_acquirer_tg mat ON mat.id = mdat.acqTgId
    INNER JOIN tg tt ON mat.tgID = tt.id
    INNER JOIN users u ON u.userName=mmut.userName
inner join merchant m on m.id = mmut.divisionId
    inner join address a on m.addressID = a.id
    inner join city c on a.cityID = c.id
    inner join state s on a.stateID = s.id
    inner join login_logs l on l.userId=u.id
    WHERE
        mmut.status = 'A'
            AND tt.name LIKE 'HDFC' and tt.typeID=1
    GROUP BY mmut.terminalID -- , l.userID
    ) a
WHERE a.terminals IN (SELECT  t.terminalId FROM transactions t force index (transactions_trantime) WHERE t.statusID IN (2 , 7)
AND t.tgID IN (4 , 21, 32, 33, 34, 35, 36, 45, 46, 47, 48, 49, 79, 80, 81)
AND t.acqId != 23
and t.terminalID not like '68%'
AND t.transactionTime BETWEEN CONCAT(DATE_FORMAT(NOW() - INTERVAL 1 DAY, '%Y-%m-01'),
' 00:00:00') AND CONCAT(DATE_FORMAT(NOW() - INTERVAL 1 DAY, '%Y-%m-%d'),
' 23:59:59')
GROUP BY t.terminalId
) IS NOT TRUE
 group by userIds
 )
 ) a
 where terminals not like '68%'
  group by userId
  order by `sr no`
 ;
"""
