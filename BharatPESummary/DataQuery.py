import os 
import sys
import datetime as dt
from datetime import date
from datetime import timedelta

out_dir="/home/<USER>/Reports/BharatPESummary/"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%d%m%Y")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
input_file = "/home/<USER>/Reports/Scripts/BharatPESummary/HistoicalData_BharatPE.xlsx"
output_file = os.path.join(out_dir,yesterday_file_name,"BharatPESummary.xlsx")

def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

def format_float_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,.2f}"

def formating(df):
    df2 = df
    for column in list(df2.select_dtypes(include=['int','float']).columns):
        if df2[column].dtype in (['int64','int32','int16','int8','int']):
            df2[column] =df2[column].map(format_int_with_commas)
        else:
            df2[column] = df2[column].map(format_float_with_commas)
    return df2
def date_serial_number(serial_number: int):
    """
    Convert an Excel serial number to a Python datetime object
    :param serial_number: the date serial number
    :return: a datetime object
    """
    # Excel stores dates as "number of days since 1900"
    delta = dt.datetime(1899, 12, 30) + dt.timedelta(days=serial_number)
    return delta

#in_dir = sys.argv[1]
#out_dir = sys.argv[2]
config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}
onboarding = """SELECT 
    m.id AS posId,
    mdat.merchantCode AS cardMID,
    mmut.terminalId AS cardTerminalId,
    mmut.status AS terminalStatus,
    mmut.recordCreated,
    (SELECT 
            mdat1.merchantCode
        FROM
            mapping_division_acquirer_tg mdat1
                INNER JOIN
            mapping_acquirer_tg mat1 ON mat1.id = mdat1.acqTgId
                AND mat1.acquirerName = mat.acquirerName
                AND mat1.instrumentId = 2
                AND mdat1.divisionId = mdat.divisionId) AS QR_MID,
    mmut.partnerTid AS qrTID,
    (SELECT 
            mdat1.merchantCode
        FROM
            mapping_division_acquirer_tg mdat1
                INNER JOIN
            mapping_acquirer_tg mat1 ON mat1.id = mdat1.acqTgId
                AND mat1.acquirerName = mat.acquirerName
                AND mat1.instrumentId = 3
                AND mdat1.divisionId = mdat.divisionId) AS UPI_MID,
    mmut.partnerTid AS UPITID
FROM
    mapping_division_acquirer_tg mdat
        INNER JOIN
    mapping_merchant_user_terminal mmut ON mdat.id = mmut.divAcqTgId and mmut.recordCreated<CURDATE() and mmut.status in ('A','D')
        INNER JOIN
    merchant m ON m.id = mdat.divisionId
        INNER JOIN
    mapping_acquirer_tg mat ON mat.id = mdat.acqTgId
        AND mat.instrumentId = 1
        AND mat.acquirerId = 68;"""

txn_Query = """SELECT 
    t.id as 'TXNID',
    t.terminalID as TID,
    t.Amount/100 as Amount,
    ts.name as TXNStatus,
    t.responseCode,
    t.transactionTypeName,
    t.refundStatus,
     CASE
        WHEN
            t.statusID NOT IN (2 , 7, 10)
        THEN
            CASE
                WHEN
                    t.tgID IS NOT NULL
                        AND tdc.description != ''
                THEN
                    tdc.description
                WHEN t.tgID IS NULL AND tr.description != '' THEN tr.description
            END
        ELSE NULL
    END AS transactionDesc
FROM
    transactions t 
            LEFT JOIN
    transaction_status ts ON t.statusID = ts.id
        LEFT JOIN
    txn_decline_codes tdc ON t.tgId = tdc.tgID
        AND t.responseCode = tdc.responseCode
        LEFT JOIN
    txn_decline_codes tr ON t.responseCode = tr.responseCode
        AND tr.tgID IS NULL
WHERE
    t.acqId = 68
    And t.isVoided = 0
        AND t.transactionTime >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        AND t.transactionTime < CURDATE();"""
