from datetime import datetime,timedelta
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import config,input_file,output_file,format_int_with_commas,format_float_with_commas,formating,date_serial_number,txn_Query,onboarding
import XLSXAutoFitColumns

pd.options.display.float_format = '{:,.2f}'.format

onbarding_data = data_retrival(onboarding)
testTid = pd.read_csv("/home/<USER>/Reports/Scripts/BharatPESummary/BharatPETestTID.csv")
onbarding_data["Status"] = np.where(
    onbarding_data.terminalStatus == "A", "Active", "De-Active"
)
onbarding_data["TID_Type"] = np.select(
    [
        (onbarding_data.cardTerminalId.str.len() == 8)
        & (onbarding_data.cardTerminalId.str.isdigit() == True)
    ],
    ["Both"],
    default="UPI",
)
onbarding_data["TestTID"] = np.where(
    onbarding_data.cardTerminalId.isin(testTid.TestTID), "Test", "Live"
)
grouped_data = onbarding_data.groupby(["TID_Type", "Status", "TestTID"]).agg(
    {"cardTerminalId": "count"}
)
onbarding_ltd = grouped_data.unstack(["Status", "TestTID"]).fillna(0)
columns = onbarding_ltd.columns
onbarding_ltd = onbarding_ltd.sort_index(level=["Status", "TestTID"], axis=1)
onbarding_ltd = onbarding_ltd.reset_index()
onbarding_data_month = onbarding_data[
    onbarding_data.recordCreated.dt.strftime("%b'%y")
    == datetime.now().strftime("%b'%y")
].reset_index(drop=True)
onbarding_data_month["TID_Type"] = "Both"
grouped_data = onbarding_data_month.groupby(["TID_Type", "Status", "TestTID"]).agg(
    {"cardTerminalId": "count"}
)
onbarding_mtd = grouped_data.unstack(["Status", "TestTID"]).fillna(0)
for column in columns:
    if column not in onbarding_mtd.columns:
        onbarding_mtd[column] = 0
onbarding_mtd = onbarding_mtd.sort_index(level=["Status", "TestTID"], axis=1)
onbarding_mtd = onbarding_mtd.reset_index()
onbarding_data_today = onbarding_data[onbarding_data.recordCreated.dt.strftime("%d'%B'%y") == (datetime.now() - timedelta(1)).strftime("%d'%B'%y")].reset_index(drop=True)
onbarding_data_today["TID_Type"]='Both'
onbording_ftd = (
    onbarding_data_today
    .reset_index(drop=True)
    .groupby(["TID_Type", "Status", "TestTID"])
    .agg({"cardTerminalId": "count"})
    .unstack(["Status", "TestTID"])
    .fillna(0)
)
for column in columns:
    if column not in onbording_ftd.columns:
        onbording_ftd[column] = 0
onbording_ftd = onbording_ftd.sort_index(level=["Status", "TestTID"], axis=1)
onbording_ftd = onbording_ftd.reset_index()
onbarding_ltd.TID_Type = np.where(
    onbarding_ltd.TID_Type == "Both",
    "Number of Terminals Enabled for Both Card & UPI",
    "Number of Terminals enabled Only for UPI",
)
onbarding_mtd.TID_Type = np.where(
    onbarding_mtd.TID_Type == "Both",
    f'MTD Terminals Added {datetime.now().strftime("%b-%y")} (Card & UPI)',
    "",
)
onbording_ftd.TID_Type = np.where(
    onbording_ftd.TID_Type == "Both",
    f'FTD Terminals Added {(datetime.now() - timedelta(1)).strftime("%d-%m-%Y")} (Card & UPI)',
    "",
)
onbording_summary = pd.concat(
    [onbarding_ltd, onbarding_mtd, onbording_ftd]
).reset_index(drop=True)
onbording_summary.columns = [
    "BharatPe Terminal Status",
    "Active Live Terminal",
    "Active Test Terminal",
    "De-Active Live Terminal",
    "De-Active Test Terminal",
]
total = pd.DataFrame(
    {
        "BharatPe Terminal Status": "Total Number of Terminals",
        "Active Live Terminal": onbording_summary["Active Live Terminal"][:2].sum(),
        "Active Test Terminal": onbording_summary["Active Test Terminal"][:2].sum(),
        "De-Active Live Terminal": onbording_summary["De-Active Live Terminal"][
            :2
        ].sum(),
        "De-Active Test Terminal": onbording_summary["De-Active Test Terminal"][
            :2
        ].sum(),
    },
    index=[0],
)

onbording_summary = pd.concat([onbording_summary, total]).reset_index(drop=True)
onbording_summary["Total"] = onbording_summary.sum(axis=1, numeric_only=True)
onbording_summary = onbording_summary.astype(
    {
        "Active Live Terminal": "int",
        "Active Test Terminal": "int",
        "De-Active Live Terminal": "int",
        "De-Active Test Terminal": "int",
        "Total": "int",
    }
)

txnData = data_retrival(txn_Query)
txnData = txnData[~txnData.TID.isin(testTid['TestTID'])].reset_index(drop=True)
txnData['Status'] = np.where(txnData['TXNStatus'].isin(['Approved','Signature Pending']),"Approved","Declined")
final = (txnData
 .groupby(['transactionTypeName','Status'])
 .agg({'TXNID':'count','Amount':'sum'})
 .reset_index()
 .rename({'TXNID':'Count'},axis=1)
 .pivot(index='transactionTypeName',columns='Status',values = ['Count','Amount']).reset_index()
)
final.columns = ("Type","Approved Count","Declined Count","Approved Amount", "Declined Amount")

CardFTD = pd.DataFrame({"Month": f'FTD {(datetime.now() - timedelta(1)).strftime("%d-%m-%Y")}',
                           "Approved Count": final[final['Type']=='Sale']['Approved Count'].astype('int'),
                           "Approved Amount": final[final['Type']=='Sale']['Approved Amount'].astype('float'),
                            "Declined Count": final[final['Type']=='Sale']['Declined Count'].astype('int'),
                           "Declined Amount": final[final['Type']=='Sale']['Declined Amount'].astype('float'),
                          },index=[0]) 
CardFTD['Total Count'] = CardFTD["Approved Count"]+CardFTD["Declined Count"]
CardFTD['Total Amount'] = CardFTD["Approved Amount"]+CardFTD["Declined Amount"]
UPIFTD = pd.DataFrame({"Month": f'FTD {(datetime.now() - timedelta(1)).strftime("%d-%m-%Y")}',
                           "Approved Count": final[final['Type']=='UPI']['Approved Count'].reset_index(drop=True).astype('int'),
                           "Approved Amount": final[final['Type']=='UPI']['Approved Amount'].reset_index(drop=True).astype('float'),
                            "Declined Count": final[final['Type']=='UPI']['Declined Count'].reset_index(drop=True).astype('int'),
                           "Declined Amount": final[final['Type']=='UPI']['Declined Amount'].reset_index(drop=True).astype('float'),
                          },index=[0]) 
UPIFTD['Total Count'] = UPIFTD["Approved Count"]+UPIFTD["Declined Count"]
UPIFTD['Total Amount'] = UPIFTD["Approved Amount"]+UPIFTD["Declined Amount"]
UPIFTD['TID Count'] = txnData[(txnData["transactionTypeName"]=='UPI') & (txnData["Status"]=="Approved")]['TID'].nunique()
CardFTD['TID Count'] = txnData[(txnData["transactionTypeName"]!='UPI') & (txnData["Status"]=="Approved")]['TID'].nunique()
UPIFTD = UPIFTD[["Month",'TID Count',"Approved Count","Approved Amount","Declined Count","Declined Amount",'Total Count','Total Amount']]
CardFTD = CardFTD[["Month",'TID Count',"Approved Count","Approved Amount","Declined Count","Declined Amount",'Total Count','Total Amount']]


CardHistory = pd.read_excel(input_file,sheet_name='Cards')
UPIHistory = pd.read_excel(input_file,sheet_name='UPI')
Transacting_terminalUPI = pd.read_excel(input_file,sheet_name="UPI_TransectingTerminal")
Transacting_terminalCards = pd.read_excel(input_file,sheet_name="Card_TransectingTerminal")
Transacting_terminalUPI = Transacting_terminalUPI.astype({'terminalId':"str"})
Transacting_terminalCards = Transacting_terminalCards.astype({'terminalId':"str"})
#Card_Data = txnData[txnData['transactionTypeName']!='UPI'].reset_index(drop=True)
Card_Data = txnData[(txnData['transactionTypeName']!='UPI') & (txnData['Status']=='Approved')].reset_index(drop=True)
UPI_Data = txnData[(txnData['transactionTypeName']=='UPI') & (txnData['Status']=='Approved')].reset_index(drop=True)
#UPI_Data = txnData[txnData['transactionTypeName']=='UPI'].reset_index(drop=True)
TID_list_UPI = UPI_Data[~UPI_Data.TID.isin(Transacting_terminalUPI.terminalId)].TID.unique()
TID_list_CARDS = Card_Data[~Card_Data.TID.isin(Transacting_terminalCards.terminalId)].TID.unique()

TID_list_df_Card = pd.DataFrame({"terminalId":TID_list_CARDS})
TID_list_df_UPI = pd.DataFrame({"terminalId":TID_list_UPI})
if (datetime.now() - timedelta(1)).strftime("%d")!= '01':
    Final_TID_list_UPI = pd.concat([Transacting_terminalUPI,TID_list_df_UPI]).reset_index(drop=True)
    Final_TID_list_Cards = pd.concat([Transacting_terminalCards,TID_list_df_Card]).reset_index(drop=True)

else:
    Final_TID_list_UPI = TID_list_df_UPI.copy()
    Final_TID_list_Cards = TID_list_df_Card.copy()

Final_TID_list_UPI = Final_TID_list_UPI.drop_duplicates(keep='first').reset_index(drop=True)
Final_TID_list_Cards = Final_TID_list_Cards.drop_duplicates(keep='first').reset_index(drop=True)

#CardHistory["Approved Count"] = CardHistory["Approved Count"].str.replace(',', '').astype("int64")
#CardHistory["Approved Amount"] = CardHistory["Approved Amount"].str.replace(',', '').astype("float64")
#CardHistory["Declined Count"] = CardHistory["Declined Count"].str.replace(',', '').astype("int64")
#CardHistory["Declined Amount"] = CardHistory["Declined Amount"].str.replace(',', '').astype("float64")
#CardHistory["Total Count"] = CardHistory["Total Count"].str.replace(',', '').astype("int64")
#CardHistory["Total Amount"] = CardHistory["Total Amount"].str.replace(',', '').astype("float64")

#UPIHistory["TID Count"] = UPIHistory["TID Count"].str.replace(',', '').astype("int64")
#UPIHistory["Approved Count"] = UPIHistory["Approved Count"].str.replace(',', '').astype("int64")
#UPIHistory["Approved Amount"] = UPIHistory["Approved Amount"].str.replace(',', '').astype("float64")
#UPIHistory["Declined Count"] = UPIHistory["Declined Count"].str.replace(',', '').astype("int64")
#UPIHistory["Declined Amount"] = UPIHistory["Declined Amount"].str.replace(',', '').astype("float64")
#UPIHistory["Total Count"] = UPIHistory["Total Count"].str.replace(',', '').astype("int64")
#UPIHistory["Total Amount"] = UPIHistory["Total Amount"].str.replace(',', '').astype("float64")

if (datetime.now() - timedelta(1)).strftime("%B") in list(CardHistory['Month']):
    curr = (datetime.now() - timedelta(1)).strftime("%B")
    curruntMonthCard = pd.DataFrame({
        "Month": curr,
        "TID Count":Final_TID_list_Cards.terminalId.nunique(),
        "Approved Count":CardFTD["Approved Count"]+CardHistory[CardHistory['Month']==(datetime.now() - timedelta(1)).strftime("%B")]["Approved Count"].reset_index(drop=True),
        "Approved Amount":CardFTD["Approved Amount"]+CardHistory[CardHistory['Month']==(datetime.now() - timedelta(1)).strftime("%B")]["Approved Amount"].reset_index(drop=True),
        "Declined Count":CardFTD["Declined Count"]+CardHistory[CardHistory['Month']==(datetime.now() - timedelta(1)).strftime("%B")]["Declined Count"].reset_index(drop=True),
        "Declined Amount":CardFTD["Declined Amount"]+CardHistory[CardHistory['Month']==(datetime.now() - timedelta(1)).strftime("%B")]["Declined Amount"].reset_index(drop=True),
        "Total Count":CardFTD["Total Count"]+CardHistory[CardHistory['Month']==(datetime.now() - timedelta(1)).strftime("%B")]["Total Count"].reset_index(drop=True),
        "Total Amount":CardFTD["Total Amount"]+CardHistory[CardHistory['Month']==(datetime.now() - timedelta(1)).strftime("%B")]["Total Amount"].reset_index(drop=True)
    })


    curruntMonthUPI = pd.DataFrame({
        "Month": curr,
        "TID Count":Final_TID_list_UPI.terminalId.nunique(),
        "Approved Count":UPIFTD["Approved Count"]+UPIHistory[UPIHistory['Month']==(datetime.now() - timedelta(1)).strftime("%B")]["Approved Count"].reset_index(drop=True),
        "Approved Amount":UPIFTD["Approved Amount"]+UPIHistory[UPIHistory['Month']==(datetime.now() - timedelta(1)).strftime("%B")]["Approved Amount"].reset_index(drop=True),
        "Declined Count":UPIFTD["Declined Count"]+UPIHistory[UPIHistory['Month']==(datetime.now() - timedelta(1)).strftime("%B")]["Declined Count"].reset_index(drop=True),
        "Declined Amount":UPIFTD["Declined Amount"]+UPIHistory[UPIHistory['Month']==(datetime.now() - timedelta(1)).strftime("%B")]["Declined Amount"].reset_index(drop=True),
        "Total Count":UPIFTD["Total Count"]+UPIHistory[UPIHistory['Month']==(datetime.now() - timedelta(1)).strftime("%B")]["Total Count"].reset_index(drop=True),
        "Total Amount":UPIFTD["Total Amount"]+UPIHistory[UPIHistory['Month']==(datetime.now() - timedelta(1)).strftime("%B")]["Total Amount"].reset_index(drop=True)
    })
    CardFinal = pd.concat([CardHistory[:-2],curruntMonthCard,CardFTD]).reset_index(drop=True)
    UPIFinal = pd.concat([UPIHistory[:-2],curruntMonthUPI,UPIFTD]).reset_index(drop=True)

else:
    curr = (datetime.now() - timedelta(1)).strftime("%B")
    curruntMonthCard = pd.DataFrame({
        "Month": curr,
        "TID Count":CardFTD["TID Count"],
        "Approved Count":CardFTD["Approved Count"],
        "Approved Amount":CardFTD["Approved Amount"],
        "Declined Count":CardFTD["Declined Count"],
        "Declined Amount":CardFTD["Declined Amount"],
        "Total Count":CardFTD["Total Count"],
        "Total Amount":CardFTD["Total Amount"]
    })

    curruntMonthUPI = pd.DataFrame({
        "Month": curr,
        "TID Count":UPIFTD["TID Count"],
        "Approved Count":UPIFTD["Approved Count"],
        "Approved Amount":UPIFTD["Approved Amount"],
        "Declined Count":UPIFTD["Declined Count"],
        "Declined Amount":UPIFTD["Declined Amount"],
        "Total Count":UPIFTD["Total Count"],
        "Total Amount":UPIFTD["Total Amount"]
    })
    CardFinal = pd.concat([CardHistory[:-1],curruntMonthCard,CardFTD]).reset_index(drop=True)
    UPIFinal = pd.concat([UPIHistory[:-1],curruntMonthUPI,UPIFTD]).reset_index(drop=True)

#CardFinal = pd.concat([CardHistory[:-1],curruntMonthCard,CardFTD]).reset_index(drop=True)
#UPIFinal = pd.concat([UPIHistory[:-1],curruntMonthUPI,UPIFTD]).reset_index(drop=True)

TotalCard = pd.DataFrame({
    "Month": "Total",
    "TID Count" : 00000,
    "Approved Count":CardFinal[:-1]["Approved Count"].sum(),
    "Approved Amount":CardFinal[:-1]["Approved Amount"].sum(),
    "Declined Count":CardFinal[:-1]["Declined Count"].sum(),
    "Declined Amount":CardFinal[:-1]["Declined Amount"].sum(),
    "Total Count":CardFinal[:-1]["Total Count"].sum(),
    "Total Amount":CardFinal[:-1]["Total Amount"].sum()
},index=[0])

TotalUPI = pd.DataFrame({
    "Month": "Total",
    "TID Count" : 00000,
    "Approved Count":UPIFinal[:-1]["Approved Count"].sum(),
    "Approved Amount":UPIFinal[:-1]["Approved Amount"].sum(),
    "Declined Count":UPIFinal[:-1]["Declined Count"].sum(),
    "Declined Amount":UPIFinal[:-1]["Declined Amount"].sum(),
    "Total Count":UPIFinal[:-1]["Total Count"].sum(),
    "Total Amount":UPIFinal[:-1]["Total Amount"].sum()
},index=[0])

CardFinal = pd.concat([CardFinal,TotalCard]).reset_index(drop=True)
UPIFinal = pd.concat([UPIFinal,TotalUPI]).reset_index(drop=True)

txnData["Description"]=np.where(txnData["transactionDesc"].isna(),"Error in transaction",txnData["transactionDesc"])
Response_Wise = (txnData[txnData["Status"]=="Declined"]
 .groupby(["transactionTypeName", "Description", "responseCode"])
 .agg({"TXNID": "count"})
 .reset_index()
 .rename({"transactionTypeName": "Type", "responseCode": "Response Code", "TXNID": "Count"},axis=1)
 .sort_values(["Type",'Count'],ascending=[True,False])
 .reset_index(drop=True)
)

ResponseTotal = pd.DataFrame({
    "Type":'Sale',
    "Description":"Total",
    "Response Code":"N/A",
    "Count":Response_Wise[Response_Wise["Type"]=='Sale']["Count"].sum()
},index=[0])
ResponseTotal = pd.concat([Response_Wise[Response_Wise["Type"]=='Sale'],ResponseTotal,Response_Wise[Response_Wise["Type"]=='UPI']]).reset_index(drop=True)

Response_total = pd.DataFrame(
    {
        "Type":"Grand Total",
        "Description":" ",
        "Response Code" :"  ",
        "Count" :Response_Wise.Count.sum()
    },
    index=[0]
)
Response_Wise = pd.concat([ResponseTotal,Response_total]).reset_index(drop=True)

Grand_total = pd.DataFrame({'Month':f'FTD Total of {(datetime.now() - timedelta(1)).strftime("%d-%m-%Y")}',
                            "Approved Count" : CardFTD["Approved Count"]+UPIFTD["Approved Count"],
                            "Approved Amount" : CardFTD["Approved Amount"]+UPIFTD["Approved Amount"],
                            "Declined Count": CardFTD["Declined Count"]+UPIFTD["Declined Count"],
                            "Declined Amount": CardFTD["Declined Amount"]+UPIFTD["Declined Amount"],
                            "Total Count": CardFTD["Total Count"]+UPIFTD["Total Count"],
                            "Total Amount": CardFTD["Total Amount"]+UPIFTD["Total Amount"],
                           },index=[0]
                          )

Final_total = pd.DataFrame({'Month':f'LTD Total Till {(datetime.now() - timedelta(1)).strftime("%d-%m-%Y")}',
                            "Approved Count" : TotalCard["Approved Count"]+TotalUPI["Approved Count"],
                            "Approved Amount" : TotalCard["Approved Amount"]+TotalUPI["Approved Amount"],
                            "Declined Count": TotalCard["Declined Count"]+TotalUPI["Declined Count"],
                            "Declined Amount": TotalCard["Declined Amount"]+TotalUPI["Declined Amount"],
                            "Total Count": TotalCard["Total Count"]+TotalUPI["Total Count"],
                            "Total Amount": TotalCard["Total Amount"]+TotalUPI["Total Amount"],
                           },index=[0]
                          )

Final_Grand = pd.concat([Grand_total,Final_total]).reset_index(drop=True)
with pd.ExcelWriter(input_file) as Writer:
    CardFinal[:-1].to_excel(Writer,sheet_name="Cards",index=False)
    UPIFinal[:-1].to_excel(Writer,sheet_name="UPI",index=False)
    Final_TID_list_UPI.to_excel(Writer,sheet_name='UPI_TransectingTerminal',index=False)
    Final_TID_list_Cards.to_excel(Writer,sheet_name='Card_TransectingTerminal',index=False)
onbording_summary_formated = formating(onbording_summary)
CardFinal_Formated = formating(CardFinal)
UPIFinal_Formated = formating(UPIFinal)
Response_Wise_formated = formating(Response_Wise)
Final_Grand_formated = formating(Final_Grand)

Response_Wise = pd.concat([Response_Wise,Response_total]).reset_index(drop=True)
with pd.ExcelWriter(output_file) as Writer:
    onbording_summary_formated.to_excel(Writer,sheet_name="OnBording Summary",index=False)
    CardFinal_Formated.to_excel(Writer,sheet_name="Card Summary",index=False)
    UPIFinal_Formated.to_excel(Writer,sheet_name="UPI Summary",index=False)
    Final_Grand_formated.to_excel(Writer,sheet_name="Grand Total",index=False)
    Response_Wise_formated.to_excel(Writer,sheet_name="Declined Summary",index=False)


fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(output_file)
fix_worksheet.process_all_worksheets()
		

print("File Creation is Successfull")
exit()
