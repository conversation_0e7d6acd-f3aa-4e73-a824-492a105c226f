import os 
import sys

from datetime import date
from datetime import timedelta

out_dir="/home/<USER>/Reports/HDFC_TSS_Daily/"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%d%m%Y")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
output_file = os.path.join(out_dir,yesterday_file_name,"TSS_File.xlsx")

def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

#in_dir = sys.argv[1]
#out_dir = sys.argv[2]
config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}

query = """
SELECT DISTINCT
    t.terminalID as TID, t.UserName as UserName, t.dbaName as 'DBA Name'
FROM
    transactions t
        LEFT JOIN
    merchant m ON t.merchantID = m.id
WHERE
    t.transactionTime >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        AND t.transactionTime < CURDATE()
        AND t.acqId = 3
        AND t.typeID = 1
        AND t.statusID NOT IN (2 , 7)
        AND t.responseCode IN ('12' , '14',
        'U0',
        'U1',
        'U2',
        'U3',
        'U4',
        'MD0011')
        AND m.status = 'A'
ORDER BY 3 , 1 , 2;
"""


