import os 
import sys
import pandas as pd
import datetime as dt
from datetime import date
from datetime import timedelta

out_dir="/home/<USER>/Reports/Axis_Bank_TXN_and_ONB_Summary/"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%d%m%Y")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
#input_file = "/home/<USER>/Reports/Scripts/HDFC_DCC/Historical_Data_DCC.xlsx"
input_dir = "/home/<USER>/Reports/Scripts/Axis_Bank_TXN_and_ONB_Summary/"
filePaths = {
    "Excel_input":os.path.join(input_dir,"Historical_Data_Axis_Bank_ONB.xlsx"),
    "Pickle":{"hist_summary":os.path.join(input_dir,"SummaryInputFile"),
              "HIST_LTD":os.path.join(input_dir,"HIST_LTDinputFile"),
              "TID_MTD":os.path.join(input_dir,"TID_MTDinputFile"),
              "TID_LTD":os.path.join(input_dir,"TID_LTDinputFile")
             }
}
output_file = os.path.join(out_dir,yesterday_file_name,"TID_wise_txn_summary_Axis_Bank.xlsx")

def readHistoricalData(filePaths,fileType="Pickle"):
    if fileType == "Pickle":
        hist_summary = pd.read_pickle(filePaths[fileType]["hist_summary"])
        HIST_LTD = pd.read_pickle(filePaths[fileType]["LTD"])
        TID_MTD = pd.read_pickle(filePaths[fileType]["TransactingTIDMTD"])
        TID_LTD = pd.read_pickle(filePaths[fileType]["TransactingTIDLTD"])
        
    else:
        hist_summary = pd.read_excel(filePaths["Excel_input"],sheet_name='Summary',parse_dates=False)
        HIST_LTD = pd.read_excel(filePaths["Excel_input"],sheet_name="LTD")
        TID_MTD = pd.read_excel(filePaths["Excel_input"],sheet_name="TIDMTD")
        TID_LTD = pd.read_excel(filePaths["Excel_input"],sheet_name="TIDLTD")
      #  hist_summary['Month'] = hist_summary['Month'].dt.strftime("%b'%y")
      #  hist_summary['Date'] = hist_summary['Date'].dt.strftime('%d-%m-%Y')
    return hist_summary , HIST_LTD , TID_MTD,TID_LTD

def writeHistoricalData(filePaths,curr_month,HIST_LTD,TID_MTD,TID_LTD,fileType="Pickle"):
    if fileType == "Pickle":
        hist_summary = pd.to_pickle(filePaths[fileType]["hist_summary"])
        HIST_LTD = pd.to_pickle(filePaths[fileType]["LTD"])
        TID_MTD = pd.to_pickle(filePaths[fileType]["TransactingTIDMTD"])
        TID_LTD = pd.to_pickle(filePaths[fileType]["TransactingTIDLTD"])
    else:
        with pd.ExcelWriter(filePaths["Excel_input"],engine='openpyxl',mode='a',if_sheet_exists="replace") as writer:
            TID_MTD.to_excel(writer,sheet_name='TIDMTD',index=False)
            TID_LTD.to_excel(writer,sheet_name='TIDLTD',index=False)
            curr_month.to_excel(writer,sheet_name='Summary',index=False)
            HIST_LTD.to_excel(writer,sheet_name='LTD',index=False)

def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

def date_serial_number(serial_number: int):
    """
    Convert an Excel serial number to a Python datetime object
    :param serial_number: the date serial number
    :return: a datetime object
    """
    # Excel stores dates as "number of days since 1900"
    delta = dt.datetime(1899, 12, 30) + dt.timedelta(days=serial_number)
    return delta

def format_int_with_percent(x):
    """
    Formats an integer with percent.
    """
    val = str(round(float(x)*100,2))+'%'
    return val

#in_dir = sys.argv[1]
#out_dir = sys.argv[2]
config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}


Summary_query = ("""
WITH SUMMARY_CTE AS (
    SELECT
    t.id AS transactionID,
    t.transactionTime,
    CASE
        WHEN t.typeID IN (22 , 24, 19) THEN 'DIGITAL'
        WHEN t.typeID IN (1,27,15) THEN 'CARD'
        ELSE 'OTHERS'
    END AS 'Type',
    CASE
        WHEN t.typeId = 15 AND t.emiType = 'debit' THEN t.additionalAmount / POWER(10, a.decimalAmountLength)
        ELSE t.amount / POWER(10, a.decimalAmountLength)
    END AS amount,
    IFNULL(t.tipAmount, 0) / IFNULL(POWER(10, a.decimalAmountLength), 2) AS tipAmount,
    IFNULL(t.additionalAmount, 0) / IFNULL(POWER(10, a.decimalAmountLength), 2) AS additionalAmount,
    IFNULL(t.authAmount, 0) AS authAmount,
    tp.program_name,
    t.terminalID,
    mmut.programId
FROM
    transactions t
        LEFT JOIN
    acquirer a ON t.acqId = a.id
        LEFT JOIN
    mapping_merchant_user_terminal mmut ON CASE
        WHEN t.divisionId = mmut.id THEN 1
        WHEN
            t.divisionID = 0
                AND t.merchantID = mmut.divisionId
        THEN
            1
        ELSE 0
    END = 1
        AND t.terminalID = mmut.terminalID
        AND mmut.terminalID NOT IN ('80137736' , '99594973')
        LEFT JOIN
    tbl_program tp ON mmut.programId = tp.program_id
WHERE
    t.transactionTime >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        AND t.transactionTime < CURDATE()
        AND t.statusId IN (2 , 7)
        AND t.typeID IN (1,27,15,22,24,19)
        AND t.acqid = 71
        AND t.terminalID not in (66771580, 66772430, 66772431)
        AND CASE
        WHEN mmut.status IS NULL THEN 'null'
        WHEN
            mmut.status = 'D'
                AND mmut.recordUpdated >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
                AND mmut.recordUpdated < CURDATE()
        THEN
            'A'
        ELSE mmut.status
    END IN ('A' , 'null')
)
SELECT DATE_FORMAT(transactionTime,"%d-%m-%Y") AS Date,
       DATE_FORMAT(transactionTime,"%b-%y") as Month,
       SUM(CASE WHEN TYPE = 'CARD' THEN 1 else 0 end) as "Card Txn Count",
       SUM(CASE WHEN TYPE = 'DIGITAL' THEN 1 else 0 end) as "Digital Txn Count",
       SUM(CASE WHEN TYPE = 'CARD' THEN amount else 0 end) as "Card Txn Amount" ,
       SUM(CASE WHEN TYPE = 'DIGITAL' THEN amount else 0 end) as "Digital Txn Amount" ,
       COUNT(*) AS "Total Txn Count",
       SUM(amount) AS "Total Txn Amount",
       COUNT(DISTINCT terminalID) AS unique_terminals
FROM SUMMARY_CTE
GROUP BY 1,2
ORDER BY 1,2;
""")

TID_Setup_Summary_query = ("""
WITH TID_SETUP_SUMMARY AS
(SELECT
    terminalID,
    MIN(DATE(mmut.recordCreated)) AS Setup_date,
    COUNT(terminalID) AS TID_Setup
FROM
    mapping_merchant_user_terminal mmut
        INNER JOIN
    mapping_division_acquirer_tg mdat ON mmut.divAcqTgId = mdat.id
        INNER JOIN
    mapping_acquirer_tg mat ON mdat.acqTgId = mat.id
        INNER JOIN
    acquirer a ON a.id = mat.acquirerID
WHERE
    a.id IN ('71')
    AND mmut.terminalID not in (66771580, 66772430, 66772431)
        AND DATE(mmut.recordCreated) >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        AND DATE(mmut.recordCreated) < CURDATE()
GROUP BY terminalID )
SELECT
    Setup_date AS Date, COUNT(*) AS TID_Setup
FROM
    TID_SETUP_SUMMARY
GROUP BY Setup_date
ORDER BY 1
;
""")

TID_INST_Summary_query = ("""
WITH TID_SETUP_SUMMARY AS
(SELECT
    terminalID,
    MIN(DATE(mmut.firstTxnDate)) AS Setup_date,
    COUNT(terminalID) AS TID_Setup
FROM
    mapping_merchant_user_terminal mmut
        INNER JOIN
    mapping_division_acquirer_tg mdat ON mmut.divAcqTgId = mdat.id
        INNER JOIN
    mapping_acquirer_tg mat ON mdat.acqTgId = mat.id
        INNER JOIN
    acquirer a ON a.id = mat.acquirerID
WHERE
    a.id IN ('71')
    AND mmut.terminalID not in (66771580, 66772430, 66772431)
        AND DATE(mmut.firstTxnDate) >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        AND DATE(mmut.firstTxnDate) < CURDATE()
GROUP BY terminalID )
SELECT
    Setup_date AS Date, COUNT(*) AS TID_Setup
FROM
    TID_SETUP_SUMMARY
GROUP BY Setup_date
ORDER BY 1
;
""")

TID_Transecting =("""
SELECT
    distinct t.terminalID
FROM
    transactions t
WHERE
    t.transactionTime >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        AND t.transactionTime < CURDATE()
        AND t.statusId IN (2 , 7)
        AND t.acqId = 71
        AND t.typeID in (1,27,15,22,24,19)
        AND t.terminalID not in (66771580, 66772430, 66772431);
    """)
