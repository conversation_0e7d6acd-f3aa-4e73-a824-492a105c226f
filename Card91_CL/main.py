from datetime import datetime,<PERSON><PERSON><PERSON>
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import config,format_int_with_commas,date_serial_number,TransactingQuery,Transacting_output
import XLSXAutoFitColumns

pd.options.display.float_format = '{:,.2f}'.format

txnData = data_retrival(TransactingQuery)
credit_debit_indianBank = txnData[(txnData.statusID.isin([2,7])) & (txnData['tgID'].isin([66,67,106,105])) & ~(txnData['Reference#'].str.contains(r'^CL.*ID', regex=True, na=False))]
credit_debit_card = txnData[(txnData['tgID'].isin([118])) & (txnData.statusID.isin([2,7])) & ~(txnData['Reference#'].str.contains(r'^CL.*ID', regex=True, na=False))]
credit_debit_cardLoad = txnData[(txnData.statusID.isin([2,7])) & (txnData['Reference#'].str.contains(r'^CL.*ID', regex=True, na=False))]
reversal_indianBank = txnData[(txnData.statusID.isin([8])) & (txnData['tgID'].isin([66,67,106,105]))]
reversal_cardLoad = txnData[(txnData.statusID.isin([8])) & (txnData['tgID'].isin([118]))]

credit_debit_card = credit_debit_card.reset_index(drop=True)
credit_debit_card = credit_debit_card.drop(['acqID','statusID','terminalType','tgID'],axis=1)
credit_debit_indianBank = credit_debit_indianBank.reset_index(drop=True)
credit_debit_indianBank = credit_debit_indianBank.drop(['acqID','statusID','terminalType','tgID'],axis=1)

credit_debit_cardLoad = credit_debit_cardLoad.reset_index(drop=True)
credit_debit_cardLoad = credit_debit_cardLoad.drop(['acqID','statusID','terminalType','tgID'],axis=1)

reversal_indianBank= reversal_indianBank.reset_index(drop=True)
reversal_indianBank= reversal_indianBank.drop(['acqID','statusID','terminalType','tgID'],axis=1)
reversal_cardLoad= reversal_cardLoad.reset_index(drop=True)
reversal_cardLoad= reversal_cardLoad.drop(['acqID','statusID','terminalType','tgID'],axis=1)

with pd.ExcelWriter(Transacting_output) as writer:
    credit_debit_indianBank.to_excel(writer,sheet_name="INDIAN BANK",index=False)
    credit_debit_card.to_excel(writer,sheet_name="Card91",index=False)
    credit_debit_cardLoad.to_excel(writer,sheet_name="Card91 Card load",index=False)
    reversal_indianBank.to_excel(writer,sheet_name="Indian Bank Reversal",index=False)
    reversal_cardLoad.to_excel(writer,sheet_name="Card91 Reversal",index=False)

fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(Transacting_output)
fix_worksheet.process_all_worksheets()

#fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(Non_Transacting_output)
#fix_worksheet.process_all_worksheets()
print("File Creation is Successfull")
exit()
