from datetime import datetime,timedelta
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import config,format_int_with_commas,date_serial_number,TransactingQuery,Transacting_output
import XLSXAutoFitColumns

pd.options.display.float_format = '{:,.2f}'.format

df = data_retrival(TransactingQuery)
df['txn_Date'] = pd.to_datetime(df['txn_Date'])
df['InstallationDate'] = pd.to_datetime(df['InstallationDate'])
# Format Installation Date
df['InstallationDate'] = df['InstallationDate'].dt.strftime('%d/%m/%y')

# Pivot with MultiIndex columns
pivot = pd.pivot_table(
    df,
    index=['POS_ID','ME_Name', 'InstallationDate'],
    columns=df['txn_Date'].dt.strftime('%d/%m/%y'),
    values='amount',
    aggfunc=['count','sum'],
    fill_value=0
)

# Sort columns by date (level 1)
pivot.columns = pivot.columns.swaplevel(0,1)
pivot.sort_index(axis=1, level=0, inplace=True)
pivot.columns = pivot.columns.set_levels(
    pivot.columns.levels[1].to_series().replace({'count': 'Txn_Count', 'sum': 'Amount'}).values,
    level=1
)

pivot.to_excel(Transacting_output)

fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(Transacting_output)
fix_worksheet.process_all_worksheets()

#fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(Non_Transacting_output)
#fix_worksheet.process_all_worksheets()
print("File Creation is Successfull")
exit()
