import os 
import sys
import datetime as dt
from datetime import timedelta,datetime,date

out_dir="/home/<USER>/Reports/AIRTEL_UPI_TRANSACTION"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%d%m%Y")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
Transacting_output_FileName = f'AIRTEL_UPI_TRANSACTION_Sumamry{(datetime.now() - timedelta(1)).strftime("%Y%m%d")}.xlsx'

Transacting_output = os.path.join(out_dir,yesterday_file_name,Transacting_output_FileName)

def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

def date_serial_number(serial_number: int):
    """
    Convert an Excel serial number to a Python datetime object
    :param serial_number: the date serial number
    :return: a datetime object
    """
    # Excel stores dates as "number of days since 1900"
    delta = dt.datetime(1899, 12, 30) + dt.timedelta(days=serial_number)
    return delta

#in_dir = sys.argv[1]
#out_dir = sys.argv[2]
config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}

TransactingQuery = """
WITH merchant_CTE AS (
    SELECT
        m.id AS merchant_id,
        m.dbaName AS dbaName,
        mat.acquirerName,
        mat.tgName,
        mmut.programId,
        MAX(m.recordCreated) AS InstallationDate,
        COUNT(mmut.terminalID) AS number_of_terminals
    FROM
        mapping_merchant_user_terminal mmut
    RIGHT JOIN merchant m
        ON m.id = mmut.divisionId
    RIGHT JOIN mapping_division_acquirer_tg mdat
        ON mdat.divisionID = m.id
    RIGHT JOIN mapping_acquirer_tg mat
        ON mat.id = mdat.acqTgId
    WHERE
        mat.tgID = 59
        AND mmut.programId = 149
        AND mmut.status = 'A'
        AND m.id NOT IN (
            2862242, 2862114, 2859856, 2873609, 2865236, 2857368, 2867875,
            2881053, 2863847, 2876710, 2855085, 2835920, 2871276, 2858329,
            2855055, 2883234, 2857313, 2876466, 2851410, 2862916, 2882758,
            2849150, 2881869, 2854053
        )
    GROUP BY
        m.id, m.dbaName, mat.acquirerName, mat.tgName, mmut.programId
)

SELECT
    DATE(t.transactionTime) AS txn_Date,
    merchant_CTE.merchant_id as POS_ID,
    merchant_CTE.dbaName AS ME_Name,
    merchant_CTE.InstallationDate,
    t.id AS txn_id,
    ROUND(t.amount / 100, 2) AS amount
FROM
    merchant_CTE
LEFT JOIN transactions t
    ON t.merchantID = merchant_CTE.merchant_id
WHERE
    t.transactionTime >= DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 DAY), '%Y-%m-01') and        
        t.transactionTime < CURDATE()
;
"""
