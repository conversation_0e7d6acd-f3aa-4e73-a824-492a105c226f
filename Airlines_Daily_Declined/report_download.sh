#!/bin/bash

dir=$2
cd $dir
tabcmd login -s http://************:8080 -u tabadmin -p $1
foldername=$(date -d "yesterday" '+%Y%m%d')
echo $foldername
[ ! -d "$foldername" ] && mkdir -p "$foldername"
cd $foldername
tabcmd export "/AirlinesDaily/AirlinesDaily?:refresh=yes" --csv -f "AirinesDaily.csv"
tabcmd export "/AirlinesDaily/EnterpriseSupport?:refresh=yes" --csv -f "EnterpriseSupport.csv"
#tabcmd export "/AirlinesDaily/Data?:refresh=yes" --csv -f "Data.xlsx"
tabcmd logout

cd ..

reportName="Airlines_Daily"

python3 /home/<USER>/Reports/Scripts/Airlines_Daily_Declined/main.py $reportName $foldername $dir
filename="Decline_Transaction_Summary_Report_Daily.xlsx"

python3 /home/<USER>/Reports/Scripts/Airlines_Daily_Declined/mail.py $foldername $filename $dir

cd $dir

cd $foldername
rm -rf *.csv
#rm -rf *.png
echo "Completed"
