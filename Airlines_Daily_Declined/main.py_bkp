from sys import exit,argv
import os
import pandas as pd
import numpy as np
import string
import XLSXAutoFitColumns
from openpyxl.styles import Font, Fill,Border,Alignment

reportName = argv[1]
foldername = argv[2]
dir = argv[3]
os.chdir(os.path.join(dir,foldername))
print(f'current Directory {os.getcwd()}')
airlines_daily = pd.read_csv("AirinesDaily.csv")
airlines_daily['Additional Information'].mask(airlines_daily['responseCode'] == '00', "Approved or completed successfully",inplace=True)
airlines_daily['Additional Information'].mask(airlines_daily['Additional Information'] == 'Blank', "Error in transaction",inplace=True)
airlines_mapping = {
    5737:"INDIGO",
    11258:"AIRASIA",
    11264: "AIR_INDIA_EXPRESS_LIMITED",
    10890:"AKASA_AIR"
}
airlines_daily.rename({"responseCode":"Response Code",
                        "Transaction Status (group)":"Transaction Status",
                        "Distinct count of Transaction ID":"Total"},axis=1,inplace=True)
airlines_daily['Total']=airlines_daily['Total'].str.replace(",",'').astype('int64')
for i in airlines_daily['enterpriseId'].unique():
    Airline = i
    Airline =  airlines_daily[airlines_daily["enterpriseId"]==i]
    Airline.reset_index(drop=True,inplace=True)
    Airline = Airline[["Transaction Status","Response Code","Additional Information","Total"]]
    Approved_total = pd.DataFrame({'Transaction Status':'Approved Total',
                       'Response Code':'',
                       'Additional Information' : '',
                        'Total': Airline[Airline['Transaction Status']=='Approved'].Total.sum()
                      },index=[0])
    Declined_total = pd.DataFrame({'Transaction Status':'Declined Total',
                       'Response Code':'',
                       'Additional Information' : '',
                        'Total': Airline[Airline['Transaction Status']=='Declined'].Total.sum()
                      },index=[0])
    Grand_total = pd.DataFrame({'Transaction Status':'Grand Total',
                       'Response Code':'',
                       'Additional Information' : '',
                        'Total': Airline['Total'].sum()
                      },index=[0])
    Data_final = pd.concat([Airline[Airline['Transaction Status']=='Approved'],Approved_total,Airline[Airline['Transaction Status']=='Declined'],Declined_total,Grand_total])
    Data_final.reset_index(drop=True,inplace=True)
    with pd.ExcelWriter(f"{airlines_mapping[i]}.xlsx") as writer:
        declinedLength = len(Airline[Airline["Transaction Status"]=='Declined'])
        Data_final.to_excel(writer,sheet_name=f"{airlines_mapping[i]}",index=False)
        ws = writer.sheets[f"{airlines_mapping[i]}"]
        ws.merge_cells('B3:C3')
        app_total = ws["A3"]
        app_total.font = Font(bold=True)
        app_total = ws["D3"]
        app_total.font = Font(bold=True)
        ws.merge_cells(f'A4:A{3+declinedLength}')
        ws.merge_cells(f'B{4+declinedLength}:C{4+declinedLength}')
        top_left_cell = ws['A4']
        top_left_cell.alignment = Alignment(horizontal="left", vertical="center")
        dec_total = ws[f"A{4+declinedLength}"]
        dec_total.font = Font(bold=True)
        dec_total = ws[f"D{4+declinedLength}"]
        dec_total.font = Font(bold=True)
        ws.merge_cells(f'B{5+declinedLength}:C{5+declinedLength}')
        dec_total = ws[f"A{5+declinedLength}"]
        dec_total.font = Font(bold=True)
        dec_total = ws[f"D{5+declinedLength}"]
        dec_total.font = Font(bold=True)
    fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(f"{airlines_mapping[i]}.xlsx")
    fix_worksheet.process_all_worksheets()

enterpriseSupport = pd.read_csv("EnterpriseSupport.csv")
enterpriseSupport['Additional Information'].mask(enterpriseSupport['Additional Information'] == 'Blank', "Error in transaction",inplace=True)
enterpriseSupport["terminalID"] = enterpriseSupport["terminalID"].astype(str)
enterpriseSupport["Usernumber"] = enterpriseSupport["Usernumber"].astype(str)
enterpriseSupport["terminalID"].mask(enterpriseSupport["terminalID"] == 'nan', "",inplace=True)
enterpriseSupport["terminalID"]=enterpriseSupport.terminalID.apply(lambda x: pd.Series(str(x).split(".")))[0]
enterpriseSupport.rename({"responseCode":"Response Code",
                        "Transaction Status (group)":"Transaction Status",
                        "terminalID":"TID",
                         "merchantCode":'MID',
                         "Usernumber":"User Number"},axis=1,inplace=True)
for i in enterpriseSupport['enterpriseId'].unique():
    Airline = i
    Airline = enterpriseSupport[enterpriseSupport["enterpriseId"]==i]
    Airline.reset_index(drop=True,inplace=True)
    Airline = Airline[["Transaction Status","Response Code","Additional Information","User Number","TID","MID"]]
    Airline.sort_values("Response Code",inplace=True)
    Airline.sort_values("Response Code",inplace=True,ignore_index=True)
    with pd.ExcelWriter(f"EnterpriseSupport{airlines_mapping[i]}.xlsx") as writer:
        Airline.to_excel(writer,sheet_name=f"{airlines_mapping[i]}",index=False)
        ws = writer.sheets[f"{airlines_mapping[i]}"]
        ws.merge_cells(f'A2:A{Airline.shape[0]+1}')
        top_left_cell = ws['A2']
        top_left_cell.alignment = Alignment(horizontal="left", vertical="center")
        responseCellStart = 2
        for responseCode in Airline["Response Code"].unique():
            responseCellEnd=responseCellStart+Airline[Airline["Response Code"]==responseCode].shape[0]-1
            ws.merge_cells(f'B{responseCellStart}:B{responseCellEnd}')
            top_left_cell = ws[f'B{responseCellStart}']
            top_left_cell.alignment = Alignment(horizontal="left", vertical="center")
            ws.merge_cells(f'C{responseCellStart}:C{responseCellEnd}')
            top_left_cell = ws[f'C{responseCellStart}']
            top_left_cell.alignment = Alignment(horizontal="left", vertical="center")
            responseCellStart = responseCellEnd+1
    fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(f"EnterpriseSupport{airlines_mapping[i]}.xlsx")
    fix_worksheet.process_all_worksheets()



exit()

