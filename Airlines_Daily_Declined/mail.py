import os
import sys
import smtplib
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from email.mime.image import MIMEImage
from enviroment import From,server_name,to,Port,Password,Username
from datetime import datetime, timedelta
import glob
import pandas as pd
folder_name = sys.argv[1]
print(os.getcwd())
files = os.listdir(os.path.join(os.getcwd(),folder_name))

for file in files:
    if file.endswith('.xlsx') and not file.startswith("EnterpriseSupport"):
        Airline_name = file.split(sep='.')[0].replace('_'," ")
        msg = MIMEMultipart()
        msg['From'] = From
        msg["To"] = ','.join(to)
        #msg["Cc"] = to
        msg['Subject'] = f"{Airline_name} sale & declined transactions {(datetime.now() - timedelta(1)).strftime('%d-%m-%Y')}"
        htmlEmail = f"""
                    <p> Dear Team, <br/><br/>
                        PFA {Airline_name} successful & declined transaction details for dated  {(datetime.now() - timedelta(1)).strftime('%d-%m-%Y')}.<br/>
                    </p>
                    """
        f = os.path.join(os.getcwd(),folder_name,file)
        #html tables data
        print(f)
        xl = pd.ExcelFile(f)
        sheet = file.split(sep='.')[0]
        def generate_html(f,sheet):
            read_file = pd.read_excel(f,sheet_name=sheet)
            html_file = f'''{read_file.to_html(index=False,na_rep='',border=None)}'''
            return html_file
        html_txt = generate_html(f,sheet)
        htmlEmail = "<br/>".join([htmlEmail,html_txt])
        htmlEmail2 = """
        <p> Please contact Support Team (<EMAIL>) directly if you have any questions. <br/>
            Thank you! <br/>
            Best Regards, <br/>
            Mosambee Support Team </p>
        <br/>
        <font color="red">Please do not reply to this email as it is auto-generated. </font>
        """
        htmlEmail = "<br/>".join([htmlEmail,htmlEmail2])
        msg.attach(MIMEText(htmlEmail, 'html'))

        with open(f, "rb") as attached_file:
            part = MIMEApplication(
                    attached_file.read(),
                    Name=os.path.basename(f)
                    )
        # After the file is closed
        part['Content-Disposition'] = 'attachment; filename="%s"' % os.path.basename(f)
        msg.attach(part)
        ef = f'EnterpriseSupport{file.split(".")[0]}.xlsx'
        enterprisefile = os.path.join(os.getcwd(),folder_name,f'{ef}')
        print(enterprisefile)
        if os.path.isfile(enterprisefile):
            #print(enterprisefile)
            with open(enterprisefile, "rb") as attached_file:
                part = MIMEApplication(
                        attached_file.read(),
                        Name=os.path.basename(enterprisefile)
                        )
                # After the file is closed
            part['Content-Disposition'] = 'attachment; filename="%s"' % os.path.basename(enterprisefile)
            msg.attach(part)
        try:
            server = smtplib.SMTP(server_name, Port)
            server.starttls()
            server.login(Username,Password)
            text = msg.as_string()
            server.sendmail(From, to, text)
            server.quit()
        except:
            print("An exception occurred")
        print("Email are sent successfully!")
print("All the emails are sent successfully!")
