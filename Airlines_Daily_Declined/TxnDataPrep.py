from datetime import datetime
import pandas as pd
import mysql.connector
import logging
import time

config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}

# Set up logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
formatter = logging.Formatter(
    "%(asctime)s - %(name)s - %(levelname)s - %(message)s")

# Log to console
handler = logging.StreamHandler()
handler.setFormatter(formatter)
logger.addHandler(handler)

# Also log to a file
file_handler = logging.FileHandler("cpy-errors.log")
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)

def connect_to_mysql(config, attempts=3, delay=2):
    attempt = 1
    # Implement a reconnection routine
    while attempt < attempts + 1:
        try:
            return mysql.connector.connect(**config)
        except (mysql.connector.Error, IOError) as err:
            if (attempts is attempt):
                # Attempts to reconnect failed; returning None
                logger.info(
                    "Failed to connect, exiting without a connection: %s", err)
                return None
            logger.info(
                "Connection failed: %s. Retrying (%d/%d)...",
                err,
                attempt,
                attempts-1,
            )
            # progressive reconnect delay
            time.sleep(delay ** attempt)
            attempt += 1
    return None

query = """
SELECT
    t.id AS ID,
    DATE(t.transactionTime) AS 'Transaction Created Date',
    t.clientId AS User,
    t.userName AS Username,
    CASE
        WHEN t.typeID = 1 AND t.isTip IS NOT NULL THEN 'Tip Adjusted'
        ELSE t.transactionTypeName
    END AS Type,
    t.modeName AS Mode,
    CASE
        WHEN t.typeID = 15 AND t.emiType = 'debit' THEN t.additionalAmount / 100
        ELSE t.amount / 100
    END AS Amount,
    t.authCode AS 'Auth Code',
    t.maskedCardNumber AS Card,
    t.drCrCode AS 'Card Type',
    t.cardTypeName AS 'Brand Type',
    t.rrn AS RRN,
    t.invoiceNumber AS 'Invoice#',
    t.serialNumber AS 'Device Serial',
    CASE
        WHEN t.settlementStatusName IS NULL THEN 'UNSETTLED'
        ELSE t.settlementStatusName
    END AS Status,
    t.settlementTxnTime AS 'Settled On',
    CASE
        WHEN
            t.tempMerchantCode IS NOT NULL
                AND t.tempMerchantCode != 0
        THEN
            CASE
                WHEN t.tempMerchantCode != t.merchantCode THEN t.tempMerchantCode
                ELSE t.merchantCode
            END
        WHEN t.merchantCode IS NULL THEN mmut.merchantCode
        ELSE t.merchantCode
    END AS MID,
    CASE
        WHEN
            t.tempTID IS NOT NULL AND t.tempTID != 0
        THEN
            CASE
                WHEN t.tempTID != t.terminalID THEN t.tempTID
                ELSE t.terminalID
            END
        WHEN t.terminalID IS NULL THEN mmut.terminalID
        ELSE t.terminalID
    END AS TID,
    t.batchNumber AS Batch,
    t.billNumber AS 'Reference#',
    CASE
        WHEN
            tdc.responseCode IN ('12' , '14',
                'U0',
                'U1',
                'U2',
                'U3',
                'U4',
                'MD0011')
        THEN
            1
        ELSE 0
    END AS Declined_TSS,
    CASE
        WHEN
            t.statusID NOT IN (2 , 7, 10)
                AND t.typeID NOT IN (3 , 10, 11, 16, 7)
        THEN
            CASE
                WHEN
                    t.tgID IS NOT NULL
                        AND tdc.description != ''
                THEN
                    tdc.description
                WHEN t.tgID IS NULL AND tr.description != '' THEN tr.description
            END
        ELSE NULL
    END AS 'Additional Information',
    t.latitude AS Latitude,
    t.longitude AS Longitude,
    t.cardHolderName AS Payer,
    t.securityToken AS 'TID Location',
    TIME(t.transactionTime) AS 'Transaction Created Time',
    t.txnStatusName AS 'Transaction Status',
    t.merchantName AS 'ME Name',
    t.acquirerName AS Acquirer,
    IFNULL(t.responseCode, '91') AS 'Response Code',
    t.currencyCode AS Currency,
    t.deviceID AS 'Device No',
    t.referenceTxnID AS 'Reference Txn Id',
    t.tgName AS tg,
    t.dbaName AS 'DBA Name',
    t.refundStatus AS 'Refund Status',
    DATE(t.txnResponseTime) AS 'Transaction Response Date',
    TIME(t.txnResponseTime) AS 'Transaction Response Time',
    m.enterpriseId,
    Case When settlementStatusID != 4 then
    Case when t.forSettlement =1 Then 'Auto Settlement'
    When t.forSettlement = 0 Then 'Manual Settlement'
        when t.forSettlement= 2 Then 'Customized Settlement'
    End
    Else 'NA'
End as 'Settlement Flag'
FROM
    transactions t
        LEFT JOIN
    (SELECT
        id, enterpriseId, status
    FROM
        merchant
    WHERE
        status = 'A') m ON t.merchantID = m.id
        LEFT JOIN
    emiData emi ON t.id = emi.transactionId
        LEFT JOIN
    transaction_status ts ON t.statusID = ts.id
        LEFT JOIN
    transaction_type tt ON tt.id = t.typeID
        LEFT JOIN
    txn_decline_codes tdc ON t.tgId = tdc.tgID
        AND t.responseCode = tdc.responseCode
        LEFT JOIN
    txn_decline_codes tr ON t.responseCode = tr.responseCode
        AND tr.tgID IS NULL
        LEFT JOIN
    (SELECT
        mmut.id, mmut.userName, mmut.terminalID, mdat.merchantCode
    FROM
        mapping_merchant_user_terminal mmut
    INNER JOIN mapping_division_acquirer_tg mdat ON mdat.id = mmut.divAcqTgId
    WHERE
        mmut.status = 'A'
    GROUP BY mmut.userName
    HAVING mmut.id = MIN(mmut.id)) mmut ON mmut.userName = t.userName
WHERE
    t.transactionTime >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        AND t.transactionTime < CURDATE()
        and m.enterpriseId = %s
        and t.typeID in (1,7,8,9,14,15,27)
"""

def convert(td):
    if pd.isna(td):
        return td
    else:
        time = [str(td.components.hours), str(td.components.minutes), str(td.components.seconds)]
        return datetime.strptime(':'.join(time), '%H:%M:%S').time()

def data_retrival(query,enterpriseID):
    cnx = connect_to_mysql(config, attempts=3)
    if cnx and cnx.is_connected():

        with cnx.cursor() as cursor:
            result = cursor.execute(query,(str(enterpriseID),))
            rows = cursor.fetchall()
            df = pd.DataFrame(rows, columns=cursor.column_names)
        cnx.close()
        df['Transaction Created Time'] = df['Transaction Created Time'].apply(
            lambda x: convert(x))
        df['Transaction Response Time'] = df['Transaction Response Time'].apply(
            lambda x: convert(x))
        df = df[['ID', 'Transaction Created Date', 'User', 'Username', 'Type', 'Mode',
                'Amount', 'Auth Code', 'Card', 'Card Type', 'Brand Type', 'RRN',
                'Invoice#', 'Device Serial', 'Status', 'Settled On', 'MID', 'TID',
                'Batch', 'Reference#', 'Declined_TSS', 'Additional Information',
                'Latitude', 'Longitude', 'Payer', 'TID Location',
                'Transaction Created Time', 'Transaction Status', 'ME Name', 'Acquirer',
                'Response Code', 'Currency', 'Device No', 'Reference Txn Id', 'tg',
                'DBA Name', 'Refund Status', 'Transaction Response Date',
                'Transaction Response Time', 'Settlement Flag']]
        return df
    else:
        return None

