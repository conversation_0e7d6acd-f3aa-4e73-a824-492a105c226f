# All Transactions Report Configuration

# Report metadata
report_name: "ALL_TRANSACTIONS"
description: "Daily all transactions summary report"

# Scheduling
schedule:
  cron: "0 7 * * *"  # Daily at 7 AM
  timezone: "Asia/Kolkata"

# Data extraction
data_source:
  main_query: |
    SELECT 
        t.id,
        t.terminalID,
        t.transactionTypeName,
        mat.acquirerName,
        ROUND(t.amount / 100, 2) AS amount,
        DATE(t.transactionTime) AS transaction_date
    FROM transactions t
    LEFT JOIN mapping_merchant_user_terminal mmut ON t.terminalID = mmut.terminalID
    LEFT JOIN mapping_division_acquirer_tg mdat ON mmut.divisionId = mdat.divisionID
    LEFT JOIN mapping_acquirer_tg mat ON mdat.acqTgId = mat.id
    WHERE t.transactionTime >= CURDATE() - INTERVAL 1 DAY
      AND t.transactionTime < CURDATE()
      AND t.status = 'SUCCESS'
  
  paybylink_query: |
    SELECT 
        pbl.id,
        'PayByLink' AS terminalID,
        'PayByLink' AS transactionTypeName,
        'PayByLink' AS acquirerName,
        ROUND(pbl.amount / 100, 2) AS amount,
        DATE(pbl.created_at) AS transaction_date
    FROM paybylink_transactions pbl
    WHERE pbl.created_at >= CURDATE() - INTERVAL 1 DAY
      AND pbl.created_at < CURDATE()
      AND pbl.status = 'SUCCESS'

# Data transformation
transformation:
  exclude_test_tids: true
  test_tid_file: "/home/<USER>/Reports/Scripts/AllTransactions/Test_TID_List.csv"
  
  groupby_columns: ["transactionTypeName", "acquirerName"]
  aggregations:
    id: "count"
    amount: "sum"
  
  replacements:
    "Cash Withdrawal": "MATM"
  
  reference_file: "/home/<USER>/Reports/Scripts/AllTransactions/AcquirerListTXNWise.csv"

# Output configuration
output:
  file_prefix: "AllTransactions_Summary"
  base_directory: "/home/<USER>/Reports/ALL_TRANSACTIONS"
  
# Email configuration
email:
  subject_template: "ALL TRANSACTIONS SUMMARY till {date}"
  recipients: []  # Uses default recipients if empty
