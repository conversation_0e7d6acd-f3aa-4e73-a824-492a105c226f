# AIRTEL UPI Transaction Report Configuration

# Report metadata
report_name: "AIRTEL_UPI_TRANSACTION"
description: "Daily AIRTEL UPI transaction summary report"

# Scheduling
schedule:
  cron: "0 6 * * *"  # Daily at 6 AM
  timezone: "Asia/Kolkata"

# Data extraction
data_source:
  query: |
    WITH merchant_CTE AS (
        SELECT
            m.id AS merchant_id,
            m.dbaName AS dbaName,
            mat.acquirerName,
            mat.tgName,
            mmut.programId,
            MAX(m.recordCreated) AS InstallationDate,
            COUNT(mmut.terminalID) AS number_of_terminals
        FROM
            mapping_merchant_user_terminal mmut
        RIGHT JOIN merchant m
            ON m.id = mmut.divisionId
        RIGHT JOIN mapping_division_acquirer_tg mdat
            ON mdat.divisionID = m.id
        RIGHT JOIN mapping_acquirer_tg mat
            ON mat.id = mdat.acqTgId
        WHERE
            mat.tgID = 59
            AND mmut.programId = 149
            AND mmut.status = 'A'
            AND m.id NOT IN (
                2862242, 2862114, 2859856, 2873609, 2865236, 2857368, 2867875,
                2881053, 2863847, 2876710, 2855085, 2835920, 2871276, 2858329,
                2855055, 2883234, 2857313, 2876466, 2851410, 2862916, 2882758,
                2849150, 2881869, 2854053
            )
        GROUP BY
            m.id, m.dbaName, mat.acquirerName, mat.tgName, mmut.programId
    )
    
    SELECT
        DATE(t.transactionTime) AS txn_Date,
        merchant_CTE.merchant_id as POS_ID,
        merchant_CTE.dbaName AS ME_Name,
        merchant_CTE.InstallationDate,
        t.id AS txn_id,
        ROUND(t.amount / 100, 2) AS amount
    FROM
        merchant_CTE
    LEFT JOIN transactions t
        ON t.merchantID = merchant_CTE.merchant_id
    WHERE
        t.transactionTime >= DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 DAY), '%Y-%m-01') and        
            t.transactionTime < CURDATE()

# Data transformation
transformation:
  pivot_config:
    index_columns: ["POS_ID", "ME_Name", "InstallationDate"]
    value_column: "amount"
    aggfunc: ["count", "sum"]
    fill_value: 0
  
  date_formatting:
    installation_date_format: "%d/%m/%y"
    transaction_date_format: "%d/%m/%y"

# Output configuration
output:
  file_prefix: "AIRTEL_UPI_TRANSACTION_Summary"
  base_directory: "/home/<USER>/Reports/AIRTEL_UPI_TRANSACTION"
  
# Email configuration
email:
  subject_template: "AIRTEL UPI TRANSACTION SUMMARY till {date}"
  recipients: []  # Uses default recipients if empty
