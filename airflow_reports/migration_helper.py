#!/usr/bin/env python3
"""
Migration Helper Script
Assists in converting legacy report scripts to Airflow DAGs
"""

import os
import re
import yaml
import argparse
from pathlib import Path
from typing import Dict, Any, List


class ReportMigrationHelper:
    """Helper class to migrate legacy reports to Airflow DAGs"""
    
    def __init__(self, scripts_dir: str, airflow_dir: str):
        self.scripts_dir = Path(scripts_dir)
        self.airflow_dir = Path(airflow_dir)
        self.config_dir = self.airflow_dir / "config" / "report_configs"
        self.dags_dir = self.airflow_dir / "dags" / "reports"
    
    def analyze_legacy_report(self, report_name: str) -> Dict[str, Any]:
        """Analyze legacy report structure and extract configuration"""
        report_path = self.scripts_dir / report_name
        
        if not report_path.exists():
            raise ValueError(f"Report directory not found: {report_path}")
        
        analysis = {
            "report_name": report_name,
            "files_found": [],
            "queries": [],
            "email_config": {},
            "output_config": {},
            "dependencies": []
        }
        
        # Analyze files in the report directory
        for file_path in report_path.glob("*.py"):
            analysis["files_found"].append(file_path.name)
            
            if file_path.name == "DataQuery.py":
                analysis.update(self._analyze_data_query(file_path))
            elif file_path.name == "enviroment.py":
                analysis["email_config"] = self._analyze_environment(file_path)
            elif file_path.name == "main.py":
                analysis["dependencies"] = self._analyze_main_dependencies(file_path)
        
        return analysis
    
    def _analyze_data_query(self, file_path: Path) -> Dict[str, Any]:
        """Extract SQL queries and configuration from DataQuery.py"""
        content = file_path.read_text()
        
        # Extract SQL queries
        queries = []
        query_pattern = r'(\w+Query)\s*=\s*"""(.*?)"""'
        matches = re.findall(query_pattern, content, re.DOTALL)
        
        for query_name, query_content in matches:
            queries.append({
                "name": query_name,
                "content": query_content.strip()
            })
        
        # Extract database config
        db_config = {}
        config_pattern = r'config\s*=\s*{(.*?)}'
        config_match = re.search(config_pattern, content, re.DOTALL)
        
        if config_match:
            config_content = config_match.group(1)
            # Simple extraction of key-value pairs
            for line in config_content.split('\n'):
                if ':' in line and '"' in line:
                    key_match = re.search(r'"(\w+)":\s*"([^"]+)"', line)
                    if key_match:
                        db_config[key_match.group(1)] = key_match.group(2)
        
        # Extract output configuration
        output_config = {}
        output_patterns = [
            r'out_dir\s*=\s*"([^"]+)"',
            r'output_file\s*=\s*"([^"]+)"',
            r'(\w+_output)\s*=\s*.*'
        ]
        
        for pattern in output_patterns:
            matches = re.findall(pattern, content)
            if matches:
                output_config["patterns"] = matches
        
        return {
            "queries": queries,
            "database_config": db_config,
            "output_config": output_config
        }
    
    def _analyze_environment(self, file_path: Path) -> Dict[str, Any]:
        """Extract email configuration from enviroment.py"""
        content = file_path.read_text()
        
        email_config = {}
        
        # Extract email settings
        patterns = {
            "from_address": r'From\s*=\s*"([^"]+)"',
            "smtp_server": r'server_name\s*=\s*"([^"]+)"',
            "smtp_port": r'Port\s*=\s*(\d+)',
            "username": r'Username\s*=\s*"([^"]+)"',
            "password": r'Password\s*=\s*"([^"]+)"'
        }
        
        for key, pattern in patterns.items():
            match = re.search(pattern, content)
            if match:
                email_config[key] = match.group(1)
        
        # Extract recipients
        to_pattern = r'to\s*=\s*\[(.*?)\]'
        to_match = re.search(to_pattern, content, re.DOTALL)
        
        if to_match:
            recipients_content = to_match.group(1)
            recipients = re.findall(r'"([^"]+)"', recipients_content)
            email_config["recipients"] = recipients
        
        return email_config
    
    def _analyze_main_dependencies(self, file_path: Path) -> List[str]:
        """Extract dependencies from main.py"""
        content = file_path.read_text()
        
        # Extract import statements
        import_pattern = r'^(?:from|import)\s+(\w+)'
        imports = re.findall(import_pattern, content, re.MULTILINE)
        
        # Filter for relevant dependencies
        relevant_deps = []
        for imp in imports:
            if imp in ['pandas', 'numpy', 'mysql', 'openpyxl', 'xlsxwriter']:
                relevant_deps.append(imp)
        
        return list(set(relevant_deps))
    
    def generate_config_file(self, analysis: Dict[str, Any]) -> str:
        """Generate YAML configuration file from analysis"""
        config = {
            "report_name": analysis["report_name"].upper(),
            "description": f"Migrated {analysis['report_name']} report",
            "schedule": {
                "cron": "0 6 * * *",  # Default to 6 AM daily
                "timezone": "Asia/Kolkata"
            },
            "data_source": {},
            "transformation": {},
            "output": {
                "file_prefix": f"{analysis['report_name']}_Summary",
                "base_directory": f"/home/<USER>/Reports/{analysis['report_name'].upper()}"
            },
            "email": {
                "subject_template": f"{analysis['report_name'].upper()} SUMMARY till {{date}}",
                "recipients": []
            }
        }
        
        # Add queries if found
        if analysis.get("queries"):
            for i, query in enumerate(analysis["queries"]):
                if i == 0:
                    config["data_source"]["query"] = query["content"]
                else:
                    config["data_source"][f"query_{i+1}"] = query["content"]
        
        # Add email recipients if found
        if analysis.get("email_config", {}).get("recipients"):
            config["email"]["recipients"] = analysis["email_config"]["recipients"]
        
        return yaml.dump(config, default_flow_style=False, sort_keys=False)
    
    def generate_dag_template(self, analysis: Dict[str, Any]) -> str:
        """Generate DAG Python file template"""
        report_name = analysis["report_name"]
        class_name = "".join(word.capitalize() for word in report_name.split("_")) + "ReportDAG"
        
        template = f'''"""
{report_name.replace("_", " ").title()} Report DAG
Generated from legacy report migration
"""

import pandas as pd
import os
from datetime import datetime, timedelta
from airflow.utils.dates import days_ago

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.base_report_dag import BaseReportDAG


class {class_name}(BaseReportDAG):
    """{report_name.replace("_", " ").title()} Report DAG implementation"""
    
    def __init__(self):
        super().__init__(
            dag_id='{report_name.lower()}_report',
            report_name='{report_name.lower()}',
            schedule_interval='0 6 * * *',  # Daily at 6 AM
            start_date=days_ago(1),
            catchup=False,
            max_active_runs=1
        )
    
    def extract_data(self, **context) -> pd.DataFrame:
        """Extract {report_name.lower()} data"""
        # TODO: Implement data extraction logic
        # Use self.report_config['data_source']['query'] for SQL query
        query = self.report_config['data_source']['query']
        df = self.db_manager.execute_query(query)
        
        if df is None or df.empty:
            raise ValueError("No data returned from query")
        
        return df
    
    def transform_data(self, data: pd.DataFrame, **context) -> pd.DataFrame:
        """Transform {report_name.lower()} data"""
        # TODO: Implement data transformation logic
        # Add your specific transformation logic here
        
        return data
    
    def generate_report(self, data: pd.DataFrame, **context) -> str:
        """Generate Excel report for {report_name.lower()}"""
        # Use default implementation or override for custom logic
        return super().generate_report(data, **context)
    
    def send_email(self, file_path: str, **context) -> bool:
        """Send email with {report_name.lower()} report"""
        # Use default implementation or override for custom logic
        return super().send_email(file_path, **context)


# Create the DAG instance
{report_name.lower()}_dag_instance = {class_name}()
dag = {report_name.lower()}_dag_instance.get_dag()

# Make the DAG available to Airflow
globals()[dag.dag_id] = dag
'''
        
        return template
    
    def migrate_report(self, report_name: str, dry_run: bool = True) -> Dict[str, str]:
        """Migrate a single report to Airflow"""
        print(f"Migrating report: {report_name}")
        
        # Analyze legacy report
        analysis = self.analyze_legacy_report(report_name)
        
        # Generate configuration file
        config_content = self.generate_config_file(analysis)
        config_file = self.config_dir / f"{report_name.lower()}.yaml"
        
        # Generate DAG file
        dag_content = self.generate_dag_template(analysis)
        dag_file = self.dags_dir / f"{report_name.lower()}_dag.py"
        
        if not dry_run:
            # Create directories if they don't exist
            self.config_dir.mkdir(parents=True, exist_ok=True)
            self.dags_dir.mkdir(parents=True, exist_ok=True)
            
            # Write files
            config_file.write_text(config_content)
            dag_file.write_text(dag_content)
            
            print(f"✅ Created: {config_file}")
            print(f"✅ Created: {dag_file}")
        else:
            print(f"📄 Would create: {config_file}")
            print(f"📄 Would create: {dag_file}")
        
        return {
            "config_file": str(config_file),
            "dag_file": str(dag_file),
            "config_content": config_content,
            "dag_content": dag_content
        }
    
    def list_legacy_reports(self) -> List[str]:
        """List all legacy report directories"""
        reports = []
        for item in self.scripts_dir.iterdir():
            if item.is_dir() and not item.name.startswith('.'):
                reports.append(item.name)
        return sorted(reports)


def main():
    parser = argparse.ArgumentParser(description="Migrate legacy reports to Airflow")
    parser.add_argument("--scripts-dir", default=".", help="Path to legacy scripts directory")
    parser.add_argument("--airflow-dir", default="./airflow_reports", help="Path to Airflow directory")
    parser.add_argument("--report", help="Specific report to migrate")
    parser.add_argument("--list", action="store_true", help="List all available reports")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be created without creating files")
    
    args = parser.parse_args()
    
    helper = ReportMigrationHelper(args.scripts_dir, args.airflow_dir)
    
    if args.list:
        reports = helper.list_legacy_reports()
        print("Available reports:")
        for report in reports:
            print(f"  - {report}")
        return
    
    if args.report:
        try:
            result = helper.migrate_report(args.report, dry_run=args.dry_run)
            print(f"Migration completed for {args.report}")
        except Exception as e:
            print(f"Error migrating {args.report}: {e}")
    else:
        print("Please specify --report or use --list to see available reports")


if __name__ == "__main__":
    main()
