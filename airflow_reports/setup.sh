#!/bin/bash

# Mosambee Reports Airflow Setup Script
# This script sets up the Airflow environment for report automation

set -e

echo "🚀 Setting up Mosambee Reports Airflow Environment"
echo "=================================================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create necessary directories
echo "📁 Creating directory structure..."
mkdir -p logs
mkdir -p plugins
mkdir -p config/report_configs
mkdir -p dags/reports
mkdir -p dags/common

# Set up environment file
echo "🔧 Setting up environment variables..."
if [ ! -f .env ]; then
    echo "AIRFLOW_UID=$(id -u)" > .env
    echo "AIRFLOW_GID=0" >> .env
    echo "_AIRFLOW_WWW_USER_USERNAME=airflow" >> .env
    echo "_AIRFLOW_WWW_USER_PASSWORD=airflow" >> .env
    echo "✅ Created .env file"
else
    echo "ℹ️  .env file already exists"
fi

# Set permissions
echo "🔐 Setting up permissions..."
sudo chown -R $(id -u):$(id -g) .
chmod +x migration_helper.py

# Initialize Airflow database
echo "🗄️  Initializing Airflow database..."
docker-compose up airflow-init

# Start Airflow services
echo "🚀 Starting Airflow services..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 30

# Check if services are running
if docker-compose ps | grep -q "Up"; then
    echo "✅ Airflow services are running!"
    echo ""
    echo "🌐 Access Airflow Web UI at: http://localhost:8080"
    echo "👤 Username: airflow"
    echo "🔑 Password: airflow"
    echo ""
    echo "📊 Available commands:"
    echo "  - View logs: docker-compose logs -f"
    echo "  - Stop services: docker-compose down"
    echo "  - Restart services: docker-compose restart"
    echo ""
    echo "📋 Next steps:"
    echo "  1. Access the Airflow UI at http://localhost:8080"
    echo "  2. Enable the DAGs you want to run"
    echo "  3. Monitor execution in the UI"
    echo ""
    echo "🔧 To migrate legacy reports:"
    echo "  ./migration_helper.py --list"
    echo "  ./migration_helper.py --report REPORT_NAME"
else
    echo "❌ Some services failed to start. Check logs with: docker-compose logs"
    exit 1
fi

echo "🎉 Setup completed successfully!"
