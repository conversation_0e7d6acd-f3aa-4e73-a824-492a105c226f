# Deployment Guide - Mosambee Reports Airflow

This guide provides step-by-step instructions for deploying the standardized Airflow report automation system.

## 📋 Prerequisites

### System Requirements
- **OS**: Linux (Ubuntu 18.04+ recommended)
- **RAM**: Minimum 4GB, Recommended 8GB+
- **CPU**: Minimum 2 cores, Recommended 4+ cores
- **Disk**: Minimum 20GB free space
- **Network**: Access to MySQL database (***********:3331)

### Software Requirements
- Docker 20.10+
- Docker Compose 1.29+
- Python 3.8+
- Git

## 🚀 Installation Steps

### Step 1: Prepare the Environment

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Logout and login again to apply Docker group changes
```

### Step 2: Deploy Airflow Reports

```bash
# Navigate to your reports directory
cd /home/<USER>/Reports/Scripts

# Copy the airflow_reports directory to the target location
sudo cp -r airflow_reports /opt/mosambee-airflow
cd /opt/mosambee-airflow

# Set ownership
sudo chown -R tableauadmin:tableauadmin /opt/mosambee-airflow

# Run setup script
./setup.sh
```

### Step 3: Verify Installation

```bash
# Run the test script
./test_setup.py

# Check if services are running
docker-compose ps

# View logs
docker-compose logs -f airflow-scheduler
```

### Step 4: Access Airflow UI

1. Open browser and navigate to: `http://your-server-ip:8080`
2. Login with:
   - Username: `airflow`
   - Password: `airflow`

## 🔧 Configuration

### Database Configuration

Edit `/opt/mosambee-airflow/config/database_config.yaml`:

```yaml
mysql:
  host: "***********"
  port: 3331
  user: "tableau"
  password: "Tableau@1234$"
  database: "sfn_transaction"
```

### Email Configuration

Edit `/opt/mosambee-airflow/config/email_config.yaml`:

```yaml
from_address: "<EMAIL>"
smtp_server: "*************"
smtp_port: 587
username: "<EMAIL>"
password: "Ms1&iUoOoYgMj"
default_recipients:
  - "<EMAIL>"
  - "<EMAIL>"
```

## 📊 Migrating Existing Reports

### Using the Migration Helper

```bash
# List all available reports
./migration_helper.py --list

# Migrate a specific report (dry run)
./migration_helper.py --report AIRTEL_UPI_TRANSACTION --dry-run

# Migrate a specific report (actual migration)
./migration_helper.py --report AIRTEL_UPI_TRANSACTION

# Migrate from custom scripts directory
./migration_helper.py --scripts-dir /path/to/scripts --report REPORT_NAME
```

### Manual Migration Steps

1. **Create Report Configuration**
   ```bash
   cp config/report_configs/airtel_upi_transaction.yaml config/report_configs/my_report.yaml
   # Edit the configuration file
   ```

2. **Create DAG File**
   ```bash
   cp dags/reports/airtel_upi_dag.py dags/reports/my_report_dag.py
   # Modify the DAG implementation
   ```

3. **Test the DAG**
   ```bash
   # Restart Airflow to pick up new DAGs
   docker-compose restart
   
   # Check DAG in UI
   # Enable and trigger the DAG
   ```

## 🔄 Migration Strategy

### Phase 1: Parallel Operation (Recommended)

1. **Keep existing cron jobs running**
2. **Deploy Airflow alongside existing system**
3. **Migrate reports one by one**
4. **Compare outputs for validation**

```bash
# Example: Migrate AIRTEL_UPI_TRANSACTION
./migration_helper.py --report AIRTEL_UPI_TRANSACTION

# Enable the DAG in Airflow UI
# Run both systems in parallel for 1 week
# Compare outputs daily
```

### Phase 2: Gradual Cutover

1. **Disable cron job for migrated report**
2. **Enable Airflow scheduling**
3. **Monitor for 1 week**
4. **Repeat for next report**

```bash
# Disable cron job
sudo crontab -e
# Comment out the specific report cron job

# Verify Airflow DAG is running correctly
# Monitor logs and email delivery
```

### Phase 3: Full Migration

1. **Migrate all remaining reports**
2. **Disable all legacy cron jobs**
3. **Archive legacy scripts**
4. **Update documentation**

## 🔍 Monitoring and Maintenance

### Daily Monitoring

1. **Check Airflow UI** for failed DAGs
2. **Review email delivery** logs
3. **Monitor disk space** usage
4. **Check database connectivity**

### Weekly Maintenance

1. **Review DAG performance** metrics
2. **Clean up old log files**
3. **Update configurations** if needed
4. **Backup configurations**

### Monthly Maintenance

1. **Update Airflow** to latest stable version
2. **Review and optimize** DAG schedules
3. **Archive old reports** if needed
4. **Performance tuning**

## 🚨 Troubleshooting

### Common Issues

#### DAG Import Errors
```bash
# Check Python path and dependencies
docker-compose exec airflow-scheduler python -c "import sys; print(sys.path)"

# Check DAG syntax
docker-compose exec airflow-scheduler python /opt/airflow/dags/reports/my_dag.py
```

#### Database Connection Issues
```bash
# Test database connectivity
docker-compose exec airflow-scheduler python -c "
from dags.common.database_utils import DatabaseManager
from dags.common.config_manager import ConfigManager
db = DatabaseManager(ConfigManager())
print('Connection test:', db.test_connection())
"
```

#### Email Delivery Issues
```bash
# Check email configuration
docker-compose exec airflow-scheduler python -c "
from dags.common.email_utils import EmailManager
from dags.common.config_manager import ConfigManager
email = EmailManager(ConfigManager())
print('Email config loaded successfully')
"
```

#### Disk Space Issues
```bash
# Clean up old logs
find /opt/mosambee-airflow/logs -name "*.log" -mtime +30 -delete

# Clean up Docker images
docker system prune -f
```

### Log Locations

- **Airflow Logs**: `/opt/mosambee-airflow/logs/`
- **Docker Logs**: `docker-compose logs [service-name]`
- **System Logs**: `/var/log/syslog`

### Emergency Procedures

#### Rollback to Legacy System
```bash
# Stop Airflow
docker-compose down

# Re-enable cron jobs
sudo crontab -e
# Uncomment the required cron jobs

# Restart cron service
sudo service cron restart
```

#### Quick Restart
```bash
# Restart all services
docker-compose restart

# Restart specific service
docker-compose restart airflow-scheduler
```

## 📞 Support

### Internal Support
- **Primary**: <EMAIL>
- **Secondary**: <EMAIL>

### External Resources
- **Airflow Documentation**: https://airflow.apache.org/docs/
- **Docker Documentation**: https://docs.docker.com/

### Escalation Process

1. **Level 1**: Check logs and common issues
2. **Level 2**: Contact internal support team
3. **Level 3**: Engage external Airflow consultants if needed

## 📈 Performance Optimization

### Resource Optimization
- Monitor CPU and memory usage
- Adjust Docker resource limits
- Optimize DAG schedules to avoid conflicts

### Database Optimization
- Use connection pooling
- Optimize SQL queries
- Monitor query performance

### Email Optimization
- Batch email sending
- Use email templates
- Monitor delivery rates

## 🔐 Security Considerations

### Access Control
- Change default Airflow passwords
- Implement RBAC if needed
- Restrict network access

### Data Security
- Encrypt sensitive configurations
- Use environment variables for secrets
- Regular security updates

### Backup Strategy
- Daily configuration backups
- Weekly full system backups
- Test restore procedures monthly
