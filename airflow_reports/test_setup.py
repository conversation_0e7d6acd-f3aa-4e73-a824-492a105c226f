#!/usr/bin/env python3
"""
Test Setup Script
Validates the Airflow setup and configuration
"""

import os
import sys
import yaml
import pandas as pd
from pathlib import Path

# Add the dags directory to Python path
sys.path.append(str(Path(__file__).parent / "dags"))

def test_config_files():
    """Test configuration files are valid"""
    print("🔧 Testing configuration files...")
    
    config_dir = Path(__file__).parent / "config"
    
    # Test database config
    db_config_path = config_dir / "database_config.yaml"
    if db_config_path.exists():
        try:
            with open(db_config_path) as f:
                db_config = yaml.safe_load(f)
            print("✅ Database config is valid")
        except Exception as e:
            print(f"❌ Database config error: {e}")
            return False
    else:
        print("❌ Database config file not found")
        return False
    
    # Test email config
    email_config_path = config_dir / "email_config.yaml"
    if email_config_path.exists():
        try:
            with open(email_config_path) as f:
                email_config = yaml.safe_load(f)
            print("✅ Email config is valid")
        except Exception as e:
            print(f"❌ Email config error: {e}")
            return False
    else:
        print("❌ Email config file not found")
        return False
    
    return True

def test_common_modules():
    """Test common modules can be imported"""
    print("📦 Testing common modules...")
    
    try:
        from common.config_manager import ConfigManager
        print("✅ ConfigManager imported successfully")
    except Exception as e:
        print(f"❌ ConfigManager import error: {e}")
        return False
    
    try:
        from common.database_utils import DatabaseManager
        print("✅ DatabaseManager imported successfully")
    except Exception as e:
        print(f"❌ DatabaseManager import error: {e}")
        return False
    
    try:
        from common.email_utils import EmailManager
        print("✅ EmailManager imported successfully")
    except Exception as e:
        print(f"❌ EmailManager import error: {e}")
        return False
    
    try:
        from common.excel_utils import ExcelManager
        print("✅ ExcelManager imported successfully")
    except Exception as e:
        print(f"❌ ExcelManager import error: {e}")
        return False
    
    try:
        from common.base_report_dag import BaseReportDAG
        print("✅ BaseReportDAG imported successfully")
    except Exception as e:
        print(f"❌ BaseReportDAG import error: {e}")
        return False
    
    return True

def test_config_manager():
    """Test ConfigManager functionality"""
    print("⚙️  Testing ConfigManager...")
    
    try:
        from common.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        
        # Test database config loading
        db_config = config_manager.load_database_config()
        if 'mysql' in db_config:
            print("✅ Database config loaded successfully")
        else:
            print("❌ Database config missing mysql section")
            return False
        
        # Test email config loading
        email_config = config_manager.load_email_config()
        if 'default_recipients' in email_config:
            print("✅ Email config loaded successfully")
        else:
            print("❌ Email config missing default_recipients")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ ConfigManager test error: {e}")
        return False

def test_database_connection():
    """Test database connection (optional - requires actual DB access)"""
    print("🗄️  Testing database connection...")
    
    try:
        from common.config_manager import ConfigManager
        from common.database_utils import DatabaseManager
        
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager)
        
        # Test connection (this will fail if DB is not accessible)
        if db_manager.test_connection():
            print("✅ Database connection successful")
            return True
        else:
            print("⚠️  Database connection failed (this is expected if DB is not accessible)")
            return True  # Don't fail the test for this
            
    except Exception as e:
        print(f"⚠️  Database connection test error: {e} (this is expected if DB is not accessible)")
        return True  # Don't fail the test for this

def test_sample_dag():
    """Test sample DAG can be imported"""
    print("📊 Testing sample DAG...")
    
    try:
        # Test if airtel_upi_dag can be imported
        dag_path = Path(__file__).parent / "dags" / "reports" / "airtel_upi_dag.py"
        if dag_path.exists():
            # Import the DAG module
            import importlib.util
            spec = importlib.util.spec_from_file_location("airtel_upi_dag", dag_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            if hasattr(module, 'dag'):
                print("✅ Sample DAG imported successfully")
                return True
            else:
                print("❌ Sample DAG missing 'dag' attribute")
                return False
        else:
            print("❌ Sample DAG file not found")
            return False
            
    except Exception as e:
        print(f"❌ Sample DAG test error: {e}")
        return False

def test_dependencies():
    """Test required dependencies are available"""
    print("📋 Testing dependencies...")
    
    required_packages = [
        'pandas',
        'mysql.connector',
        'openpyxl',
        'yaml',
        'airflow'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'mysql.connector':
                import mysql.connector
            elif package == 'yaml':
                import yaml
            else:
                __import__(package)
            print(f"✅ {package} is available")
        except ImportError:
            print(f"❌ {package} is missing")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("💡 Install with: pip install -r requirements.txt")
        return False
    
    return True

def test_directory_structure():
    """Test directory structure is correct"""
    print("📁 Testing directory structure...")
    
    base_dir = Path(__file__).parent
    required_dirs = [
        "dags",
        "dags/common",
        "dags/reports",
        "config",
        "config/report_configs",
        "logs",
        "plugins"
    ]
    
    missing_dirs = []
    
    for dir_path in required_dirs:
        full_path = base_dir / dir_path
        if full_path.exists():
            print(f"✅ {dir_path} exists")
        else:
            print(f"❌ {dir_path} missing")
            missing_dirs.append(dir_path)
    
    if missing_dirs:
        print(f"❌ Missing directories: {', '.join(missing_dirs)}")
        return False
    
    return True

def main():
    """Run all tests"""
    print("🧪 Running Airflow Setup Tests")
    print("=" * 50)
    
    tests = [
        ("Directory Structure", test_directory_structure),
        ("Dependencies", test_dependencies),
        ("Configuration Files", test_config_files),
        ("Common Modules", test_common_modules),
        ("ConfigManager", test_config_manager),
        ("Database Connection", test_database_connection),
        ("Sample DAG", test_sample_dag),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your Airflow setup is ready.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
