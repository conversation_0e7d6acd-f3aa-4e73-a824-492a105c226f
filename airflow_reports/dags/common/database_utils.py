"""
Database utilities for Airflow Reports
Standardized database connection and query execution
"""

import pandas as pd
import mysql.connector
import time
import logging
from typing import Dict, Any, Optional
from .config_manager import ConfigManager

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Centralized database connection and query management"""
    
    def __init__(self, config_manager: ConfigManager = None):
        self.config_manager = config_manager or ConfigManager()
        self.db_config = self.config_manager.load_database_config()
    
    def connect_to_mysql(self, attempts: int = 3, delay: int = 2) -> Optional[mysql.connector.MySQLConnection]:
        """
        Establish MySQL connection with retry logic
        
        Args:
            attempts: Number of connection attempts
            delay: Delay between attempts (exponential backoff)
            
        Returns:
            MySQL connection object or None if failed
        """
        attempt = 1
        
        while attempt <= attempts:
            try:
                connection = mysql.connector.connect(**self.db_config['mysql'])
                logger.info(f"Successfully connected to database on attempt {attempt}")
                return connection
            except (mysql.connector.Error, IOError) as err:
                if attempt == attempts:
                    logger.error(f"Failed to connect after {attempts} attempts: {err}")
                    return None
                
                logger.warning(f"Connection failed: {err}. Retrying ({attempt}/{attempts})...")
                time.sleep(delay ** attempt)
                attempt += 1
        
        return None
    
    def execute_query(self, query: str) -> Optional[pd.DataFrame]:
        """
        Execute SQL query and return results as DataFrame
        
        Args:
            query: SQL query string
            
        Returns:
            DataFrame with query results or None if failed
        """
        connection = self.connect_to_mysql()
        
        if not connection or not connection.is_connected():
            logger.error("No database connection available")
            return None
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(query)
                rows = cursor.fetchall()
                columns = cursor.column_names
                
                df = pd.DataFrame(rows, columns=columns)
                logger.info(f"Query executed successfully, returned {len(df)} rows")
                return df
                
        except mysql.connector.Error as err:
            logger.error(f"Error executing query: {err}")
            return None
        finally:
            connection.close()
    
    def test_connection(self) -> bool:
        """Test database connection"""
        connection = self.connect_to_mysql()
        if connection and connection.is_connected():
            connection.close()
            return True
        return False
