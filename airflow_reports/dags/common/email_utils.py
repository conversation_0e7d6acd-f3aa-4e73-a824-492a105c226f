"""
Email utilities for Airflow Reports
Standardized email sending functionality
"""

import os
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from datetime import datetime, timedelta
from typing import List, Optional
import logging
from .config_manager import ConfigManager

logger = logging.getLogger(__name__)


class EmailManager:
    """Centralized email management for reports"""
    
    def __init__(self, config_manager: ConfigManager = None):
        self.config_manager = config_manager or ConfigManager()
        self.email_config = self.config_manager.load_email_config()
    
    def send_report_email(self, 
                         report_name: str,
                         file_path: str,
                         recipients: List[str] = None,
                         subject_template: str = None,
                         body_template: str = None,
                         date_context: datetime = None) -> bool:
        """
        Send report email with attachment
        
        Args:
            report_name: Name of the report
            file_path: Path to the report file
            recipients: List of email recipients (optional, uses config default)
            subject_template: Email subject template (optional)
            body_template: Email body template (optional)
            date_context: Date context for the report (defaults to yesterday)
            
        Returns:
            True if email sent successfully, False otherwise
        """
        try:
            # Use provided date or default to yesterday
            if date_context is None:
                date_context = datetime.now() - timedelta(days=1)
            
            # Get recipients from config if not provided
            if recipients is None:
                recipients = self.email_config['default_recipients']
            
            # Create email message
            msg = MIMEMultipart()
            msg['From'] = self.email_config['from_address']
            msg['To'] = ', '.join(recipients)
            
            # Generate subject
            if subject_template is None:
                subject = f"{report_name.upper()} SUMMARY till {date_context.strftime('%d-%m-%Y')}"
            else:
                subject = subject_template.format(
                    report_name=report_name.upper(),
                    date=date_context.strftime('%d-%m-%Y')
                )
            msg['Subject'] = subject
            
            # Generate email body
            if body_template is None:
                body = self._generate_default_body(report_name, date_context)
            else:
                body = body_template.format(
                    report_name=report_name.upper(),
                    date=date_context.strftime('%d-%m-%Y')
                )
            
            msg.attach(MIMEText(body, 'html'))
            
            # Attach file if it exists
            if os.path.exists(file_path):
                with open(file_path, "rb") as attached_file:
                    part = MIMEApplication(
                        attached_file.read(),
                        Name=os.path.basename(file_path)
                    )
                part['Content-Disposition'] = f'attachment; filename="{os.path.basename(file_path)}"'
                msg.attach(part)
            else:
                logger.warning(f"Attachment file not found: {file_path}")
            
            # Send email
            return self._send_email(msg, recipients)
            
        except Exception as e:
            logger.error(f"Error sending email for {report_name}: {e}")
            return False
    
    def _generate_default_body(self, report_name: str, date_context: datetime) -> str:
        """Generate default email body"""
        return f"""
        <p>Dear Sir/Madam,<br/><br/>
        PFA {report_name.upper()} SUMMARY file Till {date_context.strftime('%d-%m-%Y')}.<br/>
        </p>
        <p>Please contact Mosambee Support Team (<EMAIL>) directly if you have any questions.<br/>
        Thank you!<br/>
        Best Regards,<br/>
        Mosambee Support Team</p>
        <br/>
        <font color="red">Please do not reply to this email as it is auto-generated.</font>
        """
    
    def _send_email(self, msg: MIMEMultipart, recipients: List[str]) -> bool:
        """Send email using SMTP"""
        try:
            server = smtplib.SMTP(
                self.email_config['smtp_server'], 
                self.email_config['smtp_port']
            )
            server.starttls()
            server.login(
                self.email_config['username'], 
                self.email_config['password']
            )
            
            text = msg.as_string()
            server.sendmail(self.email_config['from_address'], recipients, text)
            server.quit()
            
            logger.info(f"Email sent successfully to {recipients}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending email: {e}")
            return False
