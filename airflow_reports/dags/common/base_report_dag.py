"""
Base Report DAG Template
Abstract base class for all report DAGs with common functionality
"""

from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.email import EmailOperator
from airflow.utils.dates import days_ago

import pandas as pd
import logging
import os

from .config_manager import ConfigManager
from .database_utils import DatabaseManager
from .email_utils import EmailManager
from .excel_utils import ExcelManager

logger = logging.getLogger(__name__)


class BaseReportDAG(ABC):
    """Abstract base class for report DAGs"""
    
    def __init__(self, 
                 dag_id: str,
                 report_name: str,
                 schedule_interval: str = "0 6 * * *",  # Daily at 6 AM
                 start_date: datetime = days_ago(1),
                 catchup: bool = False,
                 max_active_runs: int = 1,
                 **kwargs):
        
        self.dag_id = dag_id
        self.report_name = report_name
        self.config_manager = ConfigManager()
        self.db_manager = DatabaseManager(self.config_manager)
        self.email_manager = EmailManager(self.config_manager)
        self.excel_manager = ExcelManager()
        
        # Load report-specific configuration
        try:
            self.report_config = self.config_manager.load_report_config(report_name)
        except FileNotFoundError:
            logger.warning(f"No specific config found for {report_name}, using defaults")
            self.report_config = {}
        
        # Create DAG
        self.dag = DAG(
            dag_id=dag_id,
            default_args={
                'owner': 'mosambee-reports',
                'depends_on_past': False,
                'start_date': start_date,
                'email_on_failure': True,
                'email_on_retry': False,
                'retries': 2,
                'retry_delay': timedelta(minutes=5),
                **kwargs.get('default_args', {})
            },
            description=f'Automated {report_name} report generation',
            schedule_interval=schedule_interval,
            catchup=catchup,
            max_active_runs=max_active_runs,
            tags=['reports', 'mosambee'],
            **{k: v for k, v in kwargs.items() if k != 'default_args'}
        )
        
        self._create_tasks()
    
    def _create_tasks(self):
        """Create standard tasks for the report DAG"""
        
        # Task 1: Extract data
        extract_task = PythonOperator(
            task_id='extract_data',
            python_callable=self._extract_data_wrapper,
            dag=self.dag
        )
        
        # Task 2: Transform data
        transform_task = PythonOperator(
            task_id='transform_data',
            python_callable=self._transform_data_wrapper,
            dag=self.dag
        )
        
        # Task 3: Generate report
        generate_task = PythonOperator(
            task_id='generate_report',
            python_callable=self._generate_report_wrapper,
            dag=self.dag
        )
        
        # Task 4: Send email
        email_task = PythonOperator(
            task_id='send_email',
            python_callable=self._send_email_wrapper,
            dag=self.dag
        )
        
        # Set task dependencies
        extract_task >> transform_task >> generate_task >> email_task
    
    def _extract_data_wrapper(self, **context):
        """Wrapper for data extraction with error handling"""
        try:
            logger.info(f"Starting data extraction for {self.report_name}")
            data = self.extract_data(**context)
            
            if data is None or data.empty:
                raise ValueError("No data extracted")
            
            logger.info(f"Extracted {len(data)} rows for {self.report_name}")
            return data.to_json()  # Serialize for XCom
            
        except Exception as e:
            logger.error(f"Data extraction failed for {self.report_name}: {e}")
            raise
    
    def _transform_data_wrapper(self, **context):
        """Wrapper for data transformation with error handling"""
        try:
            # Get data from previous task
            ti = context['ti']
            data_json = ti.xcom_pull(task_ids='extract_data')
            data = pd.read_json(data_json)
            
            logger.info(f"Starting data transformation for {self.report_name}")
            transformed_data = self.transform_data(data, **context)
            
            logger.info(f"Transformed data for {self.report_name}")
            return transformed_data.to_json()  # Serialize for XCom
            
        except Exception as e:
            logger.error(f"Data transformation failed for {self.report_name}: {e}")
            raise
    
    def _generate_report_wrapper(self, **context):
        """Wrapper for report generation with error handling"""
        try:
            # Get data from previous task
            ti = context['ti']
            data_json = ti.xcom_pull(task_ids='transform_data')
            data = pd.read_json(data_json)
            
            logger.info(f"Starting report generation for {self.report_name}")
            file_path = self.generate_report(data, **context)
            
            logger.info(f"Generated report: {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"Report generation failed for {self.report_name}: {e}")
            raise
    
    def _send_email_wrapper(self, **context):
        """Wrapper for email sending with error handling"""
        try:
            # Get file path from previous task
            ti = context['ti']
            file_path = ti.xcom_pull(task_ids='generate_report')
            
            logger.info(f"Sending email for {self.report_name}")
            success = self.send_email(file_path, **context)
            
            if not success:
                raise ValueError("Email sending failed")
            
            logger.info(f"Email sent successfully for {self.report_name}")
            
        except Exception as e:
            logger.error(f"Email sending failed for {self.report_name}: {e}")
            raise
    
    @abstractmethod
    def extract_data(self, **context) -> pd.DataFrame:
        """Extract data for the report - must be implemented by subclasses"""
        pass
    
    @abstractmethod
    def transform_data(self, data: pd.DataFrame, **context) -> pd.DataFrame:
        """Transform data for the report - must be implemented by subclasses"""
        pass
    
    def generate_report(self, data: pd.DataFrame, **context) -> str:
        """Generate Excel report - can be overridden by subclasses"""
        # Get output directory
        output_dir = self.config_manager.get_output_directory(self.report_name)
        date_dir = self.excel_manager.create_output_directory(output_dir)
        
        # Generate filename
        filename = self.excel_manager.generate_filename(self.report_name)
        file_path = os.path.join(date_dir, filename)
        
        # Save to Excel
        success = self.excel_manager.save_dataframe_to_excel(data, file_path)
        
        if not success:
            raise ValueError(f"Failed to save report to {file_path}")
        
        return file_path
    
    def send_email(self, file_path: str, **context) -> bool:
        """Send email with report - can be overridden by subclasses"""
        return self.email_manager.send_report_email(
            report_name=self.report_name,
            file_path=file_path
        )
    
    def get_dag(self) -> DAG:
        """Return the DAG object"""
        return self.dag
