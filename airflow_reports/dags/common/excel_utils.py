"""
Excel utilities for Airflow Reports
Standardized Excel processing and formatting
"""

import openpyxl
from openpyxl.utils import get_column_letter
import pandas as pd
import os
from datetime import datetime, timedelta
from typing import Optional
import logging

logger = logging.getLogger(__name__)


class ExcelManager:
    """Centralized Excel processing and formatting"""
    
    @staticmethod
    def auto_fit_columns(file_path: str) -> bool:
        """
        Auto-fit column widths in Excel file
        
        Args:
            file_path: Path to Excel file
            
        Returns:
            True if successful, False otherwise
        """
        try:
            workbook = openpyxl.load_workbook(file_path)
            
            for worksheet in workbook.worksheets:
                ExcelManager._fit_column_widths_for_sheet(worksheet)
            
            workbook.save(file_path)
            logger.info(f"Auto-fitted columns for {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error auto-fitting columns for {file_path}: {e}")
            return False
    
    @staticmethod
    def _fit_column_widths_for_sheet(worksheet):
        """Fit column widths for a single worksheet"""
        for column in worksheet.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            
            for cell in column:
                if cell.value is not None:
                    # Skip formulas to avoid very wide columns
                    if not str(cell.value).startswith('='):
                        max_length = max(max_length, len(str(cell.value)))
            
            # Set column width with some padding
            adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    @staticmethod
    def create_output_directory(output_path: str) -> str:
        """
        Create output directory structure based on date
        
        Args:
            output_path: Base output path
            
        Returns:
            Full path to the created directory
        """
        yesterday = datetime.now() - timedelta(days=1)
        date_folder = yesterday.strftime("%d%m%Y")
        full_path = os.path.join(output_path, date_folder)
        
        os.makedirs(full_path, exist_ok=True)
        logger.info(f"Created output directory: {full_path}")
        return full_path
    
    @staticmethod
    def generate_filename(report_name: str, date_context: datetime = None, extension: str = "xlsx") -> str:
        """
        Generate standardized filename for reports
        
        Args:
            report_name: Name of the report
            date_context: Date context (defaults to yesterday)
            extension: File extension
            
        Returns:
            Generated filename
        """
        if date_context is None:
            date_context = datetime.now() - timedelta(days=1)
        
        date_str = date_context.strftime("%Y%m%d")
        return f"{report_name}_Summary{date_str}.{extension}"
    
    @staticmethod
    def save_dataframe_to_excel(df: pd.DataFrame, 
                               file_path: str, 
                               sheet_name: str = "Sheet1",
                               auto_fit: bool = True) -> bool:
        """
        Save DataFrame to Excel with optional auto-fitting
        
        Args:
            df: DataFrame to save
            file_path: Output file path
            sheet_name: Excel sheet name
            auto_fit: Whether to auto-fit columns
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # Save DataFrame to Excel
            df.to_excel(file_path, sheet_name=sheet_name, index=False)
            
            # Auto-fit columns if requested
            if auto_fit:
                ExcelManager.auto_fit_columns(file_path)
            
            logger.info(f"Saved DataFrame to {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving DataFrame to {file_path}: {e}")
            return False
    
    @staticmethod
    def format_int_with_commas(x) -> str:
        """Format integer with commas as thousand separators"""
        if pd.isna(x):
            return ""
        return f"{int(x):,}"
