"""
Configuration Manager for Airflow Reports
Handles loading and managing configurations from YAML files
"""

import os
import yaml
from typing import Dict, Any
from pathlib import Path


class ConfigManager:
    """Centralized configuration management for report DAGs"""
    
    def __init__(self, config_dir: str = None):
        if config_dir is None:
            # Default to config directory relative to this file
            self.config_dir = Path(__file__).parent.parent.parent / "config"
        else:
            self.config_dir = Path(config_dir)
    
    def load_database_config(self) -> Dict[str, Any]:
        """Load database configuration"""
        config_path = self.config_dir / "database_config.yaml"
        return self._load_yaml_file(config_path)
    
    def load_email_config(self) -> Dict[str, Any]:
        """Load email configuration"""
        config_path = self.config_dir / "email_config.yaml"
        return self._load_yaml_file(config_path)
    
    def load_report_config(self, report_name: str) -> Dict[str, Any]:
        """Load specific report configuration"""
        config_path = self.config_dir / "report_configs" / f"{report_name}.yaml"
        return self._load_yaml_file(config_path)
    
    def _load_yaml_file(self, file_path: Path) -> Dict[str, Any]:
        """Load YAML file and return as dictionary"""
        try:
            with open(file_path, 'r') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            raise FileNotFoundError(f"Configuration file not found: {file_path}")
        except yaml.YAMLError as e:
            raise ValueError(f"Error parsing YAML file {file_path}: {e}")
    
    def get_output_directory(self, report_name: str) -> str:
        """Get output directory for a specific report"""
        base_dir = "/home/<USER>/Reports"
        return os.path.join(base_dir, report_name.upper())
    
    def get_script_directory(self, report_name: str) -> str:
        """Get script directory for a specific report"""
        base_dir = "/home/<USER>/Reports/Scripts"
        return os.path.join(base_dir, report_name)
