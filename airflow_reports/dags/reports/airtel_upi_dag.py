"""
AIRTEL UPI Transaction Report DAG
Generates daily AIRTEL UPI transaction summary reports
"""

import pandas as pd
import os
from datetime import datetime, timedelta
from airflow.utils.dates import days_ago

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.base_report_dag import BaseReportDAG


class AirtelUpiReportDAG(BaseReportDAG):
    """AIRTEL UPI Transaction Report DAG implementation"""
    
    def __init__(self):
        super().__init__(
            dag_id='airtel_upi_transaction_report',
            report_name='airtel_upi_transaction',
            schedule_interval='0 6 * * *',  # Daily at 6 AM
            start_date=days_ago(1),
            catchup=False,
            max_active_runs=1
        )
    
    def extract_data(self, **context) -> pd.DataFrame:
        """Extract AIRTEL UPI transaction data"""
        query = self.report_config['data_source']['query']
        
        # Execute query using database manager
        df = self.db_manager.execute_query(query)
        
        if df is None or df.empty:
            raise ValueError("No data returned from query")
        
        return df
    
    def transform_data(self, data: pd.DataFrame, **context) -> pd.DataFrame:
        """Transform AIRTEL UPI transaction data"""
        # Convert date columns
        data['txn_Date'] = pd.to_datetime(data['txn_Date'])
        data['InstallationDate'] = pd.to_datetime(data['InstallationDate'])
        
        # Format Installation Date
        date_format = self.report_config['transformation']['date_formatting']['installation_date_format']
        data['InstallationDate'] = data['InstallationDate'].dt.strftime(date_format)
        
        # Create pivot table
        pivot_config = self.report_config['transformation']['pivot_config']
        
        pivot = pd.pivot_table(
            data,
            index=pivot_config['index_columns'],
            columns=data['txn_Date'].dt.strftime(
                self.report_config['transformation']['date_formatting']['transaction_date_format']
            ),
            values=pivot_config['value_column'],
            aggfunc=pivot_config['aggfunc'],
            fill_value=pivot_config['fill_value']
        )
        
        # Sort columns by date (level 1)
        pivot.columns = pivot.columns.swaplevel(0, 1)
        pivot.sort_index(axis=1, level=0, inplace=True)
        
        # Rename aggregation function labels
        pivot.columns = pivot.columns.set_levels(
            pivot.columns.levels[1].to_series().replace({
                'count': 'Txn_Count', 
                'sum': 'Amount'
            }).values,
            level=1
        )
        
        return pivot
    
    def generate_report(self, data: pd.DataFrame, **context) -> str:
        """Generate Excel report for AIRTEL UPI transactions"""
        # Get output configuration
        output_config = self.report_config['output']
        
        # Create output directory
        base_dir = output_config['base_directory']
        date_dir = self.excel_manager.create_output_directory(base_dir)
        
        # Generate filename
        yesterday = datetime.now() - timedelta(days=1)
        filename = f"{output_config['file_prefix']}{yesterday.strftime('%Y%m%d')}.xlsx"
        file_path = os.path.join(date_dir, filename)
        
        # Save to Excel
        data.to_excel(file_path)
        
        # Auto-fit columns
        self.excel_manager.auto_fit_columns(file_path)
        
        return file_path
    
    def send_email(self, file_path: str, **context) -> bool:
        """Send email with AIRTEL UPI report"""
        email_config = self.report_config.get('email', {})
        
        # Get recipients (use config-specific or default)
        recipients = email_config.get('recipients')
        if not recipients:  # Empty list or None
            recipients = None  # Will use default recipients
        
        # Get subject template
        subject_template = email_config.get('subject_template')
        
        return self.email_manager.send_report_email(
            report_name=self.report_name.upper().replace('_', ' '),
            file_path=file_path,
            recipients=recipients,
            subject_template=subject_template
        )


# Create the DAG instance
airtel_upi_dag_instance = AirtelUpiReportDAG()
dag = airtel_upi_dag_instance.get_dag()

# Make the DAG available to Airflow
globals()[dag.dag_id] = dag
