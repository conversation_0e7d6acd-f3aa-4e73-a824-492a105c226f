# Mosambee Reports Airflow Standardization

This project standardizes the Mosambee report automation system using Apache Airflow, providing a centralized, scalable, and maintainable solution for all report generation tasks.

## 🏗️ Architecture Overview

### Current Structure (Legacy)
```
Scripts/
├── AIRTEL_UPI_TRANSACTION/
├── AllTransactions/
├── HDFC_64Series/
└── [50+ other report folders]
```

### New Airflow Structure
```
airflow_reports/
├── dags/
│   ├── common/           # Shared utilities
│   └── reports/          # Individual report DAGs
├── config/               # Configuration files
├── plugins/              # Custom Airflow plugins
└── docker-compose.yml    # Docker setup
```

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- Python 3.8+
- Access to MySQL database (***********:3331)

### Setup Instructions

1. **Clone and Navigate**
   ```bash
   cd airflow_reports
   ```

2. **Set Environment Variables**
   ```bash
   echo -e "AIRFLOW_UID=$(id -u)" > .env
   ```

3. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Start Airflow**
   ```bash
   docker-compose up -d
   ```

5. **Access Airflow UI**
   - URL: http://localhost:8080
   - Username: airflow
   - Password: airflow

## 📋 Features

### ✅ Standardized Components
- **Database Connections**: Centralized MySQL connection management
- **Email Service**: Unified email sending with templates
- **Excel Processing**: Automated formatting and column fitting
- **Configuration Management**: YAML-based configuration system
- **Error Handling**: Comprehensive logging and retry mechanisms

### ✅ Report DAGs
- **Base Template**: Abstract class for consistent DAG structure
- **Individual DAGs**: Specific implementations for each report
- **Scheduling**: Configurable cron-based scheduling
- **Dependencies**: Clear task dependencies and data flow

### ✅ Monitoring & Alerting
- **Airflow UI**: Visual DAG monitoring and execution history
- **Email Alerts**: Automatic failure notifications
- **Logging**: Centralized logging with different levels
- **Retry Logic**: Configurable retry attempts with exponential backoff

## 📁 Directory Structure

### `/dags/common/`
- `base_report_dag.py`: Abstract base class for all reports
- `config_manager.py`: Configuration loading and management
- `database_utils.py`: Database connection and query execution
- `email_utils.py`: Email sending functionality
- `excel_utils.py`: Excel processing and formatting

### `/dags/reports/`
- `airtel_upi_dag.py`: AIRTEL UPI transaction report
- `all_transactions_dag.py`: All transactions summary report
- `[other_report_dags].py`: Additional report implementations

### `/config/`
- `database_config.yaml`: Database connection settings
- `email_config.yaml`: Email server and recipient configuration
- `report_configs/`: Individual report configurations

## 🔧 Configuration

### Database Configuration (`config/database_config.yaml`)
```yaml
mysql:
  host: "***********"
  port: 3331
  user: "tableau"
  password: "Tableau@1234$"
  database: "sfn_transaction"
```

### Email Configuration (`config/email_config.yaml`)
```yaml
from_address: "<EMAIL>"
smtp_server: "*************"
smtp_port: 587
default_recipients:
  - "<EMAIL>"
  - "<EMAIL>"
```

### Report Configuration Example
```yaml
report_name: "AIRTEL_UPI_TRANSACTION"
schedule:
  cron: "0 6 * * *"  # Daily at 6 AM
data_source:
  query: "SELECT ..."
output:
  base_directory: "/home/<USER>/Reports/AIRTEL_UPI_TRANSACTION"
```

## 📊 Creating New Reports

### 1. Create Configuration File
```bash
# Create config/report_configs/my_new_report.yaml
```

### 2. Implement DAG Class
```python
class MyNewReportDAG(BaseReportDAG):
    def extract_data(self, **context):
        # Implement data extraction
        pass
    
    def transform_data(self, data, **context):
        # Implement data transformation
        pass
```

### 3. Register DAG
```python
# Create dags/reports/my_new_report_dag.py
dag_instance = MyNewReportDAG()
dag = dag_instance.get_dag()
globals()[dag.dag_id] = dag
```

## 🔄 Migration Strategy

### Phase 1: Parallel Operation
- Run both legacy scripts and Airflow DAGs
- Compare outputs for validation
- Gradually migrate reports one by one

### Phase 2: Full Migration
- Disable legacy cron jobs
- Enable Airflow scheduling
- Monitor and optimize performance

### Phase 3: Cleanup
- Remove legacy script directories
- Archive old configurations
- Update documentation

## 🛠️ Maintenance

### Adding New Recipients
Edit `config/email_config.yaml`:
```yaml
default_recipients:
  - "<EMAIL>"
```

### Modifying Schedules
Edit individual report configs:
```yaml
schedule:
  cron: "0 8 * * *"  # Change to 8 AM
```

### Database Changes
Update `config/database_config.yaml` and restart Airflow.

## 🐛 Troubleshooting

### Common Issues

1. **DAG Import Errors**
   - Check Python path in DAG files
   - Verify all dependencies are installed

2. **Database Connection Failures**
   - Verify database credentials in config
   - Check network connectivity

3. **Email Sending Issues**
   - Validate SMTP settings
   - Check recipient email addresses

### Logs Location
- Airflow logs: `logs/` directory
- Docker logs: `docker-compose logs airflow-scheduler`

## 📈 Benefits

### ✅ Standardization
- Consistent code structure across all reports
- Unified configuration management
- Standardized error handling and logging

### ✅ Scalability
- Easy addition of new reports
- Centralized resource management
- Horizontal scaling capabilities

### ✅ Maintainability
- Single codebase for common functionality
- Version-controlled configurations
- Clear separation of concerns

### ✅ Monitoring
- Visual DAG execution monitoring
- Comprehensive logging and alerting
- Performance metrics and optimization

## 🤝 Contributing

1. Create feature branch
2. Implement changes following existing patterns
3. Test thoroughly with sample data
4. Update documentation
5. Submit pull request

## 📞 Support

For questions or issues:
- Email: <EMAIL>
- Internal: Mosambee Support Team
