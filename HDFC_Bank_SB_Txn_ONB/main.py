from datetime import datetime,timedelta
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import config,input_file,output_file,format_int_with_commas,date_serial_number,Summary_query,TID_Transecting,TID_Setup_Summary_query,TID_INST_Summary_query
import XLSXAutoFitColumns

pd.options.display.float_format = '{:,.2f}'.format

# Data extraction from SQL
# Data extraction from SQL
TID_Setup = data_retrival(TID_Setup_Summary_query)
TID_INST = data_retrival(TID_INST_Summary_query)
HDFC_SB = data_retrival(Summary_query)
TID_FTD = data_retrival(TID_Transecting)

HDFC_SB.drop_duplicates(subset=['transactionID'],inplace=True,ignore_index=True)
HDFC_SB = HDFC_SB.groupby(['transactionTime','programId','program_name']).aggregate({'amount':'sum','tipAmount':'sum','additionalAmount':'sum','authAmount':'sum','transactionID':'count','terminalID':'nunique'}).reset_index()
HDFC_SB = HDFC_SB.rename({
    "transactionTime":"transaction_date",
    "amount":'total_amount',
    "tipAmount":"total_tipAmount",
    "additionalAmount":"total_additionalAmount",
    "authAmount":"total_authAmount",
    "transactionID":"total_transactions",
    "terminalID":"unique_terminals"
},axis=1)
#HDFC_SB.drop_duplicates(subset=['transactionID'],inplace=True,ignore_index=True)
# Reading Historical Data
# Reading Historical Data
hist_summary = pd.read_excel(input_file,sheet_name='Summary',parse_dates=False)
HIST_LTD = pd.read_excel(input_file,sheet_name="LTD")
nonInstaTID_MTD = pd.read_excel(input_file,sheet_name="Non_InstaTransactingTIDMTD")
nonInstaTID_LTD = pd.read_excel(input_file,sheet_name="Non_InstaTransactingTIDLTD")
#InstaTID_MTD = pd.read_excel(input_file,sheet_name="InstaTransactingTIDMTD")
#InstaTID_LTD = pd.read_excel(input_file,sheet_name="InstaTransactingTIDLTD")
InstaTID_LTD = pd.read_csv("/home/<USER>/Reports/Scripts/HDFC_Bank_SB_Txn_ONB/InstaTID_LTD.csv")
InstaTID_MTD =  pd.read_csv('/home/<USER>/Reports/Scripts/HDFC_Bank_SB_Txn_ONB/InstaTID_MTD.csv')
# Preparing Currunt Month Summary
# Preparing Currunt Month Summary
insta = HDFC_SB[HDFC_SB['programId']==95].reset_index(drop=True)
Non_insta = HDFC_SB[HDFC_SB['programId']==37].reset_index(drop=True)
#hist_summary['Date'] = hist_summary['Date'].map(date_serial_number)
#hist_summary['Month'] = hist_summary['Month'].map(date_serial_number)
#hist_summary['Month'] = hist_summary['Month'].dt.strftime('%b-%y')
#hist_summary['Date'] = hist_summary['Date'].dt.strftime('%d-%m-%Y')
insta_Today = pd.DataFrame({ "Date":(datetime.now() - timedelta(days=1)).strftime('%d-%m-%Y'),
               "Month":(datetime.now() - timedelta(days=1)).strftime('%b-%y'),
               "TID_Setup":0 if TID_Setup[TID_Setup['programId']==95].shape[0] == 0 else TID_Setup[TID_Setup['programId']==95].reset_index(drop=True)['TID_Setup'],
               "TID _Inst":0 if TID_INST[TID_INST['programId']==95].shape[0] == 0 else TID_INST[TID_INST['programId']==95].reset_index(drop=True)['TID_Setup'],
                "Txn_Count":insta['total_transactions'],
               "Txn_Amount" : round(insta['total_amount'][0]+insta['total_tipAmount'][0]+insta['total_additionalAmount'][0]+int(insta['total_authAmount'][0]),2)   
        })

Non_insta_Today = pd.DataFrame({ "Date":(datetime.now() - timedelta(days=1)).strftime('%d-%m-%Y'),
               "Month":(datetime.now() - timedelta(days=1)).strftime('%b-%y'),
               "TID_Setup":0 if TID_Setup[TID_Setup['programId']==37].shape[0] == 0 else TID_Setup[TID_Setup['programId']==37].reset_index(drop=True)['TID_Setup'],
               "TID _Inst":0 if TID_INST[TID_INST['programId']==37].shape[0] == 0 else TID_INST[TID_INST['programId']==37].reset_index(drop=True)['TID_Setup'],
                "Txn_Count":Non_insta['total_transactions'],
               "Txn_Amount" : round(Non_insta['total_amount'][0]+Non_insta['total_tipAmount'][0]+Non_insta['total_additionalAmount'][0]+int(Non_insta['total_authAmount'][0]),2)   
        })

overAll = pd.DataFrame({ "Date":(datetime.now() - timedelta(days=1)).strftime('%d-%m-%Y'),
               "Month":(datetime.now() - timedelta(days=1)).strftime('%b-%y'),
               "TID_Setup":insta_Today["TID_Setup"]+Non_insta_Today["TID_Setup"],
               "TID _Inst":insta_Today["TID _Inst"]+Non_insta_Today["TID _Inst"],
                "Txn_Count":insta_Today["Txn_Count"]+Non_insta_Today["Txn_Count"],
               "Txn_Amount" : insta_Today["Txn_Amount"]+Non_insta_Today["Txn_Amount"]})

Non_insta_Today = Non_insta_Today.rename(columns={
    'TID_Setup':'TID Setup-Non Insta SB', 
    'TID _Inst':'TID (Inst)-Non Insta SB', 
    'Txn_Count': 'Txn Count-Non Insta SB', 
    'Txn_Amount':'Txn Amount-Non Insta SB'
})

insta_Today = insta_Today.rename(columns={
    'TID_Setup':'TID Setup-Insta SB', 
    'TID _Inst':'TID (Inst)-Insta SB', 
    'Txn_Count':'Txn Count-Insta SB', 
    'Txn_Amount':'Txn Amount-Insta SB'
})

overAll = overAll.rename(columns={
    'TID_Setup':'TID Setup-Overall SB', 
    'TID _Inst':'TID (Inst)-Overall SB', 
    'Txn_Count':'Txn Count-Overall SB', 
    'Txn_Amount':'Txn Amount-Overall SB'
})
Todays_data = pd.concat([Non_insta_Today, insta_Today.iloc[:,2:],overAll.iloc[:,2:]],axis=1)
curr_month = pd.concat([hist_summary,Todays_data]).reset_index(drop=True)
MTD = pd.DataFrame({
    'Date' : 'Total MTD',
    'Month':(datetime.now() - timedelta(days=1)).strftime("%b'%y"),
    'TID Setup-Non Insta SB': curr_month['TID Setup-Non Insta SB'].sum(), 
    'TID (Inst)-Non Insta SB': curr_month['TID (Inst)-Non Insta SB'].sum(), 
    'Txn Count-Non Insta SB': curr_month['Txn Count-Non Insta SB'].sum(), 
    'Txn Amount-Non Insta SB': curr_month['Txn Amount-Non Insta SB'].sum(),
    'TID Setup-Insta SB': curr_month['TID Setup-Insta SB'].sum(), 
    'TID (Inst)-Insta SB': curr_month['TID (Inst)-Insta SB'].sum(), 
    'Txn Count-Insta SB': curr_month['Txn Count-Insta SB'].sum(), 
    'Txn Amount-Insta SB': curr_month['Txn Amount-Insta SB'].sum(),
    'TID Setup-Overall SB': curr_month['TID Setup-Overall SB'].sum(), 
    'TID (Inst)-Overall SB': curr_month['TID (Inst)-Overall SB'].sum(), 
    'Txn Count-Overall SB': curr_month['Txn Count-Overall SB'].sum(), 
    'Txn Amount-Overall SB': curr_month['Txn Amount-Overall SB'].sum()
},index=[0])

# Preparing LTD Summary
if HIST_LTD['Month'][HIST_LTD.shape[0]-1] == MTD['Month'][0]:
    HIST_LTD = pd.concat([HIST_LTD.iloc[:-1,:],MTD.iloc[:,1:]]).reset_index(drop=True)
else:
    HIST_LTD = pd.concat([HIST_LTD,MTD.iloc[:,1:]]).reset_index(drop=True)

LTD =  pd.DataFrame({
    'Date' : 'Total LTD',
    'Month':"Total LTD",
    'TID Setup-Non Insta SB': HIST_LTD['TID Setup-Non Insta SB'].sum(), 
    'TID (Inst)-Non Insta SB': HIST_LTD['TID (Inst)-Non Insta SB'].sum(), 
    'Txn Count-Non Insta SB': HIST_LTD['Txn Count-Non Insta SB'].sum(), 
    'Txn Amount-Non Insta SB': HIST_LTD['Txn Amount-Non Insta SB'].sum(),
    'TID Setup-Insta SB': HIST_LTD['TID Setup-Insta SB'].sum(), 
    'TID (Inst)-Insta SB': HIST_LTD['TID (Inst)-Insta SB'].sum(), 
    'Txn Count-Insta SB': HIST_LTD['Txn Count-Insta SB'].sum(), 
    'Txn Amount-Insta SB': HIST_LTD['Txn Amount-Insta SB'].sum(),
    'TID Setup-Overall SB': HIST_LTD['TID Setup-Overall SB'].sum(), 
    'TID (Inst)-Overall SB': HIST_LTD['TID (Inst)-Overall SB'].sum(), 
    'Txn Count-Overall SB': HIST_LTD['Txn Count-Overall SB'].sum(), 
    'Txn Amount-Overall SB': HIST_LTD['Txn Amount-Overall SB'].sum()
},index=[0])

LTD_Final = pd.concat([HIST_LTD,LTD.iloc[:,1:]]).reset_index(drop=True)
Final_Summary = pd.concat([curr_month,MTD,LTD]).reset_index(drop=True)
# Preping Unique TID
# Preping Unique TID
TID_FTD = TID_FTD.drop_duplicates(keep='last').reset_index(drop=True)
NonInstaTID_FTD=TID_FTD[TID_FTD['programId']==37].reset_index(drop=True).astype({"terminalID":"int64"})
NewMTD_TID = NonInstaTID_FTD[~NonInstaTID_FTD['terminalID'].isin(nonInstaTID_MTD['terminalID'])].reset_index(drop=True)
NewLTD_TID = NonInstaTID_FTD[~NonInstaTID_FTD['terminalID'].isin(nonInstaTID_LTD['terminalID'])].reset_index(drop=True)
NonInstaTID_MTD = pd.concat([nonInstaTID_MTD,NewMTD_TID]).reset_index(drop=True)
NonInstaTID_LTD = pd.concat([nonInstaTID_LTD,NewLTD_TID]).reset_index(drop=True)
transecting_TID_nonInsta = pd.DataFrame({
    'FTD-Non Insta SB' : NonInstaTID_FTD.shape[0],
    'MTD-Non Insta SB' : NonInstaTID_MTD.shape[0],
    'LTD-Non Insta SB' : NonInstaTID_LTD.shape[0],
},index=[0]
)

InstaTID_FTD=TID_FTD[TID_FTD['programId']==95].reset_index(drop=True).astype({"terminalID":"int64"})
NewMTD_TID = InstaTID_FTD[~InstaTID_FTD['terminalID'].isin(nonInstaTID_MTD['terminalID'])].reset_index(drop=True)
NewLTD_TID = InstaTID_FTD[~InstaTID_FTD['terminalID'].isin(nonInstaTID_LTD['terminalID'])].reset_index(drop=True)
InstaTID_MTD = pd.concat([InstaTID_MTD,NewMTD_TID]).reset_index(drop=True)
InstaTID_LTD = pd.concat([InstaTID_LTD,NewLTD_TID]).reset_index(drop=True)
transecting_TID_Insta = pd.DataFrame({
    'FTD-Insta SB' : InstaTID_FTD.shape[0],
    'MTD-Insta SB' : InstaTID_MTD.shape[0],
    'LTD-Insta SB' : InstaTID_LTD.shape[0],
},index=[0]
)

transecting_TID_overAll = pd.DataFrame({
    'FTD-Overall SB' : transecting_TID_nonInsta['FTD-Non Insta SB']+transecting_TID_Insta['FTD-Insta SB'],
    'MTD-Overall SB' : transecting_TID_nonInsta['MTD-Non Insta SB']+transecting_TID_Insta['MTD-Insta SB'],
    'LTD-Overall SB' : transecting_TID_nonInsta['LTD-Non Insta SB']+transecting_TID_Insta['LTD-Insta SB'],
},index=[0]
)

transecting_TID = pd.concat([transecting_TID_nonInsta,transecting_TID_Insta,transecting_TID_overAll],axis=1)
if datetime.now().strftime("%b'%y") == (datetime.now() - timedelta(days=1)).strftime("%b'%y"):
    pass
else:
    NonInstaTID_MTD = NonInstaTID_MTD[0:0]
    InstaTID_MTD = InstaTID_MTD[0:0]
    curr_month = curr_month[0:0]

with pd.ExcelWriter(input_file,mode='a',engine='openpyxl',if_sheet_exists="replace") as writer:
    NonInstaTID_MTD.to_excel(writer,sheet_name='Non_InstaTransactingTIDMTD',index=False)
    #InstaTID_MTD.to_excel(writer,sheet_name='InstaTransactingTIDMTD',index=False)
    NonInstaTID_LTD.to_excel(writer,sheet_name='Non_InstaTransactingTIDLTD',index=False)
    #InstaTID_LTD.to_excel(writer,sheet_name='InstaTransactingTIDLTD',index=False)
    curr_month.to_excel(writer,sheet_name='Summary',index=False)
    HIST_LTD.to_excel(writer,sheet_name='LTD',index=False)
InstaTID_MTD.to_csv('/home/<USER>/Reports/Scripts/HDFC_Bank_SB_Txn_ONB/InstaTID_MTD.csv',index=False)
InstaTID_LTD.to_csv('/home/<USER>/Reports/Scripts/HDFC_Bank_SB_Txn_ONB/InstaTID_LTD.csv',index=False)
with pd.ExcelWriter(output_file) as writer:
    Final_Summary["Txn Amount-Non Insta SB"] = Final_Summary["Txn Amount-Non Insta SB"].round(2)
    Final_Summary["Txn Amount-Non Insta SB"] = Final_Summary["Txn Amount-Non Insta SB"].map(format_int_with_commas)
    Final_Summary["Txn Amount-Insta SB"] = Final_Summary["Txn Amount-Insta SB"].round(2)
    Final_Summary["Txn Amount-Insta SB"] = Final_Summary["Txn Amount-Insta SB"].map(format_int_with_commas)
    Final_Summary["Txn Amount-Overall SB"] = Final_Summary["Txn Amount-Overall SB"].round(2)
    Final_Summary["Txn Amount-Overall SB"] = Final_Summary["Txn Amount-Overall SB"].map(format_int_with_commas)
   
    Final_Summary.to_excel(writer,sheet_name='Summary',index=False)
    # Transeting TID
    transecting_TID.to_excel(writer,sheet_name='Terminal Transacted',index=False)
    LTD_Final["Txn Amount-Non Insta SB"] = LTD_Final["Txn Amount-Non Insta SB"].round(2)
    LTD_Final["Txn Amount-Non Insta SB"] = LTD_Final["Txn Amount-Non Insta SB"].map(format_int_with_commas)
    LTD_Final["Txn Amount-Insta SB"] = LTD_Final["Txn Amount-Insta SB"].round(2)
    LTD_Final["Txn Amount-Insta SB"] = LTD_Final["Txn Amount-Insta SB"].map(format_int_with_commas)
    LTD_Final["Txn Amount-Overall SB"] = LTD_Final["Txn Amount-Overall SB"].round(2)
    LTD_Final["Txn Amount-Overall SB"] = LTD_Final["Txn Amount-Overall SB"].map(format_int_with_commas)
    LTD_Final.to_excel(writer,sheet_name='LTD',index=False)
fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(output_file)
fix_worksheet.process_all_worksheets()
		

print("File Creation is Successfull")
exit()
