import os 
import sys
import datetime as dt
from datetime import date
from datetime import timedelta

out_dir="/home/<USER>/Reports/HDFC_Bank_SB_Txn_ONB/"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%d%m%Y")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
input_file = "/home/<USER>/Reports/Scripts/HDFC_Bank_SB_Txn_ONB/Historical_Data_SB_ONB.xlsx"
output_file = os.path.join(out_dir,yesterday_file_name,"TID_wise_txn_summary_HDFC_BANK_SB_TXN_ONB.xlsx")

def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

def date_serial_number(serial_number: int):
    """
    Convert an Excel serial number to a Python datetime object
    :param serial_number: the date serial number
    :return: a datetime object
    """
    # Excel stores dates as "number of days since 1900"
    delta = dt.datetime(1899, 12, 30) + dt.timedelta(days=serial_number)
    return delta

#in_dir = sys.argv[1]
#out_dir = sys.argv[2]
config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}

Summary_query = ("""
SELECT
    t.id as transactionID,
    Date(t.transactionTime) as transactionTime,
    CASE
        WHEN t.typeId = 15 AND t.emiType = 'debit' THEN t.additionalAmount / POWER(10, a.decimalAmountLength)
        ELSE t.amount / POWER(10, a.decimalAmountLength)
    END AS amount,
    ifnull(t.tipAmount,0) / POWER(10, a.decimalAmountLength) AS tipAmount,
    ifnull(t.additionalAmount,0) / POWER(10, a.decimalAmountLength) AS additionalAmount,
    ifnull(t.authAmount,0) as authAmount,
    tp.program_name,
    t.terminalID,mmut.programId,
    t.merchantID,
    mmut.id as posID
  FROM transactions t
  LEFT JOIN acquirer a ON t.acqId = a.id AND a.id = 3
  LEFT JOIN mapping_merchant_user_terminal mmut
  --  ON t.merchantID = mmut.divisionId
    ON t.terminalID = mmut.terminalID
  --  AND mmut.status = 'A'
  LEFT JOIN tbl_program tp ON mmut.programId = tp.program_id
  WHERE
        t.transactionTime >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        AND t.transactionTime < CURDATE()
    AND t.statusId IN (2, 7)
   AND mmut.programId IN ('95','37')
    AND t.typeID in ('22','24')
    AND case when mmut.status is Null then 'null'
            when mmut.status = 'D' and mmut.recordUpdated >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        AND mmut.recordUpdated < CURDATE() then 'A'
                        else mmut.status end in ('A','null');
    """)


TID_Transecting =("""
SELECT
    mmut.programId, t.terminalID
  FROM transactions t
  LEFT JOIN acquirer a ON t.acqId = a.id 
 LEFT JOIN mapping_merchant_user_terminal mmut
    ON case when t.divisionId = mmut.id then 1
                when t.divisionID=0 and  t.merchantID=mmut.divisionId then 1 else 0 end =1
    AND t.terminalID = mmut.terminalID
     And mmut.terminalID not in ('64000037', '64000038', '64000039', '64000040', '64000041')
  LEFT JOIN tbl_program tp ON mmut.programId = tp.program_id
  WHERE
         t.transactionTime >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
         AND t.transactionTime < CURDATE()
    AND t.statusId IN (2, 7)
   AND mmut.programId IN ('95','37')
    AND t.typeID in (22,24)
        AND case when mmut.status is Null then 'null'
            when mmut.status = 'D' and mmut.recordUpdated >= DATE_SUB(CURDATE(), INTERVAL 3 DAY)
        AND mmut.recordUpdated < CURDATE() then 'A'
                        else mmut.status end in ('A','null');
    """)

TID_Setup_Summary_query = ("""
WITH TID_SETUP_SUMMARY AS 
(SELECT 
    mmut.programId,
    terminalID,
    MIN(DATE(recordCreated)) AS Setup_date,
    COUNT(terminalID) AS TID_Setup
FROM
    mapping_merchant_user_terminal mmut
WHERE
    mmut.programId IN ('95','37')
    AND DATE(recordCreated) >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
    AND DATE(recordCreated) < CURDATE()
GROUP BY terminalID )
SELECT 
    Setup_date AS Date,programId, COUNT(*) AS TID_Setup
FROM
    TID_SETUP_SUMMARY
GROUP BY Setup_date,programId
ORDER BY 1
;
""")

TID_INST_Summary_query = ("""
WITH TID_INST_SUMMARY AS 
(
SELECT 
    mmut.programId,
    terminalID,
    max(DATE(firstTxnDate)) AS INST_date,
    COUNT(terminalID) AS TID_Setup
FROM
    mapping_merchant_user_terminal mmut
WHERE
    mmut.programId IN ('95','37')
     AND DATE(firstTxnDate) >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
    AND DATE(firstTxnDate) < CURDATE()
GROUP BY terminalID
)SELECT 
    INST_date AS Date,programId, SUM(TID_Setup) AS TID_Setup
FROM
    TID_INST_SUMMARY
GROUP BY INST_date,programId
ORDER BY 1
;
""")
