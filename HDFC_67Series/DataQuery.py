import os 
import sys
import datetime as dt
from datetime import date
from datetime import timedelta

out_dir="/home/<USER>/Reports/HDFC_67Series/"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%d%m%Y")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
input_file = "/home/<USER>/Reports/Scripts/HDFC_67Series/Historical_Data_67Series.xlsx"
output_file = os.path.join(out_dir,yesterday_file_name,"TID_wise_txn_summary_Vyappar_Ingenico_67_series.xlsx")

def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

def date_serial_number(serial_number: int):
    """
    Convert an Excel serial number to a Python datetime object
    :param serial_number: the date serial number
    :return: a datetime object
    """
    # Excel stores dates as "number of days since 1900"
    delta = dt.datetime(1899, 12, 30) + dt.timedelta(days=serial_number)
    return delta

#in_dir = sys.argv[1]
#out_dir = sys.argv[2]
config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}

Summary_query = ("""
WITH SUMMARY_CTE AS (
    SELECT
    t.id as transactionID,
    t.transactionTime,
    CASE
        WHEN t.typeId = 15 AND t.emiType = 'debit' THEN t.additionalAmount / POWER(10, a.decimalAmountLength)
        ELSE t.amount / POWER(10, a.decimalAmountLength)
    END AS amount,
    ifnull(t.tipAmount,0) / ifnull(POWER(10, a.decimalAmountLength),1) AS tipAmount,
    ifnull(t.additionalAmount,0) / ifnull(POWER(10, a.decimalAmountLength),1) AS additionalAmount,
    ifnull(t.authAmount,0) as authAmount,
    tp.program_name,
    t.terminalID,mmut.programId
  FROM transactions t
  LEFT JOIN acquirer a ON t.acqId = a.id AND a.id = 3
 LEFT JOIN mapping_merchant_user_terminal mmut  
    ON case when t.divisionId = mmut.id then 1 
		when t.divisionID=0 and  t.merchantID=mmut.divisionId then 1 else 0 end =1
    AND t.terminalID = mmut.terminalID
    And mmut.terminalID not in ('80137736', '99594973')
  LEFT JOIN tbl_program tp ON mmut.programId = tp.program_id
  WHERE
        t.transactionTime >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        AND t.transactionTime < CURDATE()
    AND t.statusId IN (2, 7)
   AND mmut.programId IN ('39')
    AND t.typeID in (1,27,9,15,14,36,22,24,19,21)
    AND case when mmut.status is Null then 'null'
            when mmut.status = 'D' and mmut.recordUpdated >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        AND mmut.recordUpdated < CURDATE() then 'A'
                        else mmut.status end in ('A','null')
)
SELECT program_name,
       DATE(transactionTime) AS transaction_date,
       programID,
       SUM(amount) AS total_amount,
       SUM(tipAmount) AS total_tipAmount,
       SUM(additionalAmount) AS total_additionalAmount,
       SUM(authAmount) AS total_authAmount,
       COUNT(*) AS total_transactions,
       COUNT(DISTINCT terminalID) AS unique_terminals
FROM SUMMARY_CTE
GROUP BY program_name, transaction_date
ORDER BY transaction_date;
""")

TID_Transecting =("""
 SELECT
    DISTINCT t.terminalID
  FROM transactions t
  LEFT JOIN acquirer a ON t.acqId = a.id 
 LEFT JOIN mapping_merchant_user_terminal mmut
    ON case when t.divisionId = mmut.id then 1
                when t.divisionID=0 and  t.merchantID=mmut.divisionId then 1 else 0 end =1
    AND t.terminalID = mmut.terminalID
     And mmut.terminalID not in ('64000037', '64000038', '64000039', '64000040', '64000041')
  LEFT JOIN tbl_program tp ON mmut.programId = tp.program_id
  WHERE
         t.transactionTime >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
         AND t.transactionTime < CURDATE()
    AND t.statusId IN (2, 7)
   AND mmut.programId IN ('39')
    AND t.typeID in (1,27,9,15,14,36,22,24,19,21)
        AND case when mmut.status is Null then 'null'
            when mmut.status = 'D' and mmut.recordUpdated >= DATE_SUB(CURDATE(), INTERVAL 3 DAY)
        AND mmut.recordUpdated < CURDATE() then 'A'
                        else mmut.status end in ('A','null');
""")

TID_Setup_Summary_query = ("""
WITH TID_SETUP_SUMMARY AS 
(SELECT 
    terminalID,
    MIN(DATE(recordCreated)) AS Setup_date,
    COUNT(terminalID) AS TID_Setup
FROM
    mapping_merchant_user_terminal mmut
WHERE
 mmut.programId IN ('39')
 AND DATE(recordCreated)  >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
AND DATE(recordCreated) < CURDATE()
GROUP BY terminalID
)SELECT 
    Setup_date AS Date, COUNT(*) AS TID_Setup
FROM
    TID_SETUP_SUMMARY
GROUP BY Setup_date
ORDER BY 1
;
""")

TID_INST_Summary_query = ("""
WITH TID_INST_SUMMARY AS 
(
SELECT 
    terminalID,
    max(DATE(firstTxnDate)) AS INST_date,
    COUNT(terminalID) AS TID_Setup
FROM
    mapping_merchant_user_terminal mmut
WHERE
 mmut.programId IN ('39')
 AND DATE(firstTxnDate) >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
AND DATE(firstTxnDate) < CURDATE()
GROUP BY terminalID
)SELECT 
    INST_date AS Date, SUM(TID_Setup) AS TID_Setup
FROM
    TID_INST_SUMMARY
GROUP BY INST_date
ORDER BY 1
;
""")
