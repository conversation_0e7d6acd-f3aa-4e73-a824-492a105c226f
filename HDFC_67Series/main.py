from datetime import datetime,timedelta
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import config,input_file,output_file,format_int_with_commas,date_serial_number,Summary_query,TID_Transecting,TID_Setup_Summary_query,TID_INST_Summary_query
import XLSXAutoFitColumns

pd.options.display.float_format = '{:,.2f}'.format

# Data extraction from SQL
TID_Setup = data_retrival(TID_Setup_Summary_query)
TID_INST = data_retrival(TID_INST_Summary_query)
VYAPAAR_INGENICO = data_retrival(Summary_query)
TID_FTD = data_retrival(TID_Transecting)

# Reading Historical Data
hist_summary = pd.read_excel(input_file,sheet_name='Summary',parse_dates=False)
HIST_LTD = pd.read_excel(input_file,sheet_name="LTD")
TID_MTD = pd.read_excel(input_file,sheet_name="TransactingTIDMTD")
TID_LTD = pd.read_excel(input_file,sheet_name="TransactingTIDLTD")
print(VYAPAAR_INGENICO.info())
print(hist_summary.info())
# Preparing Currunt Month Summary
#hist_summary['Date'] = hist_summary['Date'].map(date_serial_number)
#hist_summary['Month'] = hist_summary['Month'].map(date_serial_number)
#hist_summary['Month'] = hist_summary['Month'].dt.strftime('%b-%y')
#hist_summary['Date'] = hist_summary['Date'].dt.strftime('%d-%m-%Y')
Todays_data = pd.DataFrame({ "Date":(datetime.now() - timedelta(days=1)).strftime('%d-%m-%Y'),
               "Month":(datetime.now() - timedelta(days=1)).strftime('%b-%y'),
               "TID_Setup":0 if TID_Setup.shape[0] == 0 else TID_Setup['TID_Setup'][0],
               "TID _Inst":0 if TID_INST.shape[0] == 0 else TID_INST['TID_Setup'][0],
                "Txn_Count":VYAPAAR_INGENICO['total_transactions'],
               "Txn_Amount" : round(VYAPAAR_INGENICO['total_amount'][0],2)
        })
curr_month = pd.concat([hist_summary,Todays_data]).reset_index(drop=True).astype({"TID _Inst":"int64"})
MTD = pd.DataFrame({
    'Date' : 'Total MTD',
    'Month':(datetime.now() - timedelta(days=1)).strftime("%b'%y"),
    'TID_Setup': curr_month['TID_Setup'].sum(),
    'TID _Inst': curr_month['TID _Inst'].sum(),
    'Txn_Count': curr_month['Txn_Count'].sum(),
    'Txn_Amount': curr_month['Txn_Amount'].sum()
},index=[0])
print(Todays_data)
print(curr_month)
print(MTD)
# Preparing LTD Summary
if HIST_LTD['Month'][HIST_LTD.shape[0]-1] == MTD['Month'][0]:
    HIST_LTD = pd.concat([HIST_LTD.iloc[:-1,:],MTD.iloc[:,1:]]).reset_index(drop=True)
else:
    HIST_LTD = pd.concat([HIST_LTD,MTD.iloc[:,1:]]).reset_index(drop=True)

LTD =  pd.DataFrame({
    'Date' : 'Total LTD',
    'Month':"Total LTD",
    'TID_Setup': HIST_LTD['TID_Setup'].sum(),
    'TID _Inst': HIST_LTD['TID _Inst'].sum(),
    'Txn_Count': HIST_LTD['Txn_Count'].sum(),
    'Txn_Amount': HIST_LTD['Txn_Amount'].sum()
},index=[0])

LTD_Final = pd.concat([HIST_LTD,LTD.iloc[:,1:]]).reset_index(drop=True)
Final_Summary = pd.concat([curr_month,MTD,LTD]).reset_index(drop=True)

# Preping Unique TID
TID_FTD=TID_FTD.astype({"terminalID":"int64"})
NewMTD_TID = TID_FTD[~TID_FTD['terminalID'].isin(TID_MTD['terminalID'])].reset_index(drop=True)
NewLTD_TID = TID_FTD[~TID_FTD['terminalID'].isin(TID_LTD['terminalID'])].reset_index(drop=True)
TID_MTD = pd.concat([TID_MTD,NewMTD_TID]).reset_index(drop=True)
TID_LTD = pd.concat([TID_LTD,NewLTD_TID]).reset_index(drop=True)
transecting_TID = pd.DataFrame({
    'FTD' : TID_FTD.shape[0],
    'MTD' : TID_MTD.shape[0],
    'LTD' : TID_LTD.shape[0],
},index=[0]
)

if datetime.now().strftime("%b'%y") == (datetime.now() - timedelta(days=1)).strftime("%b'%y"):
    pass
else:
    TID_MTD = TID_MTD[0:0]
    curr_month = curr_month[0:0]

with pd.ExcelWriter(input_file,engine='openpyxl',mode='a',if_sheet_exists="replace") as writer:
    TID_MTD.to_excel(writer,sheet_name='TransactingTIDMTD',index=False)
    TID_LTD.to_excel(writer,sheet_name='TransactingTIDLTD',index=False)
    curr_month.to_excel(writer,sheet_name='Summary',index=False)
    HIST_LTD.to_excel(writer,sheet_name='LTD',index=False)
with pd.ExcelWriter(output_file) as writer:
    Final_Summary.rename(columns={
        'Date':'Date',
        'Month' : 'Month',
        'TID_Setup': 'TID Setup',
        'TID _Inst' : 'TID (Inst)',
        'Txn_Count' : 'Txn Count',
        'Txn_Amount':'Txn Amount'
    },inplace = True)
    Final_Summary["Txn Amount"] = Final_Summary["Txn Amount"].round(2)
    Final_Summary["Txn Amount"] = Final_Summary["Txn Amount"].map(format_int_with_commas)

    Final_Summary.to_excel(writer,sheet_name='Summary',index=False)
    # Transeting TID
    transecting_TID.to_excel(writer,sheet_name='Transacting Terminal',index=False)
    LTD_Final.rename(columns={
        'Month' : 'Month',
        'TID_Setup': 'TID Setup',
        'TID _Inst' : 'TID (Inst)',
        'Txn_Count' : 'Txn Count',
        'Txn_Amount':'Txn Amount'
    },inplace = True)
    LTD_Final["Txn Amount"] = LTD_Final["Txn Amount"].round(2)
    LTD_Final["Txn Amount"] = LTD_Final['Txn Amount'].map(format_int_with_commas)
    LTD_Final.to_excel(writer,sheet_name='LTD',index=False)

fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(output_file)
fix_worksheet.process_all_worksheets()
		

print("File Creation is Successfull")
exit()
