from datetime import datetime,<PERSON><PERSON><PERSON>
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import config,format_int_with_percent,input_dir,filePaths,output_file,readHistoricalData,writeHistoricalData,format_int_with_commas,date_serial_number,Summary_query,TID_Transecting,TID_Setup_Summary_query,TID_INST_Summary_query
import XLSXAutoFitColumns

pd.options.display.float_format = '{:,.2f}'.format


# Data extraction from SQL
TID_Setup = data_retrival(TID_Setup_Summary_query)
TID_INST = data_retrival(TID_INST_Summary_query)
Transactions = data_retrival(Summary_query)
TID_FTD = data_retrival(TID_Transecting)

Transactions["% Card TXN"] = round(Transactions["Card Txn Count"][0]/Transactions["Total Txn Count"][0],4)
Transactions["% Digital TXN"] = round(Transactions["Digital Txn Count"][0]/Transactions["Total Txn Count"][0],4)
Transactions["TID Setup"] = 0 if TID_Setup.shape[0] == 0 else TID_Setup['TID_Setup'][0]
Transactions["TID (Inst)"] = 0 if TID_INST.shape[0] == 0 else TID_INST['TID_Setup'][0]
Todays_data = Transactions[['Date', 'Month','TID Setup', 'TID (Inst)', 'Card Txn Count', 'Digital Txn Count',
       'Card Txn Amount', 'Digital Txn Amount', 'Total Txn Count',
       'Total Txn Amount','% Card TXN', '% Digital TXN']]

# Reading Historical Data
hist_summary , HIST_LTD , TID_MTD,TID_LTD = readHistoricalData(filePaths,fileType="Excel")
#hist_summary , HIST_LTD , TID_MTD,TID_LTD = readHistoricalData(filePaths)
curr_month = pd.concat([hist_summary,Todays_data]).reset_index(drop=True).astype({"TID (Inst)":"int64"})

MTD = pd.DataFrame({
    'Date' : 'Total MTD',
    'Month':(datetime.now() - timedelta(days=1)).strftime("%b'%y"),
    'TID Setup': curr_month['TID Setup'].sum(),
    'TID (Inst)': curr_month['TID (Inst)'].sum(),
    'Card Txn Count': curr_month['Card Txn Count'].sum(),
    'Digital Txn Count': curr_month['Digital Txn Count'].sum(),
    'Card Txn Amount': curr_month['Card Txn Amount'].sum(),
    'Digital Txn Amount': curr_month['Digital Txn Amount'].sum(),
    'Total Txn Count': curr_month['Total Txn Count'].sum(),
    'Total Txn Amount': curr_month['Total Txn Amount'].sum(),
    '% Card TXN': round(curr_month["Card Txn Count"].sum()/curr_month["Total Txn Count"].sum(),4),
    '% Digital TXN': round(curr_month["Digital Txn Count"].sum()/curr_month["Total Txn Count"].sum(),4),
},index=[0])

if HIST_LTD['Month'][HIST_LTD.shape[0]-1] == MTD['Month'][0]:
    HIST_LTD = pd.concat([HIST_LTD.iloc[:-1,:],MTD.iloc[:,1:]]).reset_index(drop=True)
else:
    HIST_LTD = pd.concat([HIST_LTD,MTD.iloc[:,1:]]).reset_index(drop=True)

LTD = pd.DataFrame({
    'Date' : 'Total LTD',
    'Month':"Total LTD",
    'TID Setup': HIST_LTD['TID Setup'].sum(),
    'TID (Inst)': HIST_LTD['TID (Inst)'].sum(),
    'Card Txn Count': HIST_LTD['Card Txn Count'].sum(),
    'Digital Txn Count': HIST_LTD['Digital Txn Count'].sum(),
    'Card Txn Amount': HIST_LTD['Card Txn Amount'].sum(),
    'Digital Txn Amount': HIST_LTD['Digital Txn Amount'].sum(),
    'Total Txn Count': HIST_LTD['Total Txn Count'].sum(),
    'Total Txn Amount': HIST_LTD['Total Txn Amount'].sum(),
    '% Card TXN': round(HIST_LTD["Card Txn Count"].sum()/HIST_LTD["Total Txn Count"].sum(),4),
    '% Digital TXN': round(HIST_LTD["Digital Txn Count"].sum()/HIST_LTD["Total Txn Count"].sum(),4),
},index=[0])
LTD_Final = pd.concat([HIST_LTD,LTD.iloc[:,1:]]).reset_index(drop=True)
Final_Summary = pd.concat([curr_month,MTD,LTD]).reset_index(drop=True)

# Preping Unique TID
NewMTD_TID = TID_FTD[~TID_FTD['UserName'].isin(TID_MTD['UserName'])].reset_index(drop=True)
NewLTD_TID = TID_FTD[~TID_FTD['UserName'].isin(TID_LTD['UserName'])].reset_index(drop=True)
TID_MTD = pd.concat([TID_MTD,NewMTD_TID]).reset_index(drop=True)
TID_LTD = pd.concat([TID_LTD,NewLTD_TID]).reset_index(drop=True)
transecting_TID = pd.DataFrame({
    'FTD' : TID_FTD.shape[0],
    'MTD' : TID_MTD.shape[0],
    'LTD' : TID_LTD.shape[0]
},index=[0]
)

if datetime.now().strftime("%b'%y") == (datetime.now() - timedelta(days=1)).strftime("%b'%y"):
    pass
else:
    TID_MTD = TID_MTD[0:0]
    curr_month = curr_month[0:0]

writeHistoricalData(filePaths,curr_month,HIST_LTD,TID_MTD,TID_LTD,fileType="Excel")

with pd.ExcelWriter(output_file) as writer:
    Final_Summary["Card Txn Amount"] = Final_Summary["Card Txn Amount"].map(lambda x: round(x,2))
    Final_Summary["Card Txn Amount"] = Final_Summary["Card Txn Amount"].map(format_int_with_commas)
    Final_Summary["Digital Txn Amount"] = Final_Summary["Digital Txn Amount"].map(lambda x: round(x,2))
    Final_Summary["Digital Txn Amount"] = Final_Summary["Digital Txn Amount"].map(format_int_with_commas)
    Final_Summary["Total Txn Amount"] = Final_Summary["Total Txn Amount"].map(lambda x: round(x,2))
    Final_Summary["Total Txn Amount"] = Final_Summary["Total Txn Amount"].map(format_int_with_commas)
    Final_Summary["% Card TXN"] = Final_Summary["% Card TXN"].map(format_int_with_percent)
    Final_Summary["% Digital TXN"] = Final_Summary["% Digital TXN"].map(format_int_with_percent)
    Final_Summary.drop(['Month'],inplace=True,axis=1)
    Final_Summary.rename({
        'Card Txn Count': "APPVD Card Txn Count",
        'Digital Txn Count': "APPVD Digital Txn Count",
        'Card Txn Amount': 'APPVD Card Txn Amount',
        'Digital Txn Amount': 'APPVD Digital Txn Amount',
        'Total Txn Count': 'APPVD Total Txn Count',
        'Total Txn Amount': 'APPVD Total Txn Amount',
    },axis=1,inplace=True)
    Final_Summary.to_excel(writer,sheet_name='Summary',index=False)
    # Transeting TID
    transecting_TID.to_excel(writer,sheet_name='Terminal Transacted',index=False)
    LTD_Final["Card Txn Amount"] = LTD_Final["Card Txn Amount"].map(lambda x: round(x,2))
    LTD_Final["Card Txn Amount"] = LTD_Final["Card Txn Amount"].map(format_int_with_commas)
    LTD_Final["Digital Txn Amount"] = LTD_Final["Digital Txn Amount"].map(lambda x: round(x,2))
    LTD_Final["Digital Txn Amount"] = LTD_Final["Digital Txn Amount"].map(format_int_with_commas)
    LTD_Final["Total Txn Amount"] = LTD_Final["Total Txn Amount"].map(lambda x: round(x,2))
    LTD_Final["Total Txn Amount"] = LTD_Final["Total Txn Amount"].map(format_int_with_commas)
    LTD_Final["% Card TXN"] = LTD_Final["% Card TXN"].map(format_int_with_percent)
    LTD_Final["% Digital TXN"] = LTD_Final["% Digital TXN"].map(format_int_with_percent)
    LTD_Final.rename({
        'Card Txn Count': "APPVD Card Txn Count",
        'Digital Txn Count': "APPVD Digital Txn Count",
        'Card Txn Amount': 'APPVD Card Txn Amount',
        'Digital Txn Amount': 'APPVD Digital Txn Amount',
        'Total Txn Count': 'APPVD Total Txn Count',
        'Total Txn Amount': 'APPVD Total Txn Amount',
    },axis=1,inplace=True)
    LTD_Final.to_excel(writer,sheet_name='LTD',index=False)

fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(output_file)
fix_worksheet.process_all_worksheets()
		

print("File Creation is Successfull")
exit()
