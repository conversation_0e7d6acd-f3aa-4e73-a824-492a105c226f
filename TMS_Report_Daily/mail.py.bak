import os
import sys
import smtplib
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from email.mime.image import MIMEImage
from enviroment import From,server_name,to,Port,Password,Username
from datetime import datetime, timedelta
import glob
import pandas as pd
from DataQuery import output_file
#print(os.getcwd())

msg = MIMEMultipart()
msg['From'] = From
msg["To"] = ','.join(to)
#msg["Cc"] = to
msg['Subject'] = f"TEST - Amex Terminal Bases Request till {(datetime.now() - timedelta(1)).strftime('%d-%m-%Y')}"
folder_name = sys.argv[1]
file_name = sys.argv[2]

htmlEmail = f"""
<p> Dear Sir/Madam, <br/><br/>
    PFA AMEX Terminal Base file Till {(datetime.now() - timedelta(1)).strftime('%d-%m-%Y')}.<br/>
     <br/><br/>
    All terminal is enabled for AMEX contactless.
</p>
"""

#msg.attach(MIMEText(htmlEmail, 'html'))
f = output_file
#f = os.path.join(os.getcwd(),folder_name,f"{folder_name} {file_name}")
#html tables data
xl = pd.ExcelFile(f)
sheet_name = xl.sheet_names  # see all sheet names
def generate_html(f,sheet):
    read_file = pd.read_excel(f,sheet_name=sheet)
    html_file = f'''<h5>{sheet} Till {(datetime.now() - timedelta(1)).strftime('%d-%m-%Y')} <h5><br/>
    {read_file.to_html(index=False,na_rep='',)}'''
    return html_file
#for sheet in sheet_name:
#    html_txt = generate_html(f,sheet)
#    htmlEmail = "<br/>".join([htmlEmail,html_txt])
    
htmlEmail2 = """
<p> Please contact Mosambee Support Team (<EMAIL>) directly if you have any questions. <br/>
    Thank you! <br/>
    Best Regards, <br/>
    Mosambee Support Team </p>
<br/>
<font color="red">Please do not reply to this email as it is auto-generated. </font>
"""
htmlEmail = "<br/>".join([htmlEmail,htmlEmail2])
msg.attach(MIMEText(htmlEmail, 'html'))

with open(f, "rb") as attached_file:
    part = MIMEApplication(
            attached_file.read(),
            Name=os.path.basename(f)
            )
# After the file is closed
part['Content-Disposition'] = 'attachment; filename="%s"' % os.path.basename(f)
msg.attach(part)
server = smtplib.SMTP(server_name, Port)
server.starttls()
server.login(Username,Password)
text = msg.as_string()
server.sendmail(From, to, text)
server.quit()

print("Email are sent successfully!")

