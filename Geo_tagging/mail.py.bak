import os
import sys
import smtplib
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from email.mime.image import MIMEImage
from enviroment import From,server_name,to,Port,Password,Username
from datetime import datetime, timedelta
import glob
import pandas as pd
from DataQuery import outputDir
#print(os.getcwd())
htmlEmail = f"""
<p> Dear Sir/Madam, <br/><br/>
    Please find the attached Report dated {(datetime.now() - timedelta(1)).strftime('%d-%m-%Y')}.<br/>
</p>
"""

#msg.attach(MIMEText(htmlEmail, 'html'))    
htmlEmail2 = """
<p> Please contact Mosambee Support Team (<EMAIL>) directly if you have any questions. <br/>
    Thank you! <br/>
    Best Regards, <br/>
    Mosambee Support Team </p>
<br/>
<font color="red">Please do not reply to this email as it is auto-generated. </font>
"""
htmlEmail = "<br/>".join([htmlEmail,htmlEmail2])
res = []

# Iterate directory
for path in os.listdir(os.path.join(os.getcwd(),folder_name)):
    # check if current path is a file
    if path != 'daily.csv':
        if os.path.isfile(os.path.join(os.getcwd(),folder_name, path)):
            res.append(path)
            

for file_name in res:
    msg = MIMEMultipart()
    msg['From'] = From
    msg["To"] = ','.join(to)
    #msg["Cc"] = to
    msg['Subject'] = f"Geo Tagging Report for {file_name}  Dated on {(datetime.now() - timedelta(1)).strftime('%d-%m-%Y')}"
    msg.attach(MIMEText(htmlEmail, 'html'))
    f = os.path.join(os.getcwd(),folder_name,file_name)
    with open(f, "rb") as attached_file:
        part = MIMEApplication(
                attached_file.read(),
                Name=os.path.basename(f)
                )
    # After the file is closed
    part['Content-Disposition'] = 'attachment; filename="%s"' % os.path.basename(f)
    msg.attach(part)
    server = smtplib.SMTP(server_name, Port)
    server.starttls()
    server.login(Username,Password)
    text = msg.as_string()
    server.sendmail(From, to, text)
    server.quit()

print("Email are sent successfully!")

