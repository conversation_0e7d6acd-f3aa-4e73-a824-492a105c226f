import os 
import sys
from datetime import date,datetime
from datetime import timedelta

out_dir="/home/<USER>/Reports/Geo_Tagging_Daily/"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%Y%m%d")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
outputDir = os.path.join(out_dir,yesterday_file_name)


def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}


Query = '''
SELECT
    t.terminalId AS TID,
    CASE
        WHEN t.status = 'A' THEN 'Active'
        WHEN t.status = 'D' THEN 'Deactive'
    END AS 'Active/Deactive',
    CONCAT("'", t.deviceSerialNo) AS 'Device Serial',
    mdat.merchantCode AS MID,
    'Mosambee' AS 'ORG Code',
    m.dbaName AS Name,
    mat.acquirerName AS 'Acquirer',
    'Corporate' AS 'Type',
    m.mcc AS 'MCC',
    c.name AS 'City',
    t.securityToken AS 'TID Location',
    s.name AS 'State',
    CASE
        WHEN t.userActivationDate IS NULL THEN CONCAT(' ', t.recordCreated)
        ELSE CONCAT(' ', t.userActivationDate)
    END AS 'Live Date',
    CONCAT(' ', t.LastTxnDate) AS 'Lates_kx_date',
    CONCAT(' ', t.firstTxnDate) AS 'First_txn_date',
    CASE WHEN t.status = 'D' then CONCAT(' ', t.recordUpdated)
    else ' ' end AS 'Deactivation Date',
    CONCAT(' ', t.LastTxnDate) AS 'Heartbeat',
    CONCAT(' ', g.lattitude) AS lattitude,
    CONCAT(' ', g.longitude) AS longitude,
    'Yes' AS 'NFC_enabled',
    CASE
        WHEN m.isIntegrated = 0 THEN 'No'
        WHEN m.isIntegrated = 1 THEN 'Yes'
    END AS 'Integrated'
FROM
    mapping_merchant_user_terminal t
        INNER JOIN
    mapping_division_acquirer_tg mdat ON mdat.id = t.divAcqTgId
        INNER JOIN
    mapping_acquirer_tg mat ON mat.id = mdat.acqTgId
        INNER JOIN
    merchant m ON m.id = t.divisionId
        LEFT JOIN
    address a ON m.addressID = a.id
        LEFT JOIN
    city c ON c.id = a.cityID
        LEFT JOIN
    state s ON s.id = a.stateID
        LEFT JOIN
    (SELECT
        MAX(id) AS ID, pincode, lattitude, longitude
    FROM
        geoLocationMaster
    GROUP BY pincode) g ON g.pincode = a.pin
WHERE
    t.status IN ('A' , 'D')
        AND t.id IN (SELECT
            MAX(id)
        FROM
            mapping_merchant_user_terminal
        GROUP BY terminalID)
        AND mat.acquirerName IN ('HDFC','SBI', 'KOTAK', 'YES', 'MOSAMBEEHDFC')
        AND t.recordCreated < CURDATE();
'''
