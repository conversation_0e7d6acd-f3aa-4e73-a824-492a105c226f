from datetime import datetime,timed<PERSON>ta
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import Query,config,outputDir

pd.options.display.float_format = '{:,.2f}'.format

print("Starting Data extrection.")
raw_data = data_retrival(Query)
raw_data.head()
print(raw_data["Acquirer"].unique())
for acq in raw_data["Acquirer"].unique():
    raw_data[raw_data["Acquirer"]==acq].to_csv(f"{outputDir}/{acq}.csv",index=False,na_rep=" ")
    print(f"file created for {acq}")
print("File Creation is Successfull")
exit()
