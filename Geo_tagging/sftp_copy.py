import pysftp
from datetime import datetime

# Define connection parameters
host = "**************"
username = "MOS_SFTP"
password = "0S6#985Tc2o@8283"
port = "22"

# Get today's date in YYYYMMDD format
today = datetime.today().strftime('%Y%m%d')

# Define source and destination paths
source_dir = f"/home/<USER>/Reports/Geo_Tagging_Daily/{today}"
destination_dir = f"/share/CACHEDEV1_DATA/Geo-Tagging\ -\ Daily/{today}"
excluded_file = "Data.csv"

# Connect to the SFTP server
try:
    with pysftp.Connection(host, username, password, port) as sftp:
        # Create the remote directory
        print(f"Creating directory: {destination_dir}")
        sftp.makedirs(destination_dir)

        # Loop through all files in the source directory
        for filename in sftp.listdir(source_dir):
            if filename != excluded_file:
                source_path = os.path.join(source_dir, filename)
                destination_path = os.path.join(destination_dir, filename)
                print(f"Copying file: {source_path} -> {destination_path}")
                sftp.put(source_path, destination_path)

        print(f"Files copied successfully to: {destination_dir}")

except Exception as e:
    print(f"Error: {e}")

