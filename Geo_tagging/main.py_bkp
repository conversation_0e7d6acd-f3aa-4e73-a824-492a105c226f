from sys import exit,argv
import os
import pandas as pd
import numpy as np
import string

reportName = argv[1]
foldername = argv[2]
dir = argv[3]
os.chdir(os.path.join(dir,foldername))
print(f'current Directory {os.getcwd()}')
df = pd.read_csv("Daily.csv")
df = df[['TID','Active/Deactive','Device Serial','MID',
        'ORG_Name','Name','Acquirer','Type','MCC','City','TID_Location'
         ,'State', 'Live_Date','Lates_KX_Date',
         'First_TXN_Date', 'Last_TXN_Date','Heartbeat','lattitude',  'longitude','NFC_enabled','Integrated']]
acquirer = list(df['Acquirer'].unique())

for ACQ in acquirer:
    df[df["Acquirer"]==ACQ].to_csv(f"{ACQ}.csv",index=False,na_rep="")

hdfc_df = pd.read_csv("HDFC_data.csv")
hdfc_df = hdfc_df[['TID','Active/Deactive','Device Serial','MID',
        'ORG_Name','Name','Acquirer','Type','MCC','City','TID_Location'
         ,'State', 'Live_Date','Lates_KX_Date',
         'First_TXN_Date', 'Last_TXN_Date','Heartbeat','lattitude',  'longitude','NFC_enabled','Integrated']]
hdfc_df.to_csv("HDFC.csv",index=False,na_rep="")

print("Process Complete")
exit()
