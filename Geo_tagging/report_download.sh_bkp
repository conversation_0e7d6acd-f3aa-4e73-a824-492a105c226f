#!/bin/bash

#dir=$2
#cd $dir
#tabcmd login -s http://192.168.4.15:8080 -u tabadmin -p $1
#foldername=$(date -d "yesterday" '+%Y%m%d')
#echo $foldername
#[ ! -d "$foldername" ] && mkdir -p "$foldername"
#cd $foldername
#tabcmd get "/views/GeoTagging-Daily/Daily.csvo"
#tabcmd export "/GeoTagging-Daily/Daily?Acquirer=KOTAK,SBI,YES,MOSAMBEEHDFC" --csv -f "Daily.csv"
#tabcmd export "/GeoTagging-Daily/Daily?Acquirer=HDFC" --csv -f "HDFC_data.csv"
#for /f "tokens=*" %i in (./Acquirer.txt) DO tabcmd export "/GeoTagging-Daily/Daily?Acquirer=%i" --csv -f "%i.csv"
#tabcmd logout

#cd ..

#reportName="Geo_Tagging_Daily"

python3 /home/<USER>/Reports/Scripts/Geo_tagging/main.py
# $reportName $foldername $dir
#filename="Daily.csv"

#python3 /home/<USER>/Reports/Scripts/Geo_tagging/mail.py $foldername $filename $dir

#cd $dir

#cd $foldername
#rm -rf Daily.csv
#rm -rf *.png
echo "Completed"
