from datetime import datetime,timedelta
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import config,input_file,output_file,format_int_with_commas,date_serial_number,PayByLink_query,query
import XLSXAutoFitColumns

pd.options.display.float_format = '{:,.2f}'.format
data = data_retrival(query)
PayByLinkData = data_retrival(PayByLink_query)
data = pd.concat([data,PayByLinkData]).reset_index(drop=True)
test_tid = pd.read_csv("/home/<USER>/Reports/Scripts/AllTransactions/Test_TID_List.csv")
data = data[~data['terminalID'].isin(test_tid['TID'])].reset_index(drop=True)
data.program_id.fillna(0,inplace=True)
data.State.fillna('BLANK',inplace=True)
data_grouped = (data
 .groupby(['acqId','acquirerName','terminalID','merchantCode','City','State','transactionTypeName'])
 .aggregate({'id':'count','amount':'sum'}).reset_index()
)

data_grouped = data_grouped[['acquirerName','terminalID','merchantCode','City','State','transactionTypeName','id','amount']]

final = (data_grouped
         .set_index(['acquirerName','terminalID','merchantCode','City','State','transactionTypeName'],drop=True)
         .unstack(['transactionTypeName'])
         .fillna(0)
        )
column_list =  {(    'id',          'BQR'): ('id', 'BQR Txn Count'),
(    'id',          'EMI') : ('id', 'EMI Txn Count'),
(    'id',    'PayByLink') : ('id', 'PayByLink Txn Count'),
(    'id',         'Sale') : ('id', 'Sale Txn Count'),
(    'id',      'SaleTip') : ('id', 'SaleTip Txn Count'),
(    'id', 'Salecomplete') : ('id', 'Salecomplete Txn Count'),
(    'id',          'UPI') : ('id', 'UPI Txn Count'),
(    'id',         'Void') : ('id', 'Void Txn Count'),
 (    'id',          'CBWP') : ('id', 'CBWP Txn Count'),
(    'id',         'Wallet') : ('id', 'Wallet Txn Count'),
('amount',          'BQR') : ('amount', 'BQR Txn Amount'),
('amount',          'EMI') : ('amount', 'EMI Txn Amount'),
('amount',    'PayByLink') : ('amount', 'PayByLink Txn Amount'),
('amount',         'Sale') : ('amount', 'Sale Txn Amount'),
('amount',      'SaleTip') : ('amount', 'SaleTip Txn Amount'),
('amount', 'Salecomplete') : ('amount', 'Salecomplete Txn Amount'),
('amount',          'UPI') : ('amount', 'UPI Txn Amount'),
('amount',         'Void') : ('amount', 'Void Txn Amount'),
 ('amount',          'CBWP') : ('amount', 'CBWP Txn Amount'),
(    'amount',         'Wallet') : ('amount', 'Wallet Txn Amount')}
column_final_dict = {}
for column in column_list.keys():
    if column in final.columns:
        column_final_dict[column] = column_list[column]

final.columns = pd.MultiIndex.from_tuples(final.set_axis(final.columns.values, axis=1)
                                       .rename(columns=column_final_dict))

final = final.droplevel(level=0,axis=1)
column_list = [
    'Sale Txn Count','Sale Txn Amount',
    'Void Txn Count','Void Txn Amount',
    'BQR Txn Count','BQR Txn Amount',
    'UPI Txn Count','UPI Txn Amount',
    'SaleTip Txn Count','SaleTip Txn Amount',
    'Salecomplete Txn Count','Salecomplete Txn Amount',
    'EMI Txn Count','EMI Txn Amount',
    'PayByLink Txn Count','PayByLink Txn Amount',
    'Wallet Txn Count','Wallet Txn Amount',
    'CBWP Txn Count','CBWP Txn Amount',
]
for column in column_list:
    if column not in final.columns:
        final[column] = 0
final=final[column_list]
count_column_list = ['Sale Txn Count','BQR Txn Count','UPI Txn Count','SaleTip Txn Count','Salecomplete Txn Count','EMI Txn Count','PayByLink Txn Count','Wallet Txn Count','CBWP Txn Count']
Amt_column_list = ['Sale Txn Amount','BQR Txn Amount','UPI Txn Amount','SaleTip Txn Amount','Salecomplete Txn Amount','EMI Txn Amount','PayByLink Txn Amount','Wallet Txn Amount','CBWP Txn Amount']
final.reset_index(inplace=True)
final['Total Txn Count'] = final[count_column_list].sum(axis=1)
final['Total TXN Amount'] = final[Amt_column_list].sum(axis=1)
finalToWrite = (final
        .rename({'acquirerName':'Acquirer'},axis=1))
with pd.ExcelWriter(output_file) as writer:
    for Acq in list(finalToWrite.Acquirer.unique()):
        finalToWrite[finalToWrite['Acquirer']==Acq].reset_index(drop=True).to_excel(writer,Acq,index=False)

fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(output_file)
fix_worksheet.process_all_worksheets()


print("File Creation is Successfull")
