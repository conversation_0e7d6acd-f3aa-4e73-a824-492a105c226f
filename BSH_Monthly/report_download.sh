#!/bin/bash

dir=$2
cd $dir
tabcmd login -s http://************:8080 -u tabadmin -p $1
foldername=$(date -d "yesterday" '+%Y%m%d')
echo $foldername
[ ! -d "$foldername" ] && mkdir -p "$foldername"
cd $foldername
tabcmd export "/BSHMonthly/Sheet1?:refresh=yes" --csv -f "BSH_Monthly_RAW.csv"
#tabcmd export "/AirlinesDaily/Data?:refresh=yes" --csv -f "Data.xlsx"
tabcmd logout

cd ..

reportName="BSH_Monthly"

python3 /home/<USER>/Reports/Scripts/BSH_Monthly/main.py $reportName $foldername $dir
filename="BSH.csv"

python3 /home/<USER>/Reports/Scripts/BSH_Monthly/mail.py $foldername $filename $dir

cd $dir

cd $foldername
#rm -rf *.csv
#rm -rf *.png
echo "Completed"
