from sys import exit,argv
import os
import pandas as pd
import numpy as np
import string

reportName = argv[1]
foldername = argv[2]
dir = argv[3]
os.chdir(os.path.join(dir,foldername))
print(f'current Directory {os.getcwd()}')
def Convert_pect(df,col):
    df[col] = pd.Series(["{0:.2f}%".format(val * 100) for val in df[col]], index = df.index)

# First Report
BSH_Raw = pd.read_csv("BSH_Monthly_RAW.csv")
BSH = BSH_Raw[['ID','Date', 'User', 'Username','Type', 'Mode', 'Amount', 'Auth Code', 'Card', 'Card Type'
 ,'Brand Type', 'RRN', 'Invoice#','Device Serial', 'Status','Settled On','Label','MID','TID',
 'Batch','Reference#', 'Additional Information','Latitude', 'Longitude','Payer','TID Location','Transaction Created Time',
  'Transaction Status' ,'ME Name', 'Acquirer','Response Code','Currency','Device No','Reference Txn Id',
  'tg','DBA Name']]

BSH.to_csv("BSH.csv",index=False)
exit()

