from datetime import datetime,timedelta
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import config,output_file,format_int_with_commas,date_serial_number,Historical_data,query
import XLSXAutoFitColumns


data = data_retrival(query)
data['txnStatusName'] = data.txnStatusName.str.upper()
data['transactionDesc'] = data.transactionDesc.str.upper()
data['transactionDesc'] = data[~data['statusID'].isin([2,7,10])]['transactionDesc'].fillna('Error in Transactions')
data_grouped = (data
                .groupby(['transactionDesc'])
                .aggregate({'Transaction ID':'count'})
                .sort_values(['Transaction ID'],ascending=False)
                .reset_index()
                .rename({'transactionDesc':'Parameter','Transaction ID':(datetime.now() - timedelta(1)).strftime("%d'%b'%y")},axis=1)
               )

Total_Transactions = pd.DataFrame(
    {"Parameter":"No. Of transaction hitting the server",
     (datetime.now() - timedelta(1)).strftime("%d'%b'%y") : data["Transaction ID"].count()
    },index=[0]
)
Approved_Transactions = pd.DataFrame(
    {"Parameter":"No. of Approved transaction",
     (datetime.now() - timedelta(1)).strftime("%d'%b'%y") : data[data.statusID.isin([2,7,10])]["Transaction ID"].count()
    },index=[0],
)
Declined_Transactions = pd.DataFrame(
    {"Parameter":"No. Of request Declined",
     (datetime.now() - timedelta(1)).strftime("%d'%b'%y") : data[~data.statusID.isin([2,7,10])]["Transaction ID"].count()
    },index=[0],
)
Succes_pct_Transactions = pd.DataFrame(
    {"Parameter":"Success %",
     (datetime.now() - timedelta(1)).strftime("%d'%b'%y") : round((data[data.statusID.isin([2,7,10])]["Transaction ID"].count()/data["Transaction ID"].count())*100,2)
    },index=[0]
)
Declined_pct_Transactions = pd.DataFrame(
    {"Parameter":"Declined %",
     (datetime.now() - timedelta(1)).strftime("%d'%b'%y") : round((data[~data.statusID.isin([2,7,10])]["Transaction ID"].count()/data["Transaction ID"].count())*100,2)
    },index=[0],
)
Voided_Transactions = pd.DataFrame(
    {"Parameter":"NOT CATEGORIZED (Reversed & Voided)",
    (datetime.now() - timedelta(1)).strftime("%d'%b'%y") : data[(data.typeID==2) & (data.statusID.isin([2,7,10]))]["Transaction ID"].count()
    },index=[0]
)
# ISSUER_Transactions = pd.DataFrame(
#     {"Parameter":"ISSUER OTHER",
#      (datetime.now() - timedelta(1)).strftime("%d'%b'%y") : '--'
#     },index=[0]
# )
# ACQUIRER_Transactions = pd.DataFrame(
#     {"Parameter":"ACQUIRER - OTHER",
#      (datetime.now() - timedelta(1)).strftime("%d'%b'%y") : '--'
#     },index=[0],
# )
Todays_data =  pd.concat([Total_Transactions,Approved_Transactions,Declined_Transactions,Voided_Transactions,Declined_pct_Transactions,Succes_pct_Transactions,data_grouped]).reset_index(drop=True)

History_data = pd.read_excel(Historical_data,sheet_name='Sheet1')
if (datetime.now() - timedelta(1)).strftime("%d")!= '01':
    final = pd.merge(left=History_data,right=Todays_data,left_on=['Parameter'],right_on=['Parameter'],how="outer")
    final = final.fillna(0)
else:
    final = Todays_data
final['Total'] = final.sum(axis=1, numeric_only=True)
final.iloc[5:6,-1:] = int(final.iloc[1]['Total'])/int(final.iloc[0]["Total"])*100
final.iloc[4:5,-1:] = int(final.iloc[2]['Total'])/int(final.iloc[0]["Total"])*100
final['Total'] = round(final['Total'],2)

with pd.ExcelWriter(output_file) as writer:
    final.to_excel(writer,index=False)

with pd.ExcelWriter(Historical_data) as writer:
    final.iloc[:,:-1].to_excel(writer,index=False)



fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(output_file)
fix_worksheet.process_all_worksheets()
print("File Creation is Successfull")
