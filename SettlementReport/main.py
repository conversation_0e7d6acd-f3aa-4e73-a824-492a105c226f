from datetime import datetime,timedelta
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import settelmentDateTime,config,input_file,output_file1,output_file2,output_file4,output_file3,StartTime,ENDTime,cutOffStartTime,cutOffEndTime,format_int_with_commas,date_serial_number,query,ATOS_Transactions,IDFC_Transactions
import XLSXAutoFitColumns

pd.options.display.float_format = '{:,.2f}'.format

raw_data = data_retrival(query)
raw_data.drop_duplicates(subset=['id'],keep='first',inplace=True,ignore_index=True)
raw_data['transactionDate'] = raw_data['transactionTime'].dt.strftime('%d-%m-%Y')
data = raw_data[raw_data['forSettlement']==1]
data.reset_index(inplace=True,drop=True)
Cutoff = pd.read_csv(input_file)
merged_data = pd.merge(data, Cutoff, how='inner',left_on=['acquirerName','tgName'],right_on=['Acquirer','Tg'])
merged_data['settlementTxnTime'] = pd.to_datetime(merged_data['settlementTxnTime'])
merged_data['transactionTime'] = pd.to_datetime(merged_data['transactionTime'])
merged_data['Bank Settlement Start Time'] = pd.to_datetime(merged_data['Bank Settlement Start Time'])
merged_data['Bank Settlement Cutoff Time'] = pd.to_datetime(merged_data['Bank Settlement Cutoff Time'])
merged_data['Bank Settlement Start Time'] = merged_data.apply(StartTime,axis=1)
merged_data['Bank Settlement Cutoff Time'] = merged_data.apply(ENDTime,axis=1)

todays_Data = raw_data[raw_data['transactionDate'] == (datetime.now() - timedelta(days=1)).strftime('%d-%m-%Y')]
Settled_Summary = (todays_Data[~todays_Data.settlementTxnTime.isnull()]
 .groupby(['transactionDate'])
 .aggregate({'terminalID':'nunique','id':'count','amount':'sum'}).reset_index()
)

# Settlement Summary
Settled_Summary.rename(
    {
        'transactionDate':'Total Settled TXN',
        'terminalID':'User Count',
        'id':'Volume',
        'amount':'Value'
    },axis=1,inplace=True
)
merged_data['cutoffStart'] = merged_data.apply(cutOffStartTime,axis=1)
merged_data['cutoffEnd'] = merged_data.apply(cutOffEndTime,axis=1)

#condition = [
#    ((merged_data['transactionTime'] < merged_data['cutoffEnd']) & (merged_data['settlementTxnTime'] > merged_data['cutoffEnd'])),
#    ((merged_data['transactionTime'] > merged_data['cutoffStart']) & (merged_data['settlementTxnTime'].isnull())),
#    (merged_data['settlementTxnTime'].isnull())
#]
#choice = ["Late settled",'After Cutoff Unsettled','Unsettled']
condition = [
    ((merged_data['transactionTime'] > merged_data['cutoffStart']) & (merged_data['settlementTxnTime'].isnull())),
    ((merged_data['transactionTime'] < merged_data['cutoffStart']) & (merged_data['settlementTxnTime'] > merged_data['cutoffEnd'])),
    (merged_data['settlementTxnTime'].isnull())
]
choice = ['After Cutoff Unsettled',"Late settled",'Unsettled']
merged_data['StatusReport'] = np.select(condition,choice,default='Settled')
groupped = merged_data.groupby(['transactionDate','tgName','acquirerName','StatusReport']).aggregate({'id':'nunique','amount':'sum'}).reset_index()

out = groupped.pivot_table(
    index=['transactionDate','tgName','acquirerName'],
    columns='StatusReport',
    values=['id','amount']).reset_index().fillna(0)

out.columns = pd.MultiIndex.from_tuples(out.set_axis(out.columns.values, axis=1).rename(columns={
                                                        ('transactionDate',             ''):('transactionDate',             'Transaction Date'),
                                                        (         'tgName',             ''):(         'tgName',             'TG Name'),
                                                        (         'acquirerName',             ''):(         'acquirerName',             'Acquirer'),
                                                        (         'amount', 'Late settled'): (         'amount', 'Late settled TXN value'),
                                                        (         'amount',      'Settled'):(         'amount',      'Settled TXN value'),
                                                        (         'amount',    'Unsettled'):(         'amount',      'Unsettled TXN value'),
                                                        (         'amount',    'After Cutoff Unsettled'):(         'amount', 'After Auto Settlement Start Time Unsettled TXN value'),
                                                        (             'id', 'After Cutoff Unsettled'):(             'id', 'After Auto Settlement Start Time Unsettled TXN'),
                                                        (             'id', 'Late settled'):(             'id', 'Late settled TXN'),
                                                        (             'id',      'Settled'):(             'id', 'Settled TXN'),
                                                        (             'id',    'Unsettled'):(             'id', 'Unsettled TXN')
                                                       }))
out = out.droplevel(level=0,axis=1)
#out.sort_values(by='Transaction Date',ascending=False,ignore_index=True,inplace=True)
column_list = ['Transaction Date','TG Name','Acquirer','Late settled TXN','Late settled TXN value','Unsettled TXN','Unsettled TXN value','After Auto Settlement Start Time Unsettled TXN','After Auto Settlement Start Time Unsettled TXN value','Settled TXN','Settled TXN value']
for column_name in column_list:
    if column_name not in out.columns:
        out[column_name] = 0

Unsettled_Summary = (merged_data[(merged_data["StatusReport"]=='After Cutoff Unsettled') & (merged_data['transactionDate'] ==(datetime.now() - timedelta(days=1)).strftime('%d-%m-%Y')) ]
 .groupby(['acquirerName','tgName'])
 .aggregate({'terminalID':'nunique','id':'count','amount':'sum'}).reset_index()
)
out = out.sort_values(["Acquirer","TG Name","Transaction Date"],ascending=[True,True,False]).reset_index(drop=True)
FinalToWrite_others = out[~out['TG Name'].isin(['HDFC']) ][['Acquirer',"TG Name",'Transaction Date','Late settled TXN','Late settled TXN value','Unsettled TXN','Unsettled TXN value','After Auto Settlement Start Time Unsettled TXN','After Auto Settlement Start Time Unsettled TXN value']].reset_index(drop=True)
FinalToWrite_others = pd.merge(FinalToWrite_others, Cutoff, how='inner',left_on=['Acquirer','TG Name'],right_on=['Acquirer','Tg'])
FinalToWrite_others = FinalToWrite_others[['Acquirer',"TG Name",'Transaction Date','Bank Settlement Cutoff Time','Bank Settlement Start Time','Late settled TXN','Late settled TXN value','Unsettled TXN','Unsettled TXN value','After Auto Settlement Start Time Unsettled TXN','After Auto Settlement Start Time Unsettled TXN value']].rename({
    'Bank Settlement Cutoff Time':'Bank Settlement Cutoff Time',
    'Bank Settlement Start Time':'Auto Settlement Start Time'
},axis=1)
FinalToWrite_others['Bank Settlement Cutoff Time'] = FinalToWrite_others['Bank Settlement Cutoff Time'].map(lambda x : f"{x} PM" if int(x[:2]) > 12 else f"{x} AM")
FinalToWrite_others['Auto Settlement Start Time'] = FinalToWrite_others['Auto Settlement Start Time'].map(lambda x : f"{x} PM" if int(x[:2]) > 12 else f"{x} AM")
FinalToWrite_HDFC = out[out['TG Name'].isin(['HDFC']) ][['Acquirer',"TG Name",'Transaction Date','Late settled TXN','Late settled TXN value','Unsettled TXN','Unsettled TXN value']].reset_index(drop=True)
Unsettled_Summary=Unsettled_Summary.rename({"acquirerName":"Acquirer","tgName":"TG","terminalID":"User Count","id":"Volume","amount":"Value"},axis=1).sort_values(by=['Acquirer','TG','Volume'],axis=0).reset_index(drop=True)
Unsettled_Summary_final = pd.merge(Unsettled_Summary, Cutoff, how='inner',left_on=['Acquirer','TG'],right_on=['Acquirer','Tg'])
Unsettled_Summary_final = Unsettled_Summary_final[["Acquirer",'TG','Bank Settlement Start Time',"User Count","Volume","Value"]].rename({"Bank Settlement Start Time":"Auto Settlement Start Time"},axis=1)
Unsettled_Summary_final["Auto Settlement Start Time"] = Unsettled_Summary_final["Auto Settlement Start Time"].map(lambda x : f"{x} PM" if int(x[:2]) > 12 else f"{x} AM")
Unsettled_Summary_final = Unsettled_Summary_final.sort_values(by=['User Count'],ascending=[False])
with pd.ExcelWriter(output_file1,engine='openpyxl') as writer:
    Settled_Summary.to_excel(writer,sheet_name="Settled Transactions Summary",index=False)
    Unsettled_Summary_final.to_excel(writer,sheet_name="Txn after auto settlement start unsettled txn summary",index=False)
    FinalToWrite_HDFC.to_excel(writer,sheet_name="HDFC TG Late Settled & Unsettled Txn Summary",index=False)
    FinalToWrite_others.to_excel(writer,sheet_name="Other TG Late Settled & Unsettled Txn Summary" , index=False)
fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(output_file1)
fix_worksheet.process_all_worksheets()

Unsettled_TXN = merged_data[(merged_data["StatusReport"] == "Unsettled") & (merged_data['transactionDate'] == (datetime.now() - timedelta(days=1)).strftime('%d-%m-%Y'))][['id', 'transactionTime', 'userName', 'Type', 'mode', 'amount', 'tip',
       'Auth Code', 'Card', 'Card Type', 'rrn', 'Invoice',
       'StatusReport', 'settlementTxnTime', 'MID', 'terminalID',
       'batch', 'TxnTime', 'txnStatusName', 'merchantName',
       'acquirerName', 'dbaName','tgName']].reset_index(drop=True)

Unsettled_TXN.columns = ['Id', 'Date', 'userName', 'Type', 'Mode', 'Amount', 'Tip',
       'Auth Code', 'Card', 'Card Type', 'RRN', 'Invoice', 'Status',
       'Settled On', 'MID', 'TID', 'Batch', 'Time',
       'Transaction Status', 'Me Name', 'Acquirer', 'DBA Name','TG Name']
Latesettled_TXN = merged_data[(merged_data["StatusReport"] == "Late settled") & (merged_data['transactionDate'] == (datetime.now() - timedelta(days=1)).strftime('%d-%m-%Y'))][['id', 'transactionTime', 'userName', 'Type', 'mode', 'amount', 'tip',
       'Auth Code', 'Card', 'Card Type', 'rrn', 'Invoice',
       'StatusReport', 'settlementTxnTime', 'MID', 'terminalID',
       'batch', 'TxnTime', 'txnStatusName', 'merchantName',
       'acquirerName', 'dbaName','tgName']].reset_index(drop=True)
Latesettled_TXN.columns = ['Id', 'Date', 'userName', 'Type', 'Mode', 'Amount', 'Tip',
       'Auth Code', 'Card', 'Card Type', 'RRN', 'Invoice', 'Status',
       'Settled On', 'MID', 'TID', 'Batch', 'Time',
       'Transaction Status', 'Me Name', 'Acquirer', 'DBA Name','TG Name']

ATOS_Data = data_retrival(ATOS_Transactions)
IDFC_Data = data_retrival(IDFC_Transactions)
with pd.ExcelWriter(output_file2) as writer:
    ATOS_Data.to_excel(writer,index=False)

with pd.ExcelWriter(output_file3) as writer:
    IDFC_Data.to_excel(writer,index=False)

with pd.ExcelWriter(output_file4) as writer:
    Unsettled_TXN.to_excel(writer,sheet_name="Unsettled Transactions" , index=False)
    Latesettled_TXN.to_excel(writer,sheet_name="Late Settled Transactions" , index=False)

fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(output_file2)
fix_worksheet.process_all_worksheets()
fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(output_file3)
fix_worksheet.process_all_worksheets()

fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(output_file4)
fix_worksheet.process_all_worksheets()

print("File Creation is Successfull")
exit()
