===== Creating new session
=====     Server:   http://192.168.4.15:8080
=====     Username: tabadmin
===== Connecting to the server...
===== Signing in...
===== Succeeded
********
===== Continuing previous session
=====     Server:   http://192.168.4.15:8080
=====     Username: tabadmin
===== Requesting '/views/DeclinedTransactionSummary-Daily/DeclineTransactionSummaryAcquirerWise_1.csv' from the server...
===== Found attachment: DeclineTransactionSummaryAcquirerWise_1.csv
===== Saved /views/DeclinedTransactionSummary-Daily/DeclineTransactionSummaryAcquirerWise_1.csv to 'DeclineTransactionSummaryAcquirerWise_1.csv'
===== Continuing previous session
=====     Server:   http://192.168.4.15:8080
=====     Username: tabadmin
===== Requesting '/views/DeclinedTransactionSummary-Daily/AcquirerWiseDeclineSummary.csv' from the server...
===== Found attachment: AcquirerWiseDeclineSummary.csv
===== Saved /views/DeclinedTransactionSummary-Daily/AcquirerWiseDeclineSummary.csv to 'AcquirerWiseDeclineSummary.csv'
===== Continuing previous session
=====     Server:   http://192.168.4.15:8080
=====     Username: tabadmin
===== Requesting '/DeclinedTransactionSummary-Daily/SuccessCount_1' from the server...
===== Writing response to 'Success_Count.csv'
===== Saved /DeclinedTransactionSummary-Daily/SuccessCount_1 to 'Success_Count.csv'
===== Continuing previous session
=====     Server:   http://192.168.4.15:8080
=====     Username: tabadmin
===== Requesting '/DeclinedTransactionSummary-Daily/INDIGODeclineSummary' from the server...
===== Writing response to 'Indigo_Declined_summary.csv'
===== Saved /DeclinedTransactionSummary-Daily/INDIGODeclineSummary to 'Indigo_Declined_summary.csv'
===== Continuing previous session
=====     Server:   http://192.168.4.15:8080
=====     Username: tabadmin
===== Requesting '/views/DeclinedTransactionSummary-Daily/DeclineTransactionSummaryAcquirerWise_1.png' from the server...
===== Found attachment: DeclineTransactionSummaryAcquirerWise_1.png
===== Saved /views/DeclinedTransactionSummary-Daily/DeclineTransactionSummaryAcquirerWise_1.png to 'DeclineTransactionSummaryAcquirerWise_1.png'
===== Continuing previous session
=====     Server:   http://192.168.4.15:8080
=====     Username: tabadmin
===== Requesting '/views/DeclinedTransactionSummary-Daily/AcquirerWiseDeclineSummary.png' from the server...
===== Found attachment: AcquirerWiseDeclineSummary.png
===== Saved /views/DeclinedTransactionSummary-Daily/AcquirerWiseDeclineSummary.png to 'AcquirerWiseDeclineSummary.png'
===== Continuing previous session
=====     Server:   http://192.168.4.15:8080
=====     Username: tabadmin
===== Requesting '/views/DeclinedTransactionSummary-Daily/SuccessCount_1.png' from the server...
===== Found attachment: SuccessCount_1.png
===== Saved /views/DeclinedTransactionSummary-Daily/SuccessCount_1.png to 'SuccessCount_1.png'
===== Continuing previous session
=====     Server:   http://192.168.4.15:8080
=====     Username: tabadmin
===== Requesting '/views/DeclinedTransactionSummary-Daily/INDIGODeclineSummary.png' from the server...
===== Found attachment: INDIGODeclineSummary.png
===== Saved /views/DeclinedTransactionSummary-Daily/INDIGODeclineSummary.png to 'INDIGODeclineSummary.png'
===== redirecting to http://192.168.4.15:8080/auth
===== Signed out
/home/<USER>/.local/lib/python3.8/site-packages/openpyxl/workbook/child.py:99: UserWarning: Title is more than 31 characters. Some applications may not be able to read the file
  warnings.warn("Title is more than 31 characters. Some applications may not be able to read the file")
['/home/<USER>/Reports/Daily_Declined_Txn_acquirer_summary/********/DeclineTransactionSummaryAcquirerWise_1.png', '/home/<USER>/Reports/Daily_Declined_Txn_acquirer_summary/********/INDIGODeclineSummary.png', '/home/<USER>/Reports/Daily_Declined_Txn_acquirer_summary/********/AcquirerWiseDeclineSummary.png', '/home/<USER>/Reports/Daily_Declined_Txn_acquirer_summary/********/SuccessCount_1.png']
Email are sent successfully!
Completed
===== Creating new session
=====     Server:   http://192.168.4.15:8080
=====     Username: tabadmin
===== Connecting to the server...
===== Signing in...
===== Succeeded
********
===== Continuing previous session
=====     Server:   http://192.168.4.15:8080
=====     Username: tabadmin
===== Requesting '/views/DeclinedTransactionSummary-Daily/DeclineTransactionSummaryAcquirerWise_1.csv' from the server...
===== Found attachment: DeclineTransactionSummaryAcquirerWise_1.csv
===== Saved /views/DeclinedTransactionSummary-Daily/DeclineTransactionSummaryAcquirerWise_1.csv to 'DeclineTransactionSummaryAcquirerWise_1.csv'
===== Continuing previous session
=====     Server:   http://192.168.4.15:8080
=====     Username: tabadmin
===== Requesting '/views/DeclinedTransactionSummary-Daily/AcquirerWiseDeclineSummary.csv' from the server...
===== Found attachment: AcquirerWiseDeclineSummary.csv
===== Saved /views/DeclinedTransactionSummary-Daily/AcquirerWiseDeclineSummary.csv to 'AcquirerWiseDeclineSummary.csv'
===== Continuing previous session
=====     Server:   http://192.168.4.15:8080
=====     Username: tabadmin
===== Requesting '/DeclinedTransactionSummary-Daily/SuccessCount_1' from the server...
===== Writing response to 'Success_Count.csv'
===== Saved /DeclinedTransactionSummary-Daily/SuccessCount_1 to 'Success_Count.csv'
===== Continuing previous session
=====     Server:   http://192.168.4.15:8080
=====     Username: tabadmin
===== Requesting '/DeclinedTransactionSummary-Daily/INDIGODeclineSummary' from the server...
===== Writing response to 'Indigo_Declined_summary.csv'
===== Saved /DeclinedTransactionSummary-Daily/INDIGODeclineSummary to 'Indigo_Declined_summary.csv'
===== Continuing previous session
=====     Server:   http://192.168.4.15:8080
=====     Username: tabadmin
===== Requesting '/views/DeclinedTransactionSummary-Daily/DeclineTransactionSummaryAcquirerWise_1.png' from the server...
===== Found attachment: DeclineTransactionSummaryAcquirerWise_1.png
===== Saved /views/DeclinedTransactionSummary-Daily/DeclineTransactionSummaryAcquirerWise_1.png to 'DeclineTransactionSummaryAcquirerWise_1.png'
===== Continuing previous session
=====     Server:   http://192.168.4.15:8080
=====     Username: tabadmin
===== Requesting '/views/DeclinedTransactionSummary-Daily/AcquirerWiseDeclineSummary.png' from the server...
===== Found attachment: AcquirerWiseDeclineSummary.png
===== Saved /views/DeclinedTransactionSummary-Daily/AcquirerWiseDeclineSummary.png to 'AcquirerWiseDeclineSummary.png'
===== Continuing previous session
=====     Server:   http://192.168.4.15:8080
=====     Username: tabadmin
===== Requesting '/views/DeclinedTransactionSummary-Daily/SuccessCount_1.png' from the server...
===== Found attachment: SuccessCount_1.png
===== Saved /views/DeclinedTransactionSummary-Daily/SuccessCount_1.png to 'SuccessCount_1.png'
===== Continuing previous session
=====     Server:   http://192.168.4.15:8080
=====     Username: tabadmin
===== Requesting '/views/DeclinedTransactionSummary-Daily/INDIGODeclineSummary.png' from the server...
===== Found attachment: INDIGODeclineSummary.png
===== Saved /views/DeclinedTransactionSummary-Daily/INDIGODeclineSummary.png to 'INDIGODeclineSummary.png'
===== redirecting to http://192.168.4.15:8080/auth
===== Signed out
/home/<USER>/.local/lib/python3.8/site-packages/openpyxl/workbook/child.py:99: UserWarning: Title is more than 31 characters. Some applications may not be able to read the file
  warnings.warn("Title is more than 31 characters. Some applications may not be able to read the file")
['/home/<USER>/Reports/Daily_Declined_Txn_acquirer_summary/********/DeclineTransactionSummaryAcquirerWise_1.png', '/home/<USER>/Reports/Daily_Declined_Txn_acquirer_summary/********/INDIGODeclineSummary.png', '/home/<USER>/Reports/Daily_Declined_Txn_acquirer_summary/********/AcquirerWiseDeclineSummary.png', '/home/<USER>/Reports/Daily_Declined_Txn_acquirer_summary/********/SuccessCount_1.png']
Email are sent successfully!
Completed
/home/<USER>/Reports/Scripts/SettlementReport/main.py:24: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.
  merged_data['Bank Settlement Start Time'] = pd.to_datetime(merged_data['Bank Settlement Start Time'])
/home/<USER>/Reports/Scripts/SettlementReport/main.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.
  merged_data['Bank Settlement Cutoff Time'] = pd.to_datetime(merged_data['Bank Settlement Cutoff Time'])
/home/<USER>/.local/lib/python3.8/site-packages/openpyxl/workbook/child.py:99: UserWarning: Title is more than 31 characters. Some applications may not be able to read the file
  warnings.warn("Title is more than 31 characters. Some applications may not be able to read the file")
File Creation is Successfull
Email are sent successfully!
Completed
/home/<USER>/Reports/Scripts/SettlementReport/main.py:24: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.
  merged_data['Bank Settlement Start Time'] = pd.to_datetime(merged_data['Bank Settlement Start Time'])
/home/<USER>/Reports/Scripts/SettlementReport/main.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.
  merged_data['Bank Settlement Cutoff Time'] = pd.to_datetime(merged_data['Bank Settlement Cutoff Time'])
/usr/lib/python3/dist-packages/requests/__init__.py:89: RequestsDependencyWarning: urllib3 (2.2.3) or chardet (3.0.4) doesn't match a supported version!
  warnings.warn("urllib3 ({}) or chardet ({}) doesn't match a supported "
Traceback (most recent call last):
  File "/home/<USER>/Reports/Scripts/SettlementReport/main.py", line 108, in <module>
    Unsettled_Summary_final.to_excel(writer,sheet_name="Txn after auto settlement start unsettled txn summary",index=False)
  File "/home/<USER>/.local/lib/python3.8/site-packages/pandas/core/generic.py", line 2252, in to_excel
    formatter.write(
  File "/home/<USER>/.local/lib/python3.8/site-packages/pandas/io/formats/excel.py", line 940, in write
    writer._write_cells(
  File "/home/<USER>/.local/lib/python3.8/site-packages/pandas/io/excel/_xlsxwriter.py", line 245, in _write_cells
    wks = self.book.add_worksheet(sheet_name)
  File "/home/<USER>/.local/lib/python3.8/site-packages/xlsxwriter/workbook.py", line 197, in add_worksheet
    return self._add_sheet(name, worksheet_class=worksheet_class)
  File "/home/<USER>/.local/lib/python3.8/site-packages/xlsxwriter/workbook.py", line 817, in _add_sheet
    name = self._check_sheetname(name, isinstance(worksheet, Chartsheet))
  File "/home/<USER>/.local/lib/python3.8/site-packages/xlsxwriter/workbook.py", line 869, in _check_sheetname
    raise InvalidWorksheetName(
xlsxwriter.exceptions.InvalidWorksheetName: Excel worksheet name 'Txn after auto settlement start unsettled txn summary' must be <= 31 chars.
/usr/lib/python3/dist-packages/requests/__init__.py:89: RequestsDependencyWarning: urllib3 (2.2.3) or chardet (3.0.4) doesn't match a supported version!
  warnings.warn("urllib3 ({}) or chardet ({}) doesn't match a supported "
Traceback (most recent call last):
  File "/home/<USER>/Reports/Scripts/SettlementReport/mail.py", line 64, in <module>
    with open(f2, "rb") as attached_file:
FileNotFoundError: [Errno 2] No such file or directory: '/home/<USER>/Reports/SettlementReport/********/ATOSUnsettledTXN_23_May_25.xlsx'
Completed
/home/<USER>/Reports/Scripts/SettlementReport/main.py:24: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.
  merged_data['Bank Settlement Start Time'] = pd.to_datetime(merged_data['Bank Settlement Start Time'])
/home/<USER>/Reports/Scripts/SettlementReport/main.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.
  merged_data['Bank Settlement Cutoff Time'] = pd.to_datetime(merged_data['Bank Settlement Cutoff Time'])
/home/<USER>/.local/lib/python3.8/site-packages/openpyxl/workbook/child.py:99: UserWarning: Title is more than 31 characters. Some applications may not be able to read the file
  warnings.warn("Title is more than 31 characters. Some applications may not be able to read the file")
File Creation is Successfull
Email are sent successfully!
Completed
