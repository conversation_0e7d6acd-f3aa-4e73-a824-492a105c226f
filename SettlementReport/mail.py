import os
import sys
import smtplib
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from email.mime.image import MIMEImage
from enviroment import From,server_name,to,Port,Password,Username
from datetime import datetime, timedelta
import glob
import pandas as pd
from DataQuery import output_file1,output_file2,output_file3,output_file4
#print(os.getcwd())

msg = MIMEMultipart()
msg['From'] = From
msg["To"] = ','.join(to)
#msg["Cc"] = to
msg['Subject'] = f"Settlement Report Dated  {(datetime.now() - timedelta(1)).strftime('%d-%m-%Y')}"
folder_name = sys.argv[1]
file_name = sys.argv[2]

htmlEmail = f"""
<p> Dear Sir/Madam, <br/><br/>
</p>
"""

#msg.attach(MIMEText(htmlEmail, 'html'))
f = output_file1
#f = os.path.join(os.getcwd(),folder_name,f"{folder_name} {file_name}")
#html tables data
xl = pd.ExcelFile(f)
sheet_name = xl.sheet_names  # see all sheet names
def generate_html(f,sheet):
    read_file = pd.read_excel(f,sheet_name=sheet)
    html_file = f'''<h5>{sheet} <h5><br/>
    {read_file.to_html(index=False,na_rep='',)}'''
    return html_file
for sheet in sheet_name:
    html_txt = generate_html(f,sheet)
    htmlEmail = "<br/>".join([htmlEmail,html_txt])
    
htmlEmail2 = """
<p> Please contact Mosambee Support Team (<EMAIL>) directly if you have any questions. <br/>
    Thank you! <br/>
    Best Regards, <br/>
    Mosambee Support Team </p>
<br/>
<font color="red">Please do not reply to this email as it is auto-generated. </font>
"""
htmlEmail = "<br/>".join([htmlEmail,htmlEmail2])
msg.attach(MIMEText(htmlEmail, 'html'))

with open(f, "rb") as attached_file:
    part = MIMEApplication(
            attached_file.read(),
            Name=os.path.basename(f)
            )
# After the file is closed
part['Content-Disposition'] = 'attachment; filename="%s"' % os.path.basename(f)
msg.attach(part)

f2 = output_file2
with open(f2, "rb") as attached_file:
    part = MIMEApplication(
            attached_file.read(),
            Name=os.path.basename(f2)
            )
# After the file is closed
part['Content-Disposition'] = 'attachment; filename="%s"' % os.path.basename(f2)
msg.attach(part)
f3 = output_file3
with open(f3, "rb") as attached_file:
    part = MIMEApplication(
            attached_file.read(),
            Name=os.path.basename(f3)
            )
# After the file is closed
part['Content-Disposition'] = 'attachment; filename="%s"' % os.path.basename(f3)
msg.attach(part)

f4 = output_file4
with open(f4, "rb") as attached_file:
    part = MIMEApplication(
            attached_file.read(),
            Name=os.path.basename(f4)
            )
# After the file is closed
part['Content-Disposition'] = 'attachment; filename="%s"' % os.path.basename(f4)
msg.attach(part)


server = smtplib.SMTP(server_name, Port)
server.starttls()
server.ehlo()
server.login(Username,Password)
text = msg.as_string()
server.sendmail(From, to, text)
server.quit()

print("Email are sent successfully!")

