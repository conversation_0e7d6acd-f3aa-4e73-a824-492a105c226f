from sys import exit,argv
import os
import pandas as pd
import numpy as np
import string
import XLSXAutoFitColumns
from datetime import date,timedelta
from openpyxl.styles import Font, Fill,Border


reportName = argv[1]
foldername = argv[2]
dir = argv[3]
os.chdir(os.path.join(dir,foldername))
print(f'current Directory {os.getcwd()}')
def Convert_pect(df,col):
    df[col] = pd.Series(["{0:.2f}%".format(val * 100) for val in df[col]], index = df.index)

all_transactions = pd.read_csv("AcquirerWiseTransactionSummary.csv")

all_transactions.rename({"Transaction Type _ Update":"Transaction Type"},axis=1,inplace=True)

all_transactions = all_transactions[all_transactions["Acquirer"]!='All']
all_transactions = all_transactions[all_transactions["Transaction Type"]!='All']
all_transactions_pivot = all_transactions.pivot(index=["Acquirer","Transaction Type"],columns="Measure Names",values="Measure Values")
all_transactions_pivot = all_transactions_pivot.reset_index()
all_transactions_pivot = all_transactions_pivot[['Transaction Type','Acquirer', 'Volume', 'Value']]
all_transactions_pivot = all_transactions_pivot.sort_values(["Transaction Type","Acquirer"])
all_transactions_pivot = all_transactions_pivot.reset_index(drop=True)
all_transactions_pivot["Volume"]=all_transactions_pivot["Volume"].str.replace(",",'').astype('int64')
all_transactions_pivot["Value"]=all_transactions_pivot["Value"].str.replace(",",'').astype('int64')
all_Transaction = all_transactions_pivot[all_transactions_pivot["Transaction Type"]!='MATM Approved']
Matm = all_transactions_pivot[all_transactions_pivot["Transaction Type"]=='MATM Approved']

file_name = "All_Transactions.xlsx"
with pd.ExcelWriter("All_Transactions.xlsx") as writer:
    total_value = 0
    total_volume = 0
    startrow_var=1
    all_Transaction.reset_index(inplace=True,drop=True)
    for transactions in all_Transaction['Transaction Type'].unique():
        print(transactions)
        Data =  all_Transaction[all_Transaction["Transaction Type"]==transactions]
        Data = Data[['Acquirer', 'Volume', 'Value']]
        grand_total = pd.DataFrame({'Acquirer':'Grand Total',
                       'Volume':Data['Volume'].sum(),
                       'Value' : Data['Value'].sum()
                      },index=[0])
        total_value+=int(grand_total.iloc[0,2])
        total_volume+= int(grand_total.iloc[0,1])
        Data.sort_values("Volume",ascending=False,inplace=True)
        Data_final = pd.concat([Data,grand_total])
        Data_final = Data_final.reset_index(drop=True).set_index('Acquirer')
        header = f'{transactions} Transaction Details {date.today()-timedelta(days = 1)}'
        Data_final.to_excel(writer,sheet_name='Report',startrow=startrow_var)
        worksheet = writer.sheets['Report']
        worksheet.cell(startrow_var, 1, value=header)
        h = worksheet[f"A{startrow_var}"]
        h.font = Font(size=12,bold=True)
        #print(f"A{startrow_var+Data_final.shape[0]}:C{startrow_var+Data_final.shape[0]}")
        db = worksheet[f"B{startrow_var+Data_final.shape[0]+1}"]
        db.font = Font(size=11,bold=True)
        dc = worksheet[f"C{startrow_var+Data_final.shape[0]+1}"]
        dc.font = Font(size=11,bold=True)
        worksheet.merge_cells(f"A{startrow_var}:C{startrow_var}")
        startrow_var += (Data_final.shape[0]+3)
    Acquire_Cell = worksheet[f"A{startrow_var}"]
    Volume_Cell = worksheet[f"B{startrow_var}"]
    Value_Cell = worksheet[f"C{startrow_var}"]
    Acquire_Cell.value = "Grand Total"
    Acquire_Cell.font = Font(bold=True,size=12)
    Volume_Cell.value = total_volume
    Volume_Cell.font = Font(bold=True,size=12)
    Value_Cell.value = total_value
    Value_Cell.font = Font(bold=True,size=12)
    #MATM Append
    startrow_var += (Data_final.shape[0]+1)
    grand_total = pd.DataFrame({'Acquirer':'Grand Total',
                   'Volume':Matm['Volume'].sum(),
                   'Value' : Matm['Value'].sum()
                  },index=[0])
    Matm = Matm[['Acquirer', 'Volume', 'Value']]
    Matm.reset_index(inplace=True,drop=True)
    Matm.sort_values("Volume",ascending=False,inplace=True)
    Matm_Final = pd.concat([Matm,grand_total])
    Matm_Final = Matm_Final.reset_index(drop=True).set_index('Acquirer')
    header = f'MATM Approved Transaction Details {date.today()-timedelta(days = 1)}'
    Matm_Final.to_excel(writer,sheet_name='Report',startrow=startrow_var)
    worksheet = writer.sheets['Report']
    worksheet.cell(startrow_var, 1, value=header)
    h = worksheet[f"A{startrow_var}"]
    h.font = Font(size=12,bold=True)
    #print(f"A{startrow_var+Data_final.shape[0]}:C{startrow_var+Data_final.shape[0]}")
    db = worksheet[f"B{startrow_var+Matm_Final.shape[0]+1}"]
    db.font = Font(size=11,bold=True)
    dc = worksheet[f"C{startrow_var+Matm_Final.shape[0]+1}"]
    dc.font = Font(size=11,bold=True)
    worksheet.merge_cells(f"A{startrow_var}:C{startrow_var}")


fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(file_name)
fix_worksheet.process_all_worksheets()

exit()

