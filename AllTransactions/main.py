from datetime import datetime,timedelta
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import query,config,PayByLink_query,input_file,output_file,format_int_with_commas,History_Input
import XLSXAutoFitColumns


data = data_retrival(query)
PayByLinkData = data_retrival(PayByLink_query)
data = pd.concat([data,PayByLinkData]).reset_index(drop=True)
test_tid = pd.read_csv("/home/<USER>/Reports/Scripts/AllTransactions/Test_TID_List.csv")
data = data[~data['terminalID'].isin(test_tid['TID'])].reset_index(drop=True)

data.drop_duplicates(subset=['id'],inplace=True,keep='last')
data.reset_index(inplace=True,drop=True)
data_grouped = (data
 .groupby(['transactionTypeName','acquirerName'])
 .aggregate({'id':'count','amount':'sum'}).reset_index()
)

data_grouped.replace({"Cash Withdrawal":"MATM"},inplace=True)
acquirerTxnTypeWise = pd.read_csv(input_file)
Result = pd.merge(acquirerTxnTypeWise, data_grouped,  how='left', left_on=["transactionTypeName"],
                  right_on = ['transactionTypeName'])

Result.fillna(0,inplace=True)
TxnTypeList = list(acquirerTxnTypeWise.transactionTypeName.unique())
Result.sort_values(['TransactionTypeOrder','id','amount'],ascending=False,inplace=True)
Result.drop(['TransactionTypeOrder'],inplace=True,axis=1)
#History_data = pd.read_excel("History_AllTransaction.xlsx",sheet_name='Sheet1')
#with pd.ExcelWriter("History_AllTransaction.xlsx") as Historywriter:
 #   if (datetime.now() - timedelta(1)).strftime("%d")!= '01':
        #final = pd.concat([History_data,Result]).reset_index(drop=True)
  #      final = Result.copy()
   # else:
    #    final = Result.copy()
   # final.to_excel(Historywriter,"Sheet1",index=False)

#NEW CODE START for Monthly
if (datetime.now() - timedelta(1)).strftime("%d") != '01':
    if os.path.exists(History_Input):
        History_data = pd.read_excel(History_Input,sheet_name='Sheet1')
        final = pd.concat([History_data,Result]).reset_index(drop=True)
    else:
        final = Result.copy()
else:
    final = Result.copy()

with pd.ExcelWriter(History_Input) as Historywriter:
    final.to_excel(Historywriter,'Sheet1',index=False)

#New Code END for Monthly


with pd.ExcelWriter(output_file) as writer:
    for type in TxnTypeList:
        if type == 'Grand Total':
            finalToWriteDict = {
            "Acquirer" : "Grand Total",
            "Volume"  : int(Result[Result["transactionTypeName"]!='MATM']["id"].sum()),
            "Value" : Result[Result["transactionTypeName"]!='MATM']["amount"].sum()
        }
            finalToWrite =  pd.DataFrame(finalToWriteDict,index=[0])
        else:
            finalToWrite = (Result
                            .loc[Result["transactionTypeName"]==type]
                            .drop(["transactionTypeName"],axis=1)
                            .astype({"acquirerName":"object","id":"int","amount":"float"})
                            .rename({"acquirerName":"Acquirer","id":"Volume","amount":"Value"},axis=1)
                            .reset_index(drop=True)
                        )
            grand_total_dict = {
                "Acquirer" : "Grand Total",
                "Volume"  : finalToWrite["Volume"].sum(),
                "Value" : finalToWrite["Value"].sum()
            }
            grand_total = pd.DataFrame(grand_total_dict,index=[0])
            finalToWrite = pd.concat([finalToWrite,grand_total]).reset_index(drop=True)
        finalToWrite["Value"] = finalToWrite.Value.map(format_int_with_commas)
        finalToWrite["Volume"] = finalToWrite.Volume.map(format_int_with_commas)
        finalToWrite.to_excel(writer,type,index=False)

print("File Creation is Successfull")
exit()
