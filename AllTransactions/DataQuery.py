import os 
import sys

from datetime import date
from datetime import timedelta

out_dir="/home/<USER>/Reports/All_Transactions_Daily/"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%d%m%Y")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
input_file = "/home/<USER>/Reports/Scripts/AllTransactions/AcquirerListTXNWise.csv"
output_file = os.path.join(out_dir,yesterday_file_name,"All_Transactions.xlsx")
History_Input = "/home/<USER>/Reports/Scripts/AllTransactions/Monthly_TransactionSummary.xlsx"
def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,.2f}"

#in_dir = sys.argv[1]
#out_dir = sys.argv[2]
config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}

query = """
SELECT 
    t.id,
    CASE
    WHEN t.typeID = 1 AND t.isTip IS NOT NULL THEN 'Tip Adjusted'
    WHEN t.typeID IN (22, 24) THEN
        CASE
            WHEN t.acquirerName = 'HDFC' AND tp.program_id IN (37, 95) THEN CONCAT(t.cardTypeName, ' Sound Box')
            WHEN tp.program_id IN (111, 38) THEN CONCAT(t.transactionTypeName, ' Sound Box')
            WHEN t.acquirerName = 'HDFC' THEN CONCAT(t.cardTypeName, ' POS')  -- HDFC, other program IDs
            ELSE CONCAT(t.transactionTypeName, ' POS')  -- Default for other acquirers
        END
    ELSE t.transactionTypeName
END AS transactionTypeName,
    -- CASE
--         WHEN t.typeID = 1 AND t.isTip IS NOT NULL THEN 'Tip Adjusted'
--         WHEN
--             t.typeID IN (22 , 24)
--                 AND t.acquirerName = 'HDFC'
--         THEN
--             CASE
--                 WHEN tp.program_id IN (37 , 95) THEN CONCAT(t.cardTypeName, ' Sound Box')
--                 ELSE CONCAT(t.cardTypeName, ' POS')
--             END
--         WHEN
--             t.typeid IN (22 , 24)
--                 AND t.acquirerName != 'HDFC'
--         THEN
--             CONCAT(t.transactionTypeName, ' POS')
--         ELSE t.transactionTypeName
--     END AS transactionTypeName,
    CASE
        WHEN
            t.acquirerName = 'HDFC'
                AND tp.program_id = 97
        THEN
            'HDFC-AIO'
        ELSE t.acquirerName
    END AS acquirerName,
    CASE
        WHEN t.typeId = 15 AND t.emiType = 'debit' THEN t.additionalAmount / POWER(10, a.decimalAmountLength)
        ELSE t.amount / POWER(10, a.decimalAmountLength)
    END AS amount,
    CASE
        WHEN
            t.tempTID IS NOT NULL AND t.tempTID != 0
        THEN
            CASE
                WHEN t.tempTID != t.terminalID THEN t.tempTID
                ELSE t.terminalID
            END
        ELSE t.terminalID
    END AS terminalID
    FROM
    transactions t
        LEFT JOIN
    acquirer a ON t.acqId = a.id
        LEFT JOIN
    merchant m ON m.id = t.merchantID
        LEFT JOIN
    mapping_merchant_user_terminal mmut ON mmut.divisionId = m.id
        AND t.terminalID = mmut.terminalID 
        LEFT JOIN
        mapping_division_acquirer_tg mdat on mmut.divacqtgid = mdat.id
        inner JOIN
        mapping_acquirer_tg mat on mdat.acqTGID = mat.id
        LEFT JOIN 
    tbl_program tp ON mmut.programId = tp.program_id
WHERE
    t.transactionTime >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        AND t.transactionTime < CURDATE()
        AND t.userName IS NOT NULL
        AND (CASE
        WHEN
            t.typeId = 2
        THEN
            t.referenceTxnID NOT IN (SELECT 
                    t1.id
                FROM
                    transactions t1
                WHERE
                    t1.typeId = 8 AND statusId = 3
                        AND t1.id = t.referenceTxnID)
        ELSE 1
    END)
        AND t.typeID IN (1 , 27, 9, 15, 14, 36, 24, 22, 21)
        AND t.statusId IN (2 , 7)
        AND (isVoided is null or isVoided =0)
       AND case when mmut.status is Null then 'null'
            when mmut.status = 'D' and mmut.recordUpdated >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        AND mmut.recordUpdated < CURDATE() then 'A'
                        else mmut.status end in ('A','null')
       AND (CASE
        WHEN
            t.acquirerName = 'GPIN'
        THEN
            t.tgid = mat.tgid
        ELSE 1
    END)
;

"""

PayByLink_query = """
SELECT
       t.id,
    CASE
    WHEN t.typeID = 1 AND t.isTip IS NOT NULL THEN 'Tip Adjusted'
    WHEN t.typeID IN (22, 24) THEN
        CASE
            WHEN t.acquirerName = 'HDFC' AND tp.program_id IN (37, 95) THEN CONCAT(t.cardTypeName, ' Sound Box')
            WHEN tp.program_id IN (111, 38) THEN CONCAT(t.transactionTypeName, ' Sound Box')
            WHEN t.acquirerName = 'HDFC' THEN CONCAT(t.cardTypeName, ' POS')  -- HDFC, other program IDs
            ELSE CONCAT(t.transactionTypeName, ' POS')  -- Default for other acquirers
        END
    ELSE t.transactionTypeName
END AS transactionTypeName,
    -- CASE
--         WHEN t.typeID = 1 AND t.isTip IS NOT NULL THEN 'Tip Adjusted'
--         WHEN
--             t.typeID IN (22 , 24)
--                 AND t.acquirerName = 'HDFC'
--         THEN
--             CASE
--                 WHEN tp.program_id IN (37 , 95) THEN CONCAT(t.cardTypeName, ' Sound Box')
--                 ELSE CONCAT(t.cardTypeName, ' POS')
--             END
--         WHEN
--             t.typeid IN (22 , 24)
--                 AND t.acquirerName != 'HDFC'
--         THEN
--             CONCAT(t.transactionTypeName, ' POS')
--         ELSE t.transactionTypeName
--     END AS transactionTypeName,
    CASE
        WHEN
            t.acquirerName = 'HDFC'
                AND tp.program_id = 97
        THEN
            'HDFC-AIO'
        ELSE t.acquirerName
    END AS acquirerName,
    CASE
        WHEN t.typeid = 15 THEN t.additionalAmount / POWER(10, a.decimalAmountLength)
        ELSE t.amount / POWER(10, a.decimalAmountLength)
    END AS amount,
    CASE
        WHEN
            t.tempTID IS NOT NULL AND t.tempTID != 0
        THEN
            CASE
                WHEN t.tempTID != t.terminalID THEN t.tempTID
                ELSE t.terminalID
            END
        ELSE t.terminalID
    END AS terminalID
FROM
    transactions t
        LEFT JOIN
    acquirer a ON t.acqId = a.id
        LEFT JOIN
    merchant m ON m.id = t.merchantID
      LEFT JOIN
    mapping_merchant_user_terminal mmut ON  t.terminalID = mmut.terminalID
        LEFT JOIN
        mapping_division_acquirer_tg mdat on mmut.divacqtgid = mdat.id
        inner JOIN
        mapping_acquirer_tg mat on mdat.acqTGID = mat.id
        LEFT JOIN
    tbl_program tp ON mmut.programId = tp.program_id
WHERE
    t.transactionTime >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        AND t.transactionTime < CURDATE()
        AND t.userName IS NOT NULL
        AND (CASE
        WHEN
            t.typeId = 2
        THEN
            t.referenceTxnID NOT IN (SELECT
                    t1.id
                FROM
                    transactions t1
                WHERE
                    t1.typeId = 8 AND statusId = 3
                        AND t1.id = t.referenceTxnID)
        ELSE 1
    END)
        AND t.typeID IN (19)
        AND t.statusId IN (2 , 7)
        AND (isVoided is null or isVoided =0);
"""
