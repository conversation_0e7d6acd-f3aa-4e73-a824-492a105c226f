import os 
import sys
import datetime as dt
from datetime import timedelta,datetime,date
import subprocess
import shutil

out_dir="/home/<USER>/Reports/AggregationBeneficiaryReport"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%d%m%Y")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
Transacting_output_FileName = f'GPMerchantMaster.xlsx'

Transacting_output = os.path.join(out_dir,yesterday_file_name,Transacting_output_FileName)

def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

def date_serial_number(serial_number: int):
    """
    Convert an Excel serial number to a Python datetime object
    :param serial_number: the date serial number
    :return: a datetime object
    """
    # Excel stores dates as "number of days since 1900"
    delta = dt.datetime(1899, 12, 30) + dt.timedelta(days=serial_number)
    return delta

def tag_gp_merchants(df):
    """
    Tags POS IDs as 'GP Merchant' if they have at least one Acquirer 
    value of 'GPHYP' or 'GPIN'.
 
    Args:
        df (pandas.DataFrame): The input DataFrame with 'POSID' and 'Acquirer' columns.
 
    Returns:
        pandas.DataFrame: The DataFrame with a new 'Merchant Type' column.
    """
 
    # Create a boolean mask indicating POS IDs with 'GPHYP' or 'GPIN' Acquirers
    gp_merchant_mask = df.groupby('POSID')['Acquirer'].transform(lambda x: x.isin(['GPHYP', 'GPIN','GP']).any())
 
    # Create the 'Merchant Type' column based on the mask
    df['Merchant Type'] = gp_merchant_mask.map({True: 'GP Merchant', False: 'Non GP Merchant'})
 
    return df

zip_password = "GP@1234"
def zip_xlsx_file(file_path, password):
    """Creates a password-protected zip file of an .xlsx file inside a folder."""

    # Check if file exists and has .xlsx extension
    if not os.path.isfile(file_path) or not file_path.endswith(".xlsx"):
        print(f"Error: File '{file_path}' not found or is not an .xlsx file!")
        return

    folder_name = f"{os.path.splitext(file_path)[0]}"
    zip_file = f"{folder_name}.zip"

    # Create folder
    os.makedirs(folder_name, exist_ok=True)

    # Move file into folder
    new_file_path = os.path.join(folder_name, os.path.basename(file_path))
    os.rename(file_path, new_file_path)
    #shutil.move(file_path,new_file_path)
    # Run the zip command
    
    zip_command = ["zip", "-ej", "-P", password, zip_file, new_file_path]
    print(zip_file, folder_name , password)
    try:
        subprocess.run(zip_command, check=True)
        print(f"Folder '{folder_name}' containing '{file_path}' has been securely zipped as '{zip_file}'.")

        # Optionally remove the folder after zipping
    #    os.system(f"rm -rf {folder_name}")

    except subprocess.CalledProcessError as e:
        print(f"Error: Failed to create zip file. {e}")
#in_dir = sys.argv[1]
#out_dir = sys.argv[2]
config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}

TransactingQuery = """
With ExternalRecord as (SELECT
    m.id AS 'POSID',
    mmut.storeId AS 'Store ID',
    mdat.merchantCode AS 'ExternalMID',
    mmut.terminalID AS 'ExternalTID',
    mat.acquirerName AS Acquirer,
    m.businessName AS 'Merchant Legal Name',
    m.dbaName AS 'Merchant DBA Name',
    maba.acctNameBank AS 'Account Name',
    maba.acctNoBank AS 'Account Number',
    maba.ifscCode AS 'IFSC Code',
    m.recordCreated AS 'merchant_Creation',
    maba.bankName AS 'Bank Name',
    m.recordCreated AS 'Merchant creation Date',
    mmut.recordCreated AS 'TID creation Date',
    CASE
        WHEN mmut.status = 'D' THEN mmut.recordUpdated
        ELSE NULL
    END AS 'TID Deactivation  Date',
    mmut.status AS 'TID STATUS',
    mat.tgName
FROM
    mapping_merchant_user_terminal mmut
        LEFT JOIN
    mapping_division_acquirer_tg mdat ON mdat.id = mmut.divAcqTgId
        LEFT JOIN
    mapping_acquirer_tg mat ON mdat.acqTgId = mat.id
        LEFT JOIN
    merchant m ON mmut.divisionId = m.id
        LEFT JOIN
    acquirer a ON a.id = mat.acquirerID
        LEFT JOIN
    mapping_aggregator_bank_account maba ON maba.merchantID = m.id
WHERE
    mmut.programId NOT IN (19 , 20, 33, 32,126,139,138,137,136,135,134,133,132,131,130,129,128,127,112,125,124,123,122,121,120,119,118,117,116,115,114,113)
        AND m.merchantSource = 1
        AND (tgName Not IN ('HDFCUPI','GPCYBERPG','GPJUSPAYPG')
        AND (tgname like 'GP%' or mmut.programID = 30))
        and maba.acctReconcileType = 0),
	HDFCRecords as (
	   Select m.id AS 'POSID',
    mdat.merchantCode AS 'HDFCMID',
    mmut.terminalID AS 'HDFCTID'
FROM
    mapping_merchant_user_terminal mmut
        LEFT JOIN
    mapping_division_acquirer_tg mdat ON mdat.id = mmut.divAcqTgId
        LEFT JOIN
    mapping_acquirer_tg mat ON mdat.acqTgId = mat.id
        LEFT JOIN
    merchant m ON mmut.divisionId = m.id
        LEFT JOIN
    acquirer a ON a.id = mat.acquirerID
        LEFT JOIN
    mapping_aggregator_bank_account maba ON maba.merchantID = m.id
WHERE
    mmut.programId NOT IN (19 , 20, 33, 32,126,139,138,137,136,135,134,133,132,131,130,129,128,127,112,125,124,123,122,121,120,119,118,117,116,115,114,113)
        AND m.merchantSource = 1
        AND tgName Not IN ('HDFCUPI','GPCYBERPG','GPJUSPAYPG')
        AND tgName in ('HDFC','SETUUPI')
        and maba.acctReconcileType = 0
)
Select ER.* ,HR.HDFCMID,HR.HDFCTID  from ExternalRecord ER left Join HDFCRecords HR on ER.POSID = HR.POSID AND ER.ExternalMID != HR.HDFCMID and ER.ExternalTID != HR.HDFCTID
;
"""
