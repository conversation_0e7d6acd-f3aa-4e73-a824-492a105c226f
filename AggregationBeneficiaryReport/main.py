from datetime import datetime,timed<PERSON>ta
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import config,format_int_with_commas,tag_gp_merchants,date_serial_number,TransactingQuery,Transacting_output,zip_password,zip_xlsx_file
import XLSXAutoFitColumns

pd.options.display.float_format = '{:,.2f}'.format

data = data_retrival(TransactingQuery)
data.drop_duplicates(ignore_index=True,inplace=True)
final_data = data[['POSID', 'Store ID', 'ExternalMID', 'ExternalTID', 'HDFCMID', 'HDFCTID','Acquirer',
       'Merchant Legal Name', 'Merchant DBA Name', 'Account Name',
       'Account Number', 'IFSC Code', 'merchant_Creation', 'Bank Name',
       'Merchant creation Date', 'TID creation Date', 'TID Deactivation  Date',
       'TID STATUS', 'tgName']]
final_data.loc[final_data['tgName'] == 'HDFC', 'HDFCMID'] = final_data['ExternalMID']
final_data.loc[final_data['tgName'] == 'HDFC', 'HDFCTID'] = final_data['ExternalTID']
final_data = tag_gp_merchants(final_data)
with pd.ExcelWriter(Transacting_output) as writer:
    final_data.to_excel(writer,index=False,sheet_name="Sheet1")
fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(Transacting_output)
fix_worksheet.process_all_worksheets()

zip_xlsx_file(Transacting_output, zip_password)

#fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(Non_Transacting_output)
#fix_worksheet.process_all_worksheets()
print("File Creation is Successfull")
exit()
