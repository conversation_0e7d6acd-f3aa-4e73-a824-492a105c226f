  adding: GPMerchantMaster.xlsx (deflated 8%)
/home/<USER>/Reports/Scripts/AggregationBeneficiaryReport/DataQuery.py:53: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  df['Merchant Type'] = gp_merchant_mask.map({True: 'GP Merchant', False: 'Non GP Merchant'})
/home/<USER>/Reports/AggregationBeneficiaryReport/26052025/GPMerchantMaster.zip /home/<USER>/Reports/AggregationBeneficiaryReport/26052025/GPMerchantMaster GP@1234
Folder '/home/<USER>/Reports/AggregationBeneficiaryReport/26052025/GPMerchantMaster' containing '/home/<USER>/Reports/AggregationBeneficiaryReport/26052025/GPMerchantMaster.xlsx' has been securely zipped as '/home/<USER>/Reports/AggregationBeneficiaryReport/26052025/GPMerchantMaster.zip'.
File Creation is Successfull
['GPMerchantMaster.zip']
Email are sent successfully!
Completed
