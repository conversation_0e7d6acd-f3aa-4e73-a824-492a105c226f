import os
import sys
import smtplib
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from email.mime.image import MIMEImage
from enviroment import From,server_name,to,Port,Password,Username
from datetime import datetime, timedelta
import glob
import pandas as pd
from DataQuery import out_dir,yesterday_file_name
output = os.path.join(out_dir,yesterday_file_name)
#print(os.getcwd())

msg = MIMEMultipart()
msg['From'] = From
msg["To"] = ','.join(to)
#msg["Cc"] = to
msg['Subject'] = f"TEST - GP Merchant Master"

htmlEmail = f"""
<p> Dear Sir/Madam, <br/><br/>
    Please refer to the attached GP Merchant Master details for your review and records.
</p>
"""

#msg.attach(MIMEText(htmlEmail, 'html'))
out_zip =[file for file in os.listdir(output) if file.endswith(".zip")]
print(out_zip)
f =  os.path.join(output,out_zip[0])

#f = os.path.join(os.getcwd(),folder_name,f"{folder_name} {file_name}")
#html tables data
    
htmlEmail2 = """
<p> Please contact Mosambee Support Team (<EMAIL>) directly if you have any questions. <br/>
    Thank you! <br/>
    Best Regards, <br/>
    Mosambee Support Team </p>
<br/>
<font color="red">Please do not reply to this email as it is auto-generated. </font>
"""
htmlEmail = "<br/>".join([htmlEmail,htmlEmail2])
msg.attach(MIMEText(htmlEmail, 'html'))

with open(f, "rb") as attached_file:
    part = MIMEApplication(
            attached_file.read(),
            Name=os.path.basename(f)
            )
# After the file is closed
part['Content-Disposition'] = 'attachment; filename="%s"' % os.path.basename(f)
msg.attach(part)
server = smtplib.SMTP(server_name, Port)
server.starttls()
server.login(Username,Password)
text = msg.as_string()
server.sendmail(From, to, text)
server.quit()

print("Email are sent successfully!")

