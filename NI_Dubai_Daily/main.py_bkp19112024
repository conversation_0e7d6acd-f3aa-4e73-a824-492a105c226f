from sys import exit,argv
import os
import pandas as pd
import numpy as np
import string
import XLSXAutoFitColumns
from openpyxl.styles import Font, Fill,Border,Alignment
from TxnDataPrep import data_retrival,query

reportName = argv[1]
foldername = argv[2]
dir = argv[3]
os.chdir(os.path.join(dir,foldername))
print(f'current Directory {os.getcwd()}')
NI_Daily = pd.read_csv("NI_DUBAIL_Daily.csv")
me_list = list(NI_Daily["ME Name"].unique())
for me in me_list:
    filename = me.replace(" ","_")
    me = NI_Daily.loc[NI_Daily["ME Name"]==me]
    if me["Additional Information"].dtype != 'object':
        me["Additional Information"] = me["Additional Information"].astype('str')
    me["Additional Information"].replace(to_replace='nan',value='',inplace=True)
    for column in me.columns:
        if me[column].dtype == 'object':
            me[column].fillna('',inplace=True)
    me = me[['ID','Transaction Created Date', 'User', 'Username','Type', 'Mode', 'Amount', 'Auth Code', 'Card', 'Card Type'
 ,'Brand Type', 'RRN', 'Invoice#','Device Serial', 'Status','Settled On','MID','TID',
 'Batch','Reference#', 'Additional Information','Latitude', 'Longitude','Payer','Transaction Created Time',
  'Transaction Status' ,'ME Name', 'Acquirer','Response Code','Currency','Device No','Reference Txn Id',
  'tg']]
#    me.to_csv(f"./{filename}.csv")
    with pd.ExcelWriter(f"{filename}.xlsx") as writer:
        me.to_excel(writer,sheet_name=f"{filename}",index=False)

print("Complete")
