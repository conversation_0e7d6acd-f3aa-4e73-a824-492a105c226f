import os
import sys
import smtplib
from email.mime.multipart import MIME<PERSON><PERSON>ip<PERSON>
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from email.mime.image import MIMEImage
from enviroment import From,server_name,to,Port,Password,Username
from datetime import datetime, timedelta
import glob
import pandas as pd
from DataQuery import outputDir
print(os.getcwd())
files = ['AJHG_EXPRESS_LLC.xlsx' , 'EMIRATES_POST_GROUP.xlsx',
        'FRESH_TO_HOME_TRADING_SOLE_PROPRIETORSHIP_LLC.xlsx',
        'MAI_DUBAI_LLC.xlsx','FERGULF_TRADING_UAE_LLC.xlsx','ZONE_DELIVERY_SERVICES_LLC.xlsx']
htmlEmail2 = """
        <p> Please contact Mosambee Support Team (<EMAIL>) directly if you have any questions. <br/>
    Thank you! <br/>
    Best Regards, <br/>
    Mosambee Support Team </p>
        <br/>
        <font color="red">Please do not reply to this email as it is auto-generated. </font>
        """

for file in files:
    ME_Name = file.split(sep='.')[0].replace('_'," ")
    msg = MIMEMultipart()
    msg['From'] = From
    msg["To"] = ','.join(to[file])
    recipint = to[file]
    msg['Subject'] = f"{ME_Name}/Transaction Report dated {(datetime.now() - timedelta(1)).strftime('%d-%m-%Y')}"
    filename = os.path.join(outputDir,f"{file}")
    if os.path.exists(filename):
        htmlEmail = f"""
                    <p> Dear Team, <br/><br/>
                        PFA transaction data for {ME_Name}  dated  {(datetime.now() - timedelta(1)).strftime('%d-%m-%Y')}.<br/>
                    </p>
                    """
        #html tables data
        htmlEmail = "<br/>".join([htmlEmail,htmlEmail2])
        msg.attach(MIMEText(htmlEmail, 'html'))
        with open(filename, "rb") as attached_file:
            part = MIMEApplication(
                    attached_file.read(),
                    Name=os.path.basename(filename)
                    )
        # After the file is closed
        part['Content-Disposition'] = 'attachment; filename="%s"' % os.path.basename(filename)
        msg.attach(part)
    else:
        htmlEmail = f"""
                    <p> Dear Team, <br/><br/>
                        There is no Transaction data for {ME_Name}  dated  {(datetime.now() - timedelta(1)).strftime('%d-%m-%Y')}.<br/>
                    </p>
                    """
        htmlEmail = "<br/>".join([htmlEmail,htmlEmail2])
        msg.attach(MIMEText(htmlEmail, 'html'))

    try:
        server = smtplib.SMTP(server_name, Port)
        server.starttls()
        server.login(Username,Password)
        text = msg.as_string()
        server.sendmail(From, recipint, text)
        server.quit()

    except:
        print("An exception occurred")
    print("Email are sent successfully!")
print("All the emails are sent successfully!")
