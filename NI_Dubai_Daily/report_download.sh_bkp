#!/bin/bash

dir=$2
cd $dir
tabcmd login -s http://************:8080 -u tabadmin -p $1
foldername=$(date -d "yesterday" '+%Y%m%d')
echo $foldername
[ ! -d "$foldername" ] && mkdir -p "$foldername"
cd $foldername
tabcmd export "/NiDubaiDaily/Sheet1?:refresh=yes" --csv -f "NI_DUBAIL_Daily.csv"
#tabcmd export "/AirlinesDaily/Data?:refresh=yes" --csv -f "Data.xlsx"
tabcmd logout

cd ..

reportName="NI_Dubai_Daily"

python3 /home/<USER>/Reports/Scripts/NI_Dubai_Daily/main.py $reportName $foldername $dir
filename="Decline_Transaction_Summary_Report_Daily.xlsx"

python3 /home/<USER>/Reports/Scripts/NI_Dubai_Daily/mail.py $foldername $filename $dir

#cd $dir

#cd $foldername
#rm -rf *.csv
#rm -rf *.png
echo "Completed"
