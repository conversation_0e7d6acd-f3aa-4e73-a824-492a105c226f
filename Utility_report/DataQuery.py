import os 
import sys
import datetime as dt
from datetime import timedelta,datetime,date

out_dir="/home/<USER>/Reports/Utility_report"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%d%m%Y")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
Transacting_output_FileName = f'Utility_report_till_{(datetime.now() - timedelta(1)).strftime("%Y%m%d")}.xlsx'

Transacting_output = os.path.join(out_dir,yesterday_file_name,Transacting_output_FileName)

def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

def date_serial_number(serial_number: int):
    """
    Convert an Excel serial number to a Python datetime object
    :param serial_number: the date serial number
    :return: a datetime object
    """
    # Excel stores dates as "number of days since 1900"
    delta = dt.datetime(1899, 12, 30) + dt.timedelta(days=serial_number)
    return delta

#in_dir = sys.argv[1]
#out_dir = sys.argv[2]
config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}

sfn_transaction_config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}

notificationDB_config = {
    "host": "*************",
    "port": "3306",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "notification",
}

VPA_details_query = """
select
	pos_id as "POS ID",
	qr_acquirer as "acquirer",
	merchant_code as "MID",
	terminal_id as "TID",
	vpa as "VPAs",
	device_serial_no as "Device Serial Number",
	device_model as "Device Model",
	status as "Status",
	created ,
	updated
from
	mapping_mqtt_user_notification_identifier
where
	status = 1
	and vpa is not null
order by
	id desc;
 """
Transaction_details_query = """
select
	application_id as "Application ID",
	round(amount/100,2) as "Amount",
        transaction_id as "Payment Reference ID",
	transaction_time as "Payment Timestamp"
from
	collect_payment_transactions_log
	WHERE
  STR_TO_DATE(transaction_time, '%Y:%m:%d %H:%i:%s') >= DATE_FORMAT(CURDATE(), '%Y-%m-01')
   AND STR_TO_DATE(transaction_time, '%Y:%m:%d %H:%i:%s') < CURDATE() and
	status = 1
order by
	payment_id desc;
 """
Merhcant_details_query = """
Select
	 m.id as pos_id,
      mmut.terminalID,
	m.partenerID as ApplicationID
from
	mapping_merchant_user_terminal mmut
left join merchant m on
	m.id = mmut.divisionId
where
	mmut.programId = 149
order by pos_id
	;
"""
