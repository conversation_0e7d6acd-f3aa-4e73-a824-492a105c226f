from datetime import datetime,timed<PERSON>ta
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import sfn_transaction_config,notificationDB_config,format_int_with_commas,date_serial_number,VPA_details_query,Transaction_details_query,Merhcant_details_query,Transacting_output
import XLSXAutoFitColumns

pd.options.display.float_format = '{:,.2f}'.format

merchant_details = data_retrival(sfn_transaction_config,Merhcant_details_query)
transaction_details = data_retrival(notificationDB_config,Transaction_details_query)
vpa_details = data_retrival(notificationDB_config,VPA_details_query)
vpa_details = vpa_details.astype(
    {
        "POS ID":"Int64"
    }
)
df = pd.merge(left=vpa_details,right=merchant_details,left_on=["POS ID","TID"],right_on=["pos_id",'terminalID'],how='left')
df = df.drop(['pos_id','terminalID'],axis=1)
with pd.ExcelWriter(Transacting_output) as writer:
    df.to_excel(writer,sheet_name="VPA Details",index= False)
    transaction_details.to_excel(writer,sheet_name="collect payment",index=False)
fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(Transacting_output)
fix_worksheet.process_all_worksheets()

#fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(Non_Transacting_output)
#fix_worksheet.process_all_worksheets()
print("File Creation is Successfull")
exit()
