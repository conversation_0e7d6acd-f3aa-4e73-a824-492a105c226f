from datetime import datetime,timed<PERSON>ta
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import config,format_int_with_commas,tag_gp_merchants,date_serial_number,TransactingQuery,Transacting_output,zip_password,zip_xlsx_file
import XLSXAutoFitColumns

pd.options.display.float_format = '{:,.2f}'.format

data = data_retrival(TransactingQuery)
data.drop_duplicates(ignore_index=True,inplace=True)
data.to_csv(Transacting_output,index=False)
zip_xlsx_file(Transacting_output, zip_password)

print("File Creation is Successfull")
exit()
