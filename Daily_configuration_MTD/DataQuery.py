import os 
import sys
import datetime as dt
from datetime import timedelta,datetime,date
import subprocess
import shutil

out_dir="/home/<USER>/Reports/Daily_configuration_MTD"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%Y%m%d")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
Transacting_output_FileName = f'Daily_Configuration_MTD_{yesterday_file_name}.csv'

Transacting_output = os.path.join(out_dir,yesterday_file_name,Transacting_output_FileName)

def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

def date_serial_number(serial_number: int):
    """
    Convert an Excel serial number to a Python datetime object
    :param serial_number: the date serial number
    :return: a datetime object
    """
    # Excel stores dates as "number of days since 1900"
    delta = dt.datetime(1899, 12, 30) + dt.timedelta(days=serial_number)
    return delta

def tag_gp_merchants(df):
    """
    Tags POS IDs as 'GP Merchant' if they have at least one Acquirer 
    value of 'GPHYP' or 'GPIN'.
 
    Args:
        df (pandas.DataFrame): The input DataFrame with 'POSID' and 'Acquirer' columns.
 
    Returns:
        pandas.DataFrame: The DataFrame with a new 'Merchant Type' column.
    """
 
    # Create a boolean mask indicating POS IDs with 'GPHYP' or 'GPIN' Acquirers
    gp_merchant_mask = df.groupby('POSID')['Acquirer'].transform(lambda x: x.isin(['GPHYP', 'GPIN','GP']).any())
 
    # Create the 'Merchant Type' column based on the mask
    df['Merchant Type'] = gp_merchant_mask.map({True: 'GP Merchant', False: 'Non GP Merchant'})
 
    return df

zip_password = "Mosambee@1234"
def zip_xlsx_file(file_path, password):
    """Creates a password-protected zip file of an .xlsx file inside a folder."""

    # Check if file exists and has .xlsx extension
    if not os.path.isfile(file_path) or not file_path.endswith(".csv"):
        print(f"Error: File '{file_path}' not found or is not an .csv file!")
        return

    folder_name = f"{os.path.splitext(file_path)[0]}"
    zip_file = f"{folder_name}.zip"

    # Create folder
    os.makedirs(folder_name, exist_ok=True)

    # Move file into folder
    new_file_path = os.path.join(folder_name, os.path.basename(file_path))
    os.rename(file_path, new_file_path)
    #shutil.move(file_path,new_file_path)
    # Run the zip command
    
    zip_command = ["zip", "-ej", "-P", password, zip_file, new_file_path]
    print(zip_file, folder_name , password)
    try:
        subprocess.run(zip_command, check=True)
        print(f"Folder '{folder_name}' containing '{file_path}' has been securely zipped as '{zip_file}'.")

        # Optionally remove the folder after zipping
    #    os.system(f"rm -rf {folder_name}")

    except subprocess.CalledProcessError as e:
        print(f"Error: Failed to create zip file. {e}")
#in_dir = sys.argv[1]
#out_dir = sys.argv[2]
config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}

TransactingQuery = """
SELECT 
    mat.acquirerName AS 'Acquirer',
    mmut.recordCreated AS 'Configuration Date',
    m.id AS 'POS ID',
    mdat.merchantCode AS 'MID',
    m.dbaName AS "DBA NAME",
    m.businessName AS "Legal NAME",
    mmut.terminalID AS 'TID',
    mmut.userName AS 'Username',
    a.line1 AS 'Add',
    s.name AS 'State',
    m.phone AS 'Registered Mobile number',
    m.businessEmail AS 'Registered Email ID',
    c.name AS 'City',
    a.pin AS 'Pin Code',
    m.isIntegrated AS 'App Type',
    mmut.status AS 'Active Deactive',
    CASE 
        WHEN mmut.status = 'A' THEN NULL 
        ELSE mmut.recordUpdated 
    END AS 'Deactivation Date',
    tp.program_name AS 'Program Name'
FROM
    mapping_merchant_user_terminal mmut
LEFT JOIN merchant m ON mmut.divisionId = m.id
LEFT JOIN mapping_division_acquirer_tg mdat ON mdat.id = mmut.divAcqTgId
LEFT JOIN mapping_acquirer_tg mat ON mat.id = mdat.acqTgId
LEFT JOIN tbl_program tp ON tp.program_id = mmut.programId
LEFT JOIN address a ON a.id = m.addressID
LEFT JOIN state s ON s.id = a.stateID
LEFT JOIN city c ON c.id = a.cityID AND s.id = c.stateID
LEFT JOIN (
    SELECT DISTINCT terminalID 
    FROM mapping_merchant_user_terminal 
    WHERE recordCreated < DATE_SUB(CURDATE(), INTERVAL 1 DAY)
) prev_terminals ON mmut.terminalID = prev_terminals.terminalID
WHERE 
    mmut.recordCreated BETWEEN DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND DATE_SUB(CURDATE(), INTERVAL 1 SECOND)
    AND mat.id IN (1,
2,
3,
5,
6,
7,
9,
10,
12,
13,
14,
15,
16,
17,
18,
19,
20,
24,
28,
29,
30,
31,
32,
35,
36,
37,
38,
39,
40,
46,
48,
49,
51,
52,
53,
54,
58,
59,
67,
68,
70,
71,
72,
73,
74,
81,
82,
84,
94,
95,
97,
98,
103,
109,
116,
117,
118,
121,
123,
125,
130,
131,
132,
133,
136,
137,
138,
143,
145,
146,
149,
150,
153,
159,
160)
    AND prev_terminals.terminalID IS NULL;
"""
