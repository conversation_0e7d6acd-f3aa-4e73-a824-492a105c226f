#!/bin/bash

# Script Name: HDFC File Split
# Description: Split HDFC file and upload to the NAS ************* Server
# Author: <PERSON><PERSON>
# Created: 2023-07-03
# Last Modified: 2023-07-03 by <PERSON><PERSON>


base_directory="/home/<USER>/Reports/Monthly_Transactions"
previous_date=$(date -d "yesterday" +"%m%Y")
current_date=$(date +"%Y_%m_%d")
#source_directory="$base_directory/$previous_date"
remote_path="/share/Reports"
#cd "$source_directory"

###Uploading the file to the NAS server *************####
sshpass -p '0S6#985Tc2o@8283' ssh MOS_SFTP@************** "mkdir -p $remote_path/$previous_date"
sshpass -p '0S6#985Tc2o@8283' scp $base_directory/$previous_date/*  MOS_SFTP@**************:$remote_path/$previous_date
sshpass -p '0S6#985Tc2o@8283' ssh MOS_SFTP@************** "chmod 777 $remote_path/$previous_date/*"
