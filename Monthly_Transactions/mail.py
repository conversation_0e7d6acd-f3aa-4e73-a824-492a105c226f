import os
import sys
import smtplib
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from email.mime.image import MIMEImage
from enviroment import From,server_name,to,Port,Password,Username
from datetime import datetime, timedelta
import glob
import pandas as pd
from DataQuery import out_dir,yesterday_file_name
#print(os.getcwd())

msg = MIMEMultipart()
msg['From'] = From
msg["To"] = ','.join(to)
#msg["Cc"] = to
msg['Subject'] = f"Test - Monthly Transactions for  {(datetime.now() - timedelta(10)).strftime('%m-%Y')}"
folder_name = sys.argv[1]
file_name = sys.argv[2]

htmlEmail = f"""
<p> Dear Sir/Madam, <br/>
    Kindly find below Status of Monthly Transactions report for the month of {(datetime.now() - timedelta(10)).strftime('%m-%Y')}.<br/>
</p>
"""
for file in os.listdir(os.path.join(out_dir,yesterday_file_name)):
    htmlEmail_file = f"""{file} created."""
    htmlEmail =  "<br/>".join([htmlEmail,htmlEmail_file])

htmlEmail2 = """
<p> Please contact Mosambee Support Team (<EMAIL>) directly if you have any questions. <br/>
    Thank you! <br/>
    Best Regards, <br/>
    Mosambee Support Team </p>
<br/>
<font color="red">Please do not reply to this email as it is auto-generated. </font>
"""
htmlEmail = "<br/>".join([htmlEmail,htmlEmail2])
msg.attach(MIMEText(htmlEmail, 'html'))

# After the file is closed
server = smtplib.SMTP(server_name, Port)
server.starttls()
server.login(Username,Password)
text = msg.as_string()
server.sendmail(From, to, text)
server.quit()

print("Email are sent successfully!")

