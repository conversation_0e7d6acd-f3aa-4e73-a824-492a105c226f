from datetime import datetime,timedelta
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import config,query,out_dir,yesterday_file_name
import XLSXAutoFitColumns

pd.options.display.float_format = '{:,.2f}'.format


# Data extraction from SQL

for key in query:
    print(f"{key} Started")
    data = data_retrival(query[key])
    output_file = os.path.join(out_dir,yesterday_file_name,f"{key}_{(datetime.now() - timedelta(10)).strftime('%b_%Y')}.csv")
    data.to_csv(output_file,index=False)
    print(f"{key} Completed")
    del data
print("File Creation is Successfull")
exit()
