import os 
import sys
import pandas as pd
import datetime as dt
from datetime import date
from datetime import timedelta

out_dir="/home/<USER>/Reports/Monthly_Transactions/"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%m%Y")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

def date_serial_number(serial_number: int):
    """
    Convert an Excel serial number to a Python datetime object
    :param serial_number: the date serial number
    :return: a datetime object
    """
    # Excel stores dates as "number of days since 1900"
    delta = dt.datetime(1899, 12, 30) + dt.timedelta(days=serial_number)
    return delta

def format_int_with_percent(x):
    """
    Formats an integer with percent.
    """
    val = str(round(float(x)*100,2))+'%'
    return val

#in_dir = sys.argv[1]
#out_dir = sys.argv[2]
config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}

query = {
    "Vyapaar_Ingenico_BQR_Trnx_Details" : """SELECT 
                                                a.terminalID,
                                                COUNT(a.terminalID) AS Count,
                                                SUM(ROUND(amount / 100, 2)) AS Amount
                                            FROM
                                                transactions a FORCE INDEX (TRANSACTIONS_TRANTIME)
                                            WHERE
                                                a.transactionTime >= DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m-01')
                                                    AND a.transactionTime < DATE_FORMAT(NOW(), '%Y-%m-01')
                                                    AND a.terminalID LIKE '67%'
                                                    AND a.typeID IN (22 , 24)
                                                    AND a.cardTypeName = 'BQR'
                                                    AND a.statusID IN (2 , 7)
                                                    AND a.acquirerName = 'HDFC'
                                                    AND a.tgName = 'MINTOAKQR'
                                            GROUP BY a.terminalID;""" ,
    "Vyapaar_Ingenico_Paybylink_Trnx_Details" : """SELECT 
                                                        a.terminalID,
                                                        COUNT(a.terminalID) AS Count,
                                                        SUM(ROUND(amount / 100, 2)) AS Amount
                                                    FROM
                                                        transactions a FORCE INDEX (TRANSTYPE)
                                                    WHERE
                                                        a.transactionTime >= DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m-01')
                                                            AND a.transactionTime < DATE_FORMAT(NOW(), '%Y-%m-01')
                                                            AND terminalID LIKE '67%'
                                                            AND typeID = 19
                                                            AND statusID IN (2 , 7)
                                                            AND acquirerName = 'HDFC'
                                                            AND tgName = 'MINTOAKPG'
                                                    GROUP BY a.terminalID
                                                    ;""" ,
    "Vyapaar_Ingenico_Card_Trnx_Details" : """SELECT 
                                                a.terminalID,
                                                COUNT(a.terminalID) AS Count,
                                                SUM(ROUND(amount / 100, 2)) AS Amount
                                            FROM
                                                transactions a FORCE INDEX (TRANSACTIONS_TRANTIME)
                                            WHERE
                                                a.transactionTime >= DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m-01')
                                                    AND a.transactionTime < DATE_FORMAT(NOW(), '%Y-%m-01')
                                                    AND terminalID LIKE '67%'
                                                    AND typeID = 1
                                                    AND statusID IN (2 , 7)
                                                    AND acquirerName = 'HDFC'
                                                    AND tgName = 'HDFC'
                                                    AND NOT (drCrCode = 'DD' AND cardTypeId = 8)
                                            GROUP BY a.terminalID;""" ,
    "HDFC_BQR_Trnx_Details" : """SELECT 
                                    a.terminalID,
                                    COUNT(a.terminalID) AS Count,
                                    SUM(ROUND(amount / 100, 2)) AS Amount
                                FROM
                                    transactions a FORCE INDEX (TRANSACTIONS_TRANTIME)
                                WHERE
                                    a.transactionTime >= DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m-01')
                                        AND a.transactionTime < DATE_FORMAT(NOW(), '%Y-%m-01')
                                        AND typeID = 24
                                        AND cardTypeName = 'BQR'
                                        AND statusID IN (2 , 7)
                                        AND acquirerName = 'HDFC'
                                        AND tgName = 'HDFCQR'
                                GROUP BY a.terminalID
                                ;""" ,
    "Vyapaar_Mintoak_BQR_Trnx_Details" : """SELECT 
    a.terminalID,
    COUNT(a.terminalID) AS Count,
    SUM(ROUND(amount / 100, 2)) AS Amount
FROM
    transactions a FORCE INDEX (TRANSACTIONS_TRANTIME)
WHERE
    a.transactionTime >= DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m-01')
        AND a.transactionTime < DATE_FORMAT(NOW(), '%Y-%m-01')
        AND a.terminalID LIKE '64%'
        AND a.typeID IN (22 , 24)
        AND a.cardTypeName = 'BQR'
        AND a.statusID IN (2 , 7)
        AND a.acquirerName = 'HDFC'
        AND a.tgName = 'MINTOAKQR'
GROUP BY a.terminalID
;""" ,
    "HDFC_Paybylink_Trnx_Details" : """SELECT 
    a.terminalID,
    COUNT(a.terminalID) AS Count,
    SUM(ROUND(amount / 100, 2)) AS Amount
FROM
    transactions a FORCE INDEX (TRANSTYPE)
WHERE
    a.transactionTime >= DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m-01')
        AND a.transactionTime < DATE_FORMAT(NOW(), '%Y-%m-01')
        AND typeID = 19
        AND statusID IN (2 , 7)
        AND acquirerName = 'HDFC'
        AND tgName = 'FSSPG'
GROUP BY a.terminalID
;""" ,
    "Vyapaar_Mintoak_Paybylinnk_Trnx" : """SELECT 
    a.terminalID,
    COUNT(a.terminalID) AS Count,
    SUM(ROUND(amount / 100, 2)) AS Amount
FROM
    transactions a FORCE INDEX (TRANSTYPE)
WHERE
    a.transactionTime >= DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m-01')
        AND a.transactionTime < DATE_FORMAT(NOW(), '%Y-%m-01')
        AND terminalID LIKE '64%'
        AND typeID = 19
        AND statusID IN (2 , 7)
        AND acquirerName = 'HDFC'
        AND tgName = 'MINTOAKPG'
GROUP BY a.terminalID
;""" ,
    "HDFC_Card_Trnx_Details" : """SELECT 
    a.terminalID,
    COUNT(a.terminalID) AS Count,
    SUM(ROUND(amount / 100, 2)) AS Amount
FROM
    transactions a FORCE INDEX (TRANSACTIONS_TRANTIME)
WHERE
    a.transactionTime >= DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m-01')
        AND a.transactionTime < DATE_FORMAT(NOW(), '%Y-%m-01')
        AND typeID = 1
        AND statusID IN (2 , 7)
        AND acquirerName = 'HDFC'
        AND tgName = 'HDFC'
        AND NOT (terminalID LIKE '64%'
        OR terminalID LIKE '67%')
        AND NOT (drCrCode = 'DD' AND cardTypeId = 8)
GROUP BY a.terminalID
;""" ,
    "Vyapaar_Mintoak_Card_Trnx_Details" : """SELECT 
    a.terminalID,
    COUNT(a.terminalID) AS Count,
    SUM(ROUND(amount / 100, 2)) AS Amount
FROM
    transactions a FORCE INDEX (TRANSACTIONS_TRANTIME)
WHERE
    a.transactionTime >= DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m-01')
        AND a.transactionTime < DATE_FORMAT(NOW(), '%Y-%m-01')
        AND terminalID LIKE '64%'
        AND typeID = 1
        AND statusID IN (2 , 7)
        AND acquirerName = 'HDFC'
        AND tgName = 'HDFC'
        AND NOT (drCrCode = 'DD' AND cardTypeId = 8)
GROUP BY a.terminalID
;""" ,
    "HDFC_UPI_Trnx_Details" : """SELECT 
    a.terminalID,
    COUNT(a.terminalID) AS Count,
    SUM(ROUND(amount / 100, 2)) AS Amount
FROM
    transactions a FORCE INDEX (TRANSACTIONS_TRANTIME)
WHERE
    a.transactionTime >= DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m-01')
        AND a.transactionTime < DATE_FORMAT(NOW(), '%Y-%m-01')
        AND a.typeID IN (24 , 22)
        AND a.cardTypeName = 'UPI'
        AND a.statusID IN (2 , 7)
        AND a.acqId = 3
        AND tgName IN ('HDFCUPI' , 'HDFCQR')
GROUP BY a.terminalID
;""" ,
    "Vyapaar_Mintoak_UPI_Trnx_Details" : """SELECT 
    a.terminalID,
    COUNT(a.terminalID) AS Count,
    SUM(ROUND(amount / 100, 2)) AS Amount
FROM
    transactions a FORCE INDEX (TRANSACTIONS_TRANTIME)
WHERE
    a.transactionTime >= DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m-01')
        AND a.transactionTime < DATE_FORMAT(NOW(), '%Y-%m-01')
        AND a.terminalID LIKE '64%'
        AND a.typeID IN (22 , 24)
        AND a.cardTypeName = 'UPI'
        AND a.statusID IN (2 , 7)
        AND a.acquirerName = 'HDFC'
GROUP BY a.terminalID
;""" ,
    "MATM_Trnx_Details" : """SELECT 
    id,
    DATE(transactionTime) AS 'Transaction Created Date',
    userName,
    transactionTypeName AS Type,
    modeName AS Mode,
    ROUND(amount / 100, 2) AS Amount,
    transactionTypeName AS 'Txn Type',
    authCode AS 'Auth Code',
    maskedCardNumber AS Card,
    drCrCode AS 'Card Type',
    cardTypeName AS 'Brand Type',
    modeName AS 'Card Txn Type',
    rrn,
    serialNumber AS 'Device Serial',
    settlementStatusName AS Status,
    settlementTxnTime AS 'Settled On',
    merchantCode AS MID,
    terminalID AS TID,
    batchNumber AS 'Batch #',
    TIME(transactionTime) AS 'Transaction Created Time',
    txnStatusName AS 'Transaction Status',
    merchantName AS 'ME Name',
    acquirerName AS Acquirer,
    responseCode AS 'Response Code',
    currencyCode AS Currency,
    bankRefId AS 'Bank Ref No',
    deviceID AS 'Device No',
    referenceTxnID AS 'Reference Txn Id',
    tgTransactionId AS 'TG Txn Id',
    tgName AS Tg,
    dbaName AS 'DBA Name',
    terminalType AS 'Device Type'
FROM
    transactions a FORCE INDEX (TRANSTYPE)
WHERE
    a.transactionTime >= DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m-01')
        AND a.transactionTime < DATE_FORMAT(NOW(), '%Y-%m-01')
        AND typeID = 36
        AND statusID IN (2 , 7)
;""" ,
    "KOTAK_BQR" : """SELECT 
    a.id,
    DATE(transactionTime) AS 'Transaction Created Date',
    userName,
    transactionTypeName AS Type,
    modeName AS Mode,
    ROUND(amount / 100, 2) AS Amount,
    transactionTypeName AS 'Txn Type',
    authCode AS 'Auth Code',
    maskedCardNumber AS Card,
    drCrCode AS 'Card Type',
    cardTypeName AS 'Brand Type',
    modeName AS 'Card Txn Type',
    rrn,
    serialNumber AS 'Device Serial',
    settlementStatusName AS Status,
    settlementTxnTime AS 'Settled On',
    merchantCode AS MID,
    a.terminalID AS TID,
    batchNumber AS 'Batch #',
    TIME(transactionTime) AS 'Transaction Created Time',
    txnStatusName AS 'Transaction Status',
    merchantName AS 'ME Name',
    acquirerName AS Acquirer,
    responseCode AS 'Response Code',
    currencyCode AS Currency,
    bankRefId AS 'Bank Ref No',
    deviceID AS 'Device No',
    referenceTxnID AS 'Reference Txn Id',
    tgTransactionId AS 'TG Txn Id',
    tgName AS Tg,
    dbaName AS 'DBA Name',
    terminalType AS 'Device Type',
    acqId,
    acquirerName
FROM
    transactions a FORCE INDEX (ACQID_INDEX)
WHERE
    a.transactionTime >= DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m-01')
        AND a.transactionTime < DATE_FORMAT(NOW(), '%Y-%m-01')
        AND a.statusID IN (2 , 7)
        AND a.typeID IN (24 , 22)
        AND a.cardTypeName = 'BQR'
        AND acqId IN (21 , 40, 57)
;""" ,
    "KOTAK_UPI" : """SELECT 
    a.id,
    DATE(transactionTime) AS 'Transaction Created Date',
    userName,
    transactionTypeName AS Type,
    modeName AS Mode,
    ROUND(amount / 100, 2) AS Amount,
    transactionTypeName AS 'Txn Type',
    authCode AS 'Auth Code',
    maskedCardNumber AS Card,
    drCrCode AS 'Card Type',
    cardTypeName AS 'Brand Type',
    modeName AS 'Card Txn Type',
    rrn,
    serialNumber AS 'Device Serial',
    settlementStatusName AS Status,
    settlementTxnTime AS 'Settled On',
    merchantCode AS MID,
    a.terminalID AS TID,
    batchNumber AS 'Batch #',
    TIME(transactionTime) AS 'Transaction Created Time',
    txnStatusName AS 'Transaction Status',
    merchantName AS 'ME Name',
    acquirerName AS Acquirer,
    responseCode AS 'Response Code',
    currencyCode AS Currency,
    bankRefId AS 'Bank Ref No',
    deviceID AS 'Device No',
    referenceTxnID AS 'Reference Txn Id',
    tgTransactionId AS 'TG Txn Id',
    tgName AS Tg,
    dbaName AS 'DBA Name',
    terminalType AS 'Device Type',
    acqId,
    acquirerName
FROM
    transactions a FORCE INDEX (ACQID_INDEX)
WHERE
    a.transactionTime >= DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m-01')
        AND a.transactionTime < DATE_FORMAT(NOW(), '%Y-%m-01')
        AND a.statusID IN (2 , 7)
        AND a.typeID = 22
        AND acqId IN (21 , 40, 57)
;""" ,
    "SBI_Card" : """SELECT 
    id,
    DATE(transactionTime) AS 'Transaction Created Date',
    userName,
    transactionTypeName AS Type,
    modeName AS Mode,
    ROUND(amount / 100, 2) AS Amount,
    transactionTypeName AS 'Txn Type',
    authCode AS 'Auth Code',
    maskedCardNumber AS Card,
    drCrCode AS 'Card Type',
    cardTypeName AS 'Brand Type',
    modeName AS 'Card Txn Type',
    rrn,
    serialNumber AS 'Device Serial',
    settlementStatusName AS Status,
    settlementTxnTime AS 'Settled On',
    merchantCode AS MID,
    terminalID AS TID,
    batchNumber AS 'Batch #',
    TIME(transactionTime) AS 'Transaction Created Time',
    txnStatusName AS 'Transaction Status',
    merchantName AS 'ME Name',
    acquirerName AS Acquirer,
    responseCode AS 'Response Code',
    currencyCode AS Currency,
    bankRefId AS 'Bank Ref No',
    deviceID AS 'Device No',
    referenceTxnID AS 'Reference Txn Id',
    tgTransactionId AS 'TG Txn Id',
    tgName AS Tg,
    dbaName AS 'DBA Name',
    terminalType AS 'Device Type'
FROM
    transactions a
WHERE
    acqId = 26 AND statusId IN (2 , 7)
        AND typeID = 1
        AND a.transactionTime >= DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m-01')
        AND a.transactionTime < DATE_FORMAT(NOW(), '%Y-%m-01')
;""" ,
    "SBI_BQR" : """SELECT 
    a.id,
    DATE(transactionTime) AS 'Transaction Created Date',
    userName,
    transactionTypeName AS Type,
    modeName AS Mode,
    ROUND(amount / 100, 2) AS Amount,
    transactionTypeName AS 'Txn Type',
    authCode AS 'Auth Code',
    maskedCardNumber AS Card,
    drCrCode AS 'Card Type',
    cardTypeName AS 'Brand Type',
    modeName AS 'Card Txn Type',
    rrn,
    serialNumber AS 'Device Serial',
    settlementStatusName AS Status,
    settlementTxnTime AS 'Settled On',
    merchantCode AS MID,
    a.terminalID AS TID,
    batchNumber AS 'Batch #',
    TIME(transactionTime) AS 'Transaction Created Time',
    txnStatusName AS 'Transaction Status',
    merchantName AS 'ME Name',
    acquirerName AS Acquirer,
    responseCode AS 'Response Code',
    currencyCode AS Currency,
    bankRefId AS 'Bank Ref No',
    deviceID AS 'Device No',
    referenceTxnID AS 'Reference Txn Id',
    tgTransactionId AS 'TG Txn Id',
    tgName AS Tg,
    dbaName AS 'DBA Name',
    terminalType AS 'Device Type',
    acqId,
    acquirerName
FROM
    transactions a FORCE INDEX (ACQID_INDEX)
WHERE
    a.transactionTime >= DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m-01')
        AND a.transactionTime < DATE_FORMAT(NOW(), '%Y-%m-01')
        AND a.statusID IN (2 , 7)
        AND a.typeID IN (24 , 22)
        AND a.cardTypeName = 'BQR'
        AND acqId = 26
;""" ,
    "SBI_UPI" : """SELECT 
    a.id,
    DATE(transactionTime) AS 'Transaction Created Date',
    userName,
    transactionTypeName AS Type,
    modeName AS Mode,
    ROUND(amount / 100, 2) AS Amount,
    transactionTypeName AS 'Txn Type',
    authCode AS 'Auth Code',
    maskedCardNumber AS Card,
    drCrCode AS 'Card Type',
    cardTypeName AS 'Brand Type',
    modeName AS 'Card Txn Type',
    rrn,
    serialNumber AS 'Device Serial',
    settlementStatusName AS Status,
    settlementTxnTime AS 'Settled On',
    merchantCode AS MID,
    a.terminalID AS TID,
    batchNumber AS 'Batch #',
    TIME(transactionTime) AS 'Transaction Created Time',
    txnStatusName AS 'Transaction Status',
    merchantName AS 'ME Name',
    acquirerName AS Acquirer,
    responseCode AS 'Response Code',
    currencyCode AS Currency,
    bankRefId AS 'Bank Ref No',
    deviceID AS 'Device No',
    referenceTxnID AS 'Reference Txn Id',
    tgTransactionId AS 'TG Txn Id',
    tgName AS Tg,
    dbaName AS 'DBA Name',
    terminalType AS 'Device Type',
    acqId,
    acquirerName
FROM
    transactions a FORCE INDEX (ACQID_INDEX)
WHERE
    a.transactionTime >= DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m-01')
        AND a.transactionTime < DATE_FORMAT(NOW(), '%Y-%m-01')
        AND a.statusID IN (2 , 7)
        AND a.typeID = 22
        AND acqId = 26
;""" ,
    "AMEX_Tnx_Details" : """SELECT 
    a.id,
    DATE(transactionTime) AS 'Transaction Created Date',
    a.userName,
    transactionTypeName AS Type,
    modeName AS Mode,
    ROUND(amount / 100, 2) AS Amount,
    transactionTypeName AS 'Txn Type',
    authCode AS 'Auth Code',
    maskedCardNumber AS Card,
    drCrCode AS 'Card Type',
    a.cardTypeName AS 'Brand Type',
    modeName AS 'Card Txn Type',
    rrn,
    serialNumber AS 'Device Serial',
    settlementStatusName AS Status,
    settlementTxnTime AS 'Settled On',
    a.merchantCode AS MID,
    a.terminalID AS TID,
    batchNumber AS 'Batch #',
    TIME(transactionTime) AS 'Transaction Created Time',
    txnStatusName AS 'Transaction Status',
    merchantName AS 'ME Name',
    a.acquirerName AS Acquirer,
    responseCode AS 'Response Code',
    currencyCode AS Currency,
    bankRefId AS 'Bank Ref No',
    t.deviceID AS 'Device No',
    referenceTxnID AS 'Reference Txn Id',
    tgTransactionId AS 'TG Txn Id',
    a.tgName AS Tg,
    dbaName AS 'DBA Name',
    terminalType AS 'Device Type'
FROM
    transactions a,
    mapping_merchant_user_terminal mmut,
    mapping_division_acquirer_tg mdat,
    mapping_acquirer_tg mat
WHERE
    a.terminalID = mmut.terminalID
        AND mmut.divAcqTgId = mdat.id
        AND mdat.acqTgId = mat.Id
        AND mat.tgID = 50
        AND a.transactionTime >= DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m-01')
        AND a.transactionTime < DATE_FORMAT(NOW(), '%Y-%m-01')
        AND a.statusID IN (2 , 7)
;""" ,
    "GP_Paybylink_Tnx_Details" : """SELECT 
    id,
    DATE(transactionTime) AS 'Transaction Created Date',
    userName,
    transactionTypeName AS Type,
    modeName AS Mode,
    ROUND(amount / 100, 2) AS Amount,
    transactionTypeName AS 'Txn Type',
    authCode AS 'Auth Code',
    maskedCardNumber AS Card,
    drCrCode AS 'Card Type',
    cardTypeName AS 'Brand Type',
    modeName AS 'Card Txn Type',
    rrn,
    serialNumber AS 'Device Serial',
    settlementStatusName AS Status,
    settlementTxnTime AS 'Settled On',
    merchantCode AS MID,
    terminalID AS TID,
    batchNumber AS 'Batch #',
    TIME(transactionTime) AS 'Transaction Created Time',
    txnStatusName AS 'Transaction Status',
    merchantName AS 'ME Name',
    acquirerName AS Acquirer,
    responseCode AS 'Response Code',
    currencyCode AS Currency,
    bankRefId AS 'Bank Ref No',
    deviceID AS 'Device No',
    referenceTxnID AS 'Reference Txn Id',
    tgTransactionId AS 'TG Txn Id',
    tgName AS Tg,
    dbaName AS 'DBA Name',
    terminalType AS 'Device Type'
FROM
    transactions a FORCE INDEX (TRANSACTIONS_TRANTIME)
WHERE
    a.transactionTime >= DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m-01')
        AND a.transactionTime < DATE_FORMAT(NOW(), '%Y-%m-01')
        AND typeID = 19
        AND statusID IN (2 , 7)
        AND acqId IN (16 , 20, 24, 55)
;""",
    "HDFC_Issuer_EMI_Tnx_Details" : """SELECT 
    id,
    DATE(transactionTime) AS 'Transaction Created Date',
    userName,
    transactionTypeName AS Type,
    modeName AS Mode,
    ROUND(amount / 100, 2) AS Amount,
    transactionTypeName AS 'Txn Type',
    authCode AS 'Auth Code',
    maskedCardNumber AS Card,
    drCrCode AS 'Card Type',
    cardTypeName AS 'Brand Type',
    modeName AS 'Card Txn Type',
    rrn,
    serialNumber AS 'Device Serial',
    settlementStatusName AS Status,
    settlementTxnTime AS 'Settled On',
    merchantCode AS MID,
    terminalID AS TID,
    batchNumber AS 'Batch #',
    TIME(transactionTime) AS 'Transaction Created Time',
    txnStatusName AS 'Transaction Status',
    merchantName AS 'ME Name',
    acquirerName AS Acquirer,
    responseCode AS 'Response Code',
    currencyCode AS Currency,
    bankRefId AS 'Bank Ref No',
    deviceID AS 'Device No',
    referenceTxnID AS 'Reference Txn Id',
    tgTransactionId AS 'TG Txn Id',
    tgName AS Tg,
    dbaName AS 'DBA Name',
    terminalType AS 'Device Type',
    acqId,
    acquirerName
FROM
    transactions a
WHERE
    typeId = 15
        AND a.transactionTime >= DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m-01')
        AND a.transactionTime < DATE_FORMAT(NOW(), '%Y-%m-01')
        AND acqId = 3
        AND statusId = 2;"""
}
