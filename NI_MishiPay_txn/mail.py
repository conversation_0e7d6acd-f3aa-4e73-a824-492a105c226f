import os
import sys
import smtplib
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ip<PERSON>
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from email.mime.image import MIMEImage
from enviroment import From,server_name,to,Port,Password,Username
from datetime import datetime, timedelta
import glob
import pandas as pd
from DataQuery import outputDir,Transacting_output,start_time, end_time
print(os.getcwd())
htmlEmail2 = """
        <p> Please contact Mosambee Support Team (<EMAIL>) directly if you have any questions. <br/>
    Thank you! <br/>
    Best Regards, <br/>
    Mosambee Support Team </p>
        <br/>
        <font color="red">Please do not reply to this email as it is auto-generated. </font>
        """
ME_Name = "DUBAI AIRPORT DUTY FREE SHOPPING COMPLEX"
msg = MIMEMultipart()
msg['From'] = From
msg["To"] = ','.join(to)
recipint = to
msg['Subject'] = f"{ME_Name}/Transaction Report dated {start_time.strftime('%Y-%m-%d %H:%M:%S')} to {end_time.strftime('%Y-%m-%d %H:%M:%S')}"
filename = Transacting_output
if os.path.exists(filename):
    htmlEmail = f"""
                    <p> Dear Team, <br/><br/>
                        PFA transaction data for {ME_Name}  dated  {start_time.strftime('%Y-%m-%d %H:%M:%S')} to {end_time.strftime('%Y-%m-%d %H:%M:%S')}.<br/>
                    </p>
                    """
        #html tables data
    htmlEmail = "<br/>".join([htmlEmail,htmlEmail2])
    msg.attach(MIMEText(htmlEmail, 'html'))
    with open(filename, "rb") as attached_file:
        part = MIMEApplication(
                attached_file.read(),
                Name=os.path.basename(filename)
                 )
        # After the file is closed
    part['Content-Disposition'] = 'attachment; filename="%s"' % os.path.basename(filename)
    msg.attach(part)
else:
    htmlEmail = f"""
                <p> Dear Team, <br/><br/>
                    There is no Transaction data for {ME_Name}  dated {start_time.strftime('%Y-%m-%d %H:%M:%S')} to {end_time.strftime('%Y-%m-%d %H:%M:%S')}.<br/>
                </p>
                """
    htmlEmail = "<br/>".join([htmlEmail,htmlEmail2])
    msg.attach(MIMEText(htmlEmail, 'html'))

try:
    server = smtplib.SMTP(server_name, Port)
    server.starttls()
    server.login(Username,Password)
    text = msg.as_string()
    server.sendmail(From, recipint, text)
    server.quit()

except:
    print("An exception occurred")
print("Email are sent successfully!")
