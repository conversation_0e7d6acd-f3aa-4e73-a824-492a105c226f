import os 
import sys
from datetime import date,datetime
from datetime import timedelta

out_dir="/home/<USER>/Reports/NI_MishiPay_txn/"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%d%m%Y")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)


def get_report_time_range_and_filename():
    now = datetime.now()

    if now.hour >= 12:
        # After 12 PM → today's 00:00:00 to 11:59:59
        start_time = datetime(now.year, now.month, now.day, 0, 0, 0)
        end_time = datetime(now.year, now.month, now.day, 11, 59, 59)
    else:
        # Before 12 PM → yesterday's 12:00:00 to 23:59:59
        yesterday = now - timedelta(days=1)
        start_time = datetime(yesterday.year, yesterday.month, yesterday.day, 12, 0, 0)
        end_time = datetime(yesterday.year, yesterday.month, yesterday.day, 23, 59, 59)

    return start_time, end_time

start_time, end_time = get_report_time_range_and_filename()

#os.chdir(in_dir)
Transacting_output_FileName = f'DDF_{start_time.strftime("%Y%m%d_%H%M%S")}_to_{end_time.strftime("%Y%m%d_%H%M%S")}.xlsx'

Transacting_output = os.path.join(out_dir,yesterday_file_name,Transacting_output_FileName)

#os.chdir(in_dir)
outputDir = os.path.join(out_dir,yesterday_file_name)
def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

config = {
    "host": "************",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234",
    "database": "sfn_transaction",
    }
query = '''
SELECT 
    t.id AS ID,
    DATE(t.transactionTime) AS 'Transaction Created Date',
    t.clientId AS User,
    t.userName AS Username,
    CASE
        WHEN t.typeID = 1 AND t.isTip IS NOT NULL THEN 'Tip Adjusted'
        ELSE t.transactionTypeName
    END AS Type,
    t.modeName AS Mode,
    CASE
        WHEN t.typeID = 15 AND t.emiType = 'debit' THEN t.additionalAmount / POWER(10, IFNULL(a.decimalAmountLength, 2))
        ELSE t.amount / POWER(10, IFNULL(a.decimalAmountLength, 2))
    END AS Amount,
    t.authCode AS 'Auth Code',
    t.maskedCardNumber AS Card,
    t.drCrCode AS 'Card Type',
    t.cardTypeName AS 'Brand Type',
    t.rrn AS RRN,
    t.invoiceNumber AS 'Invoice#',
    t.serialNumber AS 'Device Serial',
    COALESCE(t.settlementStatusName, 'UNSETTLED') AS Status,
    t.settlementTxnTime AS 'Settled On',
    CASE
        WHEN t.tempMerchantCode IS NOT NULL AND t.tempMerchantCode != 0 AND t.tempMerchantCode != t.merchantCode THEN t.tempMerchantCode
        ELSE COALESCE(t.merchantCode, mdat.merchantCode)
    END AS MID,
    CASE
        WHEN t.tempTID IS NOT NULL AND t.tempTID != 0 AND t.tempTID != t.terminalID THEN t.tempTID
        ELSE COALESCE(t.terminalID, mmut.terminalID)
    END AS TID,
    t.batchNumber AS Batch,
    t.billNumber AS 'Reference#',
    CASE
        WHEN t.statusID NOT IN (2,7,10) AND t.typeID NOT IN (3,10,11,16,7) THEN 
            COALESCE(tdc.description, tr.description)
        ELSE NULL
    END AS 'Additional Information',
    t.latitude AS Latitude,
    t.longitude AS Longitude,
    t.cardHolderName AS Payer,
    t.securityToken AS 'TID Location',
    CONCAT("'",TIME(t.transactionTime)) AS 'Transaction Created Time',
    t.txnStatusName AS 'Transaction Status',
    t.merchantName AS 'ME Name',
    t.acquirerName AS Acquirer,
    IFNULL(t.responseCode, '91') AS 'Response Code',
    t.currencyCode AS Currency,
    t.deviceID AS 'Device No',
    t.referenceTxnID AS 'Reference Txn Id',
    t.tgName AS tg,
    t.dbaName AS 'DBA Name',
    t.refundStatus AS 'Refund Status',
    DATE(t.txnResponseTime) AS 'Transaction Response Date',
    TIME(t.txnResponseTime) AS 'Transaction Response Time',
    m.enterpriseId,
    CASE
        WHEN t.settlementStatusID != 4 THEN 
            CASE
                WHEN t.forSettlement = 1 THEN 'Auto Settlement'
                WHEN t.forSettlement = 0 THEN 'Manual Settlement'
                WHEN t.forSettlement = 2 THEN 'Customized Settlement'
            END
        ELSE 'NA'
    END AS 'Settlement Flag'
FROM
    transactions t
    LEFT JOIN acquirer a ON t.acquirerName = a.name
    LEFT JOIN merchant m ON t.merchantID = m.id AND m.status = 'A'
    LEFT JOIN txn_decline_codes tdc ON t.tgId = tdc.tgID AND t.responseCode = tdc.responseCode
    LEFT JOIN txn_decline_codes tr ON t.responseCode = tr.responseCode AND tr.tgID IS NULL
    LEFT JOIN mapping_merchant_user_terminal mmut 
        ON t.divisionID = mmut.id AND mmut.status = 'A'
	Left Join mapping_division_acquirer_tg mdat on mdat.id = mmut.divAcqTgId
WHERE
    t.transactionTime >= (
        CASE 
            WHEN HOUR(NOW()) >= 12 THEN DATE(NOW())
            ELSE DATE(NOW() - INTERVAL 1 DAY) + INTERVAL 12 HOUR
        END
    )
    AND t.transactionTime <= (
        CASE 
            WHEN HOUR(NOW()) >= 12 THEN DATE(NOW()) + INTERVAL 11 HOUR + INTERVAL 59 MINUTE + INTERVAL 59 SECOND
            ELSE DATE(NOW() - INTERVAL 1 DAY) + INTERVAL 23 HOUR + INTERVAL 59 MINUTE + INTERVAL 59 SECOND
        END
    )
    AND t.typeID IN (1,2)
    AND t.merchantName = 'DUBAI AIRPORT DUTY FREE SHOPPING COMPLEX';
'''
