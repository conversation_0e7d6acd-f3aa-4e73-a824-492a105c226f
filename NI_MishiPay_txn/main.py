from datetime import datetime,<PERSON><PERSON>ta
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import query,config,format_int_with_commas,outputDir, Transacting_output
import XLSXAutoFitColumns
import shutil
pd.options.display.float_format = '{:,.2f}'.format
NI_Daily = data_retrival(query)
for merchant in NI_Daily["ME Name"].unique():
    fileName = Transacting_output
    merchantData = NI_Daily[NI_Daily["ME Name"]==merchant].reset_index(drop=True)
    merchantData = merchantData[['ID','Transaction Created Date', 'User', 'Username','Type', 'Mode', 'Amount', 'Auth Code', 'Card', 'Card','Brand Type',
                                 'RRN', 'Invoice#','Device Serial', 'Status','Settled On','MID','TID','Batch','Reference#', 'Additional Information',
                                 'Latitude', 'Longitude','Payer','Transaction Created Time','Transaction Status' ,'ME Name', 'Acquirer','Response Code',
                                 'Currency','Device No','Reference Txn Id','tg']]
    print(f"Writing {fileName}")
    with pd.ExcelWriter(fileName) as writer:
        merchantData.to_excel(writer,sheet_name=f'DDF',na_rep=' ',index=False)
    fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(fileName)
    fix_worksheet.process_all_worksheets()
print("File Creation is Successfull")
exit()
