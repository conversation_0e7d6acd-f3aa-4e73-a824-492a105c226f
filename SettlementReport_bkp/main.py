from datetime import datetime,timedelta
from sys import exit,argv
import pandas as pd
import mysql.connector
import logging
import time
import os
import numpy as np
from SQLConnectionsFile import data_retrival,connect_to_mysql
from DataQuery import settelmentDateTime,config,input_file,output_file1,output_file2,output_file3,StartTime,ENDTime,cutOffStartTime,cutOffEndTime,format_int_with_commas,date_serial_number,query,ATOS_Transactions,IDFC_Transactions
import XLSXAutoFitColumns

pd.options.display.float_format = '{:,.2f}'.format

raw_data = data_retrival(query)
raw_data.drop_duplicates(subset=['id'],keep='first',inplace=True,ignore_index=True)
raw_data['transactionDate'] = raw_data['transactionTime'].dt.strftime('%d-%m-%Y')
data = raw_data[raw_data['forSettlement']==1]
data.reset_index(inplace=True,drop=True)
Cutoff = pd.read_csv(input_file)
merged_data = pd.merge(data, Cutoff, how='left', left_on='acquirerName',right_on='Acquirer')
merged_data['settlementTxnTime'] = pd.to_datetime(merged_data['settlementTxnTime'])
merged_data['transactionTime'] = pd.to_datetime(merged_data['transactionTime'])
merged_data['Bank Settlement Start Time'] = pd.to_datetime(merged_data['Bank Settlement Start Time'])
merged_data['Bank Settlement Cutoff Time'] = pd.to_datetime(merged_data['Bank Settlement Cutoff Time'])
merged_data['Bank Settlement Start Time'] = merged_data.apply(StartTime,axis=1)
merged_data['Bank Settlement Cutoff Time'] = merged_data.apply(ENDTime,axis=1)
merged_data['settlementTxnTime'] = merged_data.apply(settelmentDateTime,axis=1)
Unsettle_txn = merged_data[merged_data.settlementTxnTime.isnull()]
#Unsettle_txn = Unsettle_txn[Unsettle_txn['transactionTime'] <= Unsettle_txn['Bank Settlement Start Time']]
todays_Data = raw_data[raw_data['transactionDate'] == (datetime.now() - timedelta(days=1)).strftime('%d-%m-%Y')]
Settled_Summary = (todays_Data[~todays_Data.settlementTxnTime.isnull()]
 .groupby(['transactionDate'])
 .aggregate({'terminalID':'nunique','id':'count','amount':'sum'}).reset_index()
)

# Settlement Summary
Settled_Summary.rename(
    {
        'transactionDate':'Total Settled TXN',
        'terminalID':'User Count',
        'id':'Volume',
        'amount':'Value'
    },axis=1,inplace=True
)

# Unsettled Transaction Summary.
# DUMMY Settelment 

merged_data['cutoffStart'] = merged_data.apply(cutOffStartTime,axis=1)
merged_data['cutoffEnd'] = merged_data.apply(cutOffEndTime,axis=1)


condition = [
    ((merged_data['transactionTime'] < merged_data['cutoffStart']) & (merged_data['settlementTxnTime'] > merged_data['cutoffEnd'])),
    ((merged_data['transactionTime'] > merged_data['cutoffStart']) & (merged_data['settlementTxnTime'].isnull())),
    ((merged_data['transactionTime'] < merged_data['cutoffStart']) & (merged_data['settlementTxnTime'].isnull()))
]
choice = ["Late settled",'After Cutoff Unsettled','Unsettled']
merged_data['StatusReport'] = np.select(condition,choice,default='Settled')

Unsettled_Summary = (merged_data[(merged_data["StatusReport"]=='After Cutoff Unsettled') & (merged_data['transactionDate'] ==(datetime.now() - timedelta(days=1)).strftime('%d-%m-%Y')) ]
 .groupby(['transactionDate','acquirerName','tgName'])
 .aggregate({'terminalID':'nunique','id':'count','amount':'sum'}).reset_index()
)
# DUMMY Settelment 
Unsettled_Transaction_Summary = {
    "Atos & Atom" : "Atos & Atom Unsettled TXN after 11-15pm",
    "IDFC" : "IDFC Unsettled TXN after 10-45pm",
    "PP" : "PP Unsettled TXN after 10-45pm",
    "BHARATPE" :"BHARATPE Unsettled TXN after 10-30pm",
    "SBI" : "SBI Unsettled TXN after 10-30pm",
    "INDIANBANK" : "INDIANBANK Unsettled TXN after 11-30pm",
    "YES" : "YES ( TG-ISG ) Unsettled TXN after 11-00pm"
}
Unsettled_Transaction_Summary_Condition = [
    (Unsettled_Summary['transactionDate'] ==(datetime.now() - timedelta(days=1)).strftime('%d-%m-%Y')) & (Unsettled_Summary['tgName']=='WLPFO')  & (Unsettled_Summary['acquirerName'].isin(['KOTAK','YES'])),
    (Unsettled_Summary['transactionDate'] ==(datetime.now() - timedelta(days=1)).strftime('%d-%m-%Y')) & (Unsettled_Summary['tgName']=='FD')  & (Unsettled_Summary['acquirerName']=='IDFC'),
    (Unsettled_Summary['transactionDate'] ==(datetime.now() - timedelta(days=1)).strftime('%d-%m-%Y')) & (Unsettled_Summary['tgName']=='PP')  & (Unsettled_Summary['acquirerName']=='PP'),
    (Unsettled_Summary['transactionDate'] ==(datetime.now() - timedelta(days=1)).strftime('%d-%m-%Y')) & (Unsettled_Summary['tgName']=='GPHYP')  & (Unsettled_Summary['acquirerName']=='BHARATPE'),
    (Unsettled_Summary['transactionDate'] ==(datetime.now() - timedelta(days=1)).strftime('%d-%m-%Y')) & (Unsettled_Summary['tgName']=='HYP')  & (Unsettled_Summary['acquirerName']=='SBI'),
    (Unsettled_Summary['transactionDate'] ==(datetime.now() - timedelta(days=1)).strftime('%d-%m-%Y')) & (Unsettled_Summary['tgName']=='GPHYP') & (Unsettled_Summary['acquirerName']=='INDIANBANK'),
    (Unsettled_Summary['transactionDate'] ==(datetime.now() - timedelta(days=1)).strftime('%d-%m-%Y')) & (Unsettled_Summary['tgName']=='ISG') & (Unsettled_Summary['acquirerName']=='YES  ( TG-ISG )'),
    ]


Unsettled_Transaction_Summary_Choices = ['Atos & Atom','IDFC','PP','BHARATPE','SBI','INDIANBANK','YES  ( TG-ISG )']
Unsettled_Summary['TG_NAME'] = np.select(Unsettled_Transaction_Summary_Condition,Unsettled_Transaction_Summary_Choices,default = 'TO DROP')

DUMMY_Unsettled_Summary = pd.DataFrame(
   { "Dumay" : (datetime.now() - timedelta(days=1)).strftime('%d-%m-%Y'),
    "User count" :0,
    "Volume" : 0,
    "Value" :0
   },index=[0]
)

groupped = merged_data.groupby(['transactionDate','tgName','acquirerName','StatusReport']).aggregate({'id':'nunique','amount':'sum'}).reset_index()

out = groupped.pivot_table(
    index=['transactionDate','tgName','acquirerName'],
    columns='StatusReport',
    values=['id','amount']).reset_index().fillna(0)
out.columns = pd.MultiIndex.from_tuples(out.set_axis(out.columns.values, axis=1).rename(columns={
                                                        ('transactionDate',             ''):('transactionDate',             'Transaction Date'),
                                                        (         'tgName',             ''):(         'tgName',             'TG Name'),
                                                        (         'acquirerName',             ''):(         'acquirerName',             'Acquirer'),
                                                        (         'amount', 'Late settled'): (         'amount', 'Late settled TXN value'),
                                                        (         'amount',      'Settled'):(         'amount',      'Settled TXN value'),
                                                        (         'amount',    'Unsettled'):(         'amount',      'Unsettled TXN value'),
                                                        (         'amount',    'After Cutoff Unsettled'):(         'amount',      'After Cutoff Unsettled TXN value'),
                                                        (             'id', 'After Cutoff Unsettled'):(             'id', 'After Cutoff Unsettled TXN'),
                                                        (             'id', 'Late settled'):(             'id', 'Late settled TXN'),
                                                        (             'id',      'Settled'):(             'id', 'Settled TXN'),
                                                        (             'id',    'Unsettled'):(             'id', 'Unsettled TXN')
                                                       }))
out = out.droplevel(level=0,axis=1)
out.sort_values(by='Transaction Date',ascending=False,ignore_index=True,inplace=True)
column_list = ['Transaction Date','Late settled TXN','Late settled TXN value','Unsettled TXN','Unsettled TXN value','After Cutoff Unsettled TXN','After Cutoff Unsettled TXN value','Settled TXN','Settled TXN value']
for column_name in column_list:
    if column_name not in out.columns:
        out[column_name] = 0

#Unsettled_Summary = (Unsettled_Summary[Unsettled_Summary['TG_NAME'] != 'TO DROP'][['acquirerName','TG_NAME','terminalID','id','amount']]
 #   .rename({'acquirerName':'Acquirer',
 #       'TG_NAME' : 'TG',
 #        'terminalID' : 'User Count',
 #        'id' : 'Volumn',
 #        'amount' : 'Value'
 #       },axis=1)
#).sort_values(['TG','Volumn'],ascending=[True,False],ignore_index=True)
with pd.ExcelWriter(output_file1) as writer:
    Settled_Summary.to_excel(writer,sheet_name='Settled Summary',index=False)
    for TG_Name in Unsettled_Transaction_Summary.keys():
        TG_Data = Unsettled_Summary[Unsettled_Summary['TG_NAME']==TG_Name].reset_index(drop=True)
        if TG_Data.shape[0] > 0:
            final_to_write = pd.DataFrame(
                {
                    Unsettled_Transaction_Summary[TG_Name]  : TG_Data['transactionDate'][0],
                    "User count" : TG_Data.terminalID.sum(),
                    "Volume" : TG_Data.id.sum(),
                    "Value" : TG_Data.amount.sum()
                },index=[0]
            )
        else:
            final_to_write = DUMMY_Unsettled_Summary.rename({"Dumay":Unsettled_Transaction_Summary[TG_Name]},axis=1)
        final_to_write.to_excel(writer,f'{TG_Name}_Unsettled',index=False)
    #Unsettled_Summary.to_excel(writer,sheet_name='Unsettled Summary',index=False)
    for Acquirer in out['Acquirer'].unique():
        if Acquirer in ['HDFC','MOSAMBEEHDFC']:
            FinalToWrite = out[out['Acquirer']==Acquirer][['Transaction Date','Late settled TXN','Late settled TXN value','Unsettled TXN','Unsettled TXN value']]
        else:
            FinalToWrite = out[out['Acquirer']==Acquirer][['Transaction Date','Late settled TXN','Late settled TXN value','Unsettled TXN','Unsettled TXN value','After Cutoff Unsettled TXN','After Cutoff Unsettled TXN value']]
        FinalToWrite.reset_index(inplace=True,drop=True)
        FinalToWrite.to_excel(writer,Acquirer,index=False)
fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(output_file1)
fix_worksheet.process_all_worksheets()
		
ATOS_Data = data_retrival(ATOS_Transactions)
IDFC_Data = data_retrival(IDFC_Transactions)
with pd.ExcelWriter(output_file2) as writer:
    ATOS_Data.to_excel(writer,index=False)

with pd.ExcelWriter(output_file3) as writer:
    IDFC_Data.to_excel(writer,index=False)

fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(output_file2)
fix_worksheet.process_all_worksheets()
fix_worksheet = XLSXAutoFitColumns.XLSXAutoFitColumns(output_file3)
fix_worksheet.process_all_worksheets()
print("File Creation is Successfull")
exit()
