import os 
import sys
import datetime as dt
from datetime import date,datetime,timedelta
import pandas as pd
out_dir="/home/<USER>/Reports/SettlementReport/"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%d%m%Y")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
output_file_name1 =  f'SettlmentReport_{(datetime.now()).strftime("%d_%b_%y")}.xlsx'
output_file_name2 =  f'ATOSUnsettledTXN_{(datetime.now()).strftime("%d_%b_%y")}.xlsx'
output_file_name3 =  f'IDFC_{(datetime.now()).strftime("%d_%b_%y")}.xlsx'

input_file = "/home/<USER>/Reports/Scripts/SettlementReport/SettlementTime.csv"
output_file1 = os.path.join(out_dir,yesterday_file_name,output_file_name1)
output_file2 = os.path.join(out_dir,yesterday_file_name,output_file_name2)
output_file3 = os.path.join(out_dir,yesterday_file_name,output_file_name3)
def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

def date_serial_number(serial_number: int):
    """
    Convert an Excel serial number to a Python datetime object
    :param serial_number: the date serial number
    :return: a datetime object
    """
    # Excel stores dates as "number of days since 1900"
    delta = dt.datetime(1899, 12, 30) + dt.timedelta(days=serial_number)
    return delta

def StartTime(x):
    acquirerName = x['Acquirer']
    Start_time = x['Bank Settlement Start Time']
    if acquirerName not in ["HDFC","MOSAMBEEHDFC"]:
        return Start_time - timedelta(1)
    else:
        return Start_time
def ENDTime(x):
    acquirerName = x['Acquirer']
    Start_time = x['Bank Settlement Cutoff Time']
    if acquirerName not in ["HDFC","MOSAMBEEHDFC","INDIANBANK"]:
        return Start_time - timedelta(1)
    else:
        return Start_time

def cutOffStartTime(x):
    acquirerName = x['Acquirer']
    Start_time = x['Bank Settlement Start Time']
    No_ofDays = x['transactionTime']
    #END_TIME = x['Bank Settlement Cutoff Time']
    return Start_time - timedelta((datetime.date(datetime.now())-datetime.date(No_ofDays)).days-1)

def cutOffEndTime(x):
    acquirerName = x['Acquirer']
    Start_time = x['Bank Settlement Cutoff Time']
    No_ofDays = x['transactionTime']
    #END_TIME = x['Bank Settlement Cutoff Time']
    return Start_time - timedelta((datetime.date(datetime.now())-datetime.date(No_ofDays)).days-1)
def settelmentDateTime(x):
    if ((pd.isnull(x["settlementTxnTime"])) and (x["settlementDate"] != None) and (x['settlementTime']!=None)):
        return pd.to_datetime(str(x['settlementDate'])+ ' ' +str(x['settlementTime']))
    else:
        return x["settlementTxnTime"]
#in_dir = sys.argv[1]
#out_dir = sys.argv[2]
config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}
query = """
SELECT
    t.id,
    t.transactionTime,
    t.settlementTxnTime,
    DATE_FORMAT(t.settlementTxnTime,'%H:%i:%s')as settlementTime,
    DATE_FORMAT(t.settlementTxnTime,'%d-%m-%Y')as settlementDate,
    t.typeID,
    t.acqId,
   -- t.acquirerName,
    CASE
        WHEN t.typeId = 15 AND t.emiType = 'debit' THEN t.additionalAmount / POWER(10, a.decimalAmountLength)
        ELSE t.amount / POWER(10, a.decimalAmountLength)
    END AS amount,
    t.tipAmount / POWER(10, a.decimalAmountLength) AS tipAmount,
    t.additionalAmount / POWER(10, a.decimalAmountLength) AS additionalAmount,
    t.authAmount,
    CASE WHEN t.tempTID is not null and t.tempTID!=0 THEN   CASE WHEN 
t.tempTID!=t.terminalID THEN t.tempTID ELSE t.terminalID END    ELSE 
t.terminalID END as terminalID,
    tp.program_id,
        t.settlementStatusID,
    t.settlementStatusName,
    t.tgName,
    t.forSettlement,
    t.isVoided,
    Case when t.acquirerName ='YES' and t.tgName ='ISG' then 'YES  ( TG-ISG )'
    else t.acquirerName end as acquirerName
FROM
    transactions t
        LEFT JOIN
    acquirer a ON t.acqId = a.id
        LEFT JOIN
    merchant m ON m.id = t.merchantID
        LEFT JOIN
    mapping_merchant_user_terminal mmut ON  t.terminalID = mmut.terminalID
        LEFT JOIN
        mapping_division_acquirer_tg mdat on mmut.divacqtgid = mdat.id
        inner JOIN
        mapping_acquirer_tg mat on mdat.acqTGID = mat.id
        LEFT JOIN
    tbl_program tp ON mmut.programId = tp.program_id
WHERE
    t.transactionTime >= DATE_SUB(CURDATE(), INTERVAL 6 DAY)
        AND t.transactionTime < CURDATE()
        AND t.userName IS NOT NULL
        AND (CASE
        WHEN
            t.typeId = 2
        THEN
            t.referenceTxnID NOT IN (SELECT
                    t1.id
                FROM
                    transactions t1
                WHERE
                    t1.typeId = 8 AND statusId = 3
                        AND t1.id = t.referenceTxnID)
        ELSE 1
    END)
        AND t.typeID IN (1)
        AND t.statusId IN (2,7,10)
        AND t.isVoided = 0
       -- and  t.forSettlement =1 
       AND case when mmut.status is Null then 'null'
            when mmut.status = 'D' and mmut.recordUpdated >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        AND mmut.recordUpdated < CURDATE() then 'A'
                        else mmut.status end in ('A','null','H')
       AND (CASE
        WHEN
            t.acquirerName = 'GPIN'
        THEN
            t.tgid = mat.tgid
        ELSE 1
    END)
   -- and t.tgName in ('WLPFO','FD','PP','GPHYP','HYP','ISG','HDFC')
    and t.acquirerName in ('YES','KOTAK','IDBI','PNB','JNK','CUB','PP','SBI','RBL','UNITED','HDFC','MOSAMBEEHDFC','BHARATPE','IDFC','BOI','UBI','INDIANBANK')
;
"""

ATOS_Transactions = '''
SELECT 
    t.id AS ID,
    CONCAT(' ', DATE(t.transactionTime)) AS 'Transaction Created Date',
    t.clientId AS User,
    t.userName AS Username,
    CASE
        WHEN t.typeID = 1 AND t.isTip IS NOT NULL THEN 'Tip Adjusted'
        ELSE t.transactionTypeName
    END AS Type,
    t.modeName AS Mode,
    CASE
        WHEN t.typeID = 15 AND t.emiType = 'debit' THEN t.additionalAmount / POWER(10, a.decimalAmountLength)
        ELSE t.amount / POWER(10, a.decimalAmountLength)
    END AS Amount,
    t.authCode AS 'Auth Code',
    t.maskedCardNumber AS Card,
    t.drCrCode AS 'Card Type',
    t.cardTypeName AS 'Brand Type',
    CONCAT(' ', t.rrn) AS RRN,
    t.invoiceNumber AS 'Invoice#',
    t.serialNumber AS 'Device Serial',
    CASE
        WHEN t.settlementStatusName IS NULL THEN 'UNSETTLED'
        ELSE t.settlementStatusName
    END AS Status,
    CONCAT(' ', t.settlementTxnTime) AS 'Settled On',
    CASE
        WHEN
            t.tempMerchantCode IS NOT NULL
                AND t.tempMerchantCode != 0
        THEN
            CASE
                WHEN t.tempMerchantCode != t.merchantCode THEN CONCAT(' ', t.tempMerchantCode)
                ELSE CONCAT(' ', t.merchantCode)
            END
        WHEN t.merchantCode IS NULL THEN CONCAT(' ', mmut.merchantCode)
        ELSE CONCAT(' ', t.merchantCode)
    END AS MID,
    CASE
        WHEN
            t.tempTID IS NOT NULL AND t.tempTID != 0
        THEN
            CASE
                WHEN t.tempTID != t.terminalID THEN t.tempTID
                ELSE t.terminalID
            END
        WHEN t.terminalID IS NULL THEN mmut.terminalID
        ELSE t.terminalID
    END AS TID,
    t.batchNumber AS Batch,
    t.billNumber AS 'Reference#',
    CASE
        WHEN
            t.statusID NOT IN (2 , 7, 10)
                AND t.typeID NOT IN (3 , 10, 11, 16, 7)
        THEN
            CASE
                WHEN
                    t.tgID IS NOT NULL
                        AND tdc.description != ''
                THEN
                    tdc.description
                WHEN t.tgID IS NULL AND tr.description != '' THEN tr.description
            END
        ELSE NULL
    END AS 'Additional Information',
    t.latitude AS Latitude,
    t.longitude AS Longitude,
    t.cardHolderName AS Payer,
    t.securityToken AS 'TID Location',
    CONCAT(' ', TIME(t.transactionTime)) AS 'Transaction Created Time',
    t.txnStatusName AS 'Transaction Status',
    t.merchantName AS 'ME Name',
    t.acquirerName AS Acquirer,
    IFNULL(t.responseCode, '91') AS 'Response Code',
    t.currencyCode AS Currency,
    t.deviceID AS 'Device No',
    t.referenceTxnID AS 'Reference Txn Id',
    t.tgName AS tg,
    t.dbaName AS 'DBA Name',
    t.refundStatus AS 'Refund Status',
    CONCAT(' ', DATE(t.txnResponseTime)) AS 'Transaction Response Date',
    CONCAT(' ', TIME(t.txnResponseTime)) AS 'Transaction Response Time',
    m.enterpriseId,
    CASE
        WHEN
            settlementStatusID != 4
        THEN
            CASE
                WHEN t.forSettlement = 1 THEN 'Auto Settlement'
                WHEN t.forSettlement = 0 THEN 'Manual Settlement'
                WHEN t.forSettlement = 2 THEN 'Customized Settlement'
            END
        ELSE 'NA'
    END AS 'Settlement Flag'
FROM
    transactions t
        LEFT JOIN
    acquirer a ON t.acquirerName = a.name
        LEFT JOIN
    (SELECT 
        id, enterpriseId, status
    FROM
        merchant
    WHERE
        status = 'A') m ON t.merchantID = m.id
        LEFT JOIN
    emiData emi ON t.id = emi.transactionId
        LEFT JOIN
    transaction_status ts ON t.statusID = ts.id
        LEFT JOIN
    transaction_type tt ON tt.id = t.typeID
        LEFT JOIN
    txn_decline_codes tdc ON t.tgId = tdc.tgID
        AND t.responseCode = tdc.responseCode
        LEFT JOIN
    txn_decline_codes tr ON t.responseCode = tr.responseCode
        AND tr.tgID IS NULL
        LEFT JOIN
    (SELECT 
        mmut.id, mmut.userName, mmut.terminalID, mdat.merchantCode
    FROM
        mapping_merchant_user_terminal mmut
    INNER JOIN mapping_division_acquirer_tg mdat ON mdat.id = mmut.divAcqTgId
    WHERE
        mmut.status = 'A'
    GROUP BY mmut.userName
    HAVING mmut.id = MIN(mmut.id)) mmut ON mmut.userName = t.userName
WHERE
    t.transactionTime >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        AND t.transactionTime < CURDATE()
AND t.typeID = 1
        AND t.tgName = 'WLPFO'
        AND t.statusId IN (2,7,10)
        AND t.isVoided = 0
        AND t.forSettlement =1 
        AND t.settlementTxnTime is NULL;
'''

IDFC_Transactions = '''
SELECT 
    t.id AS ID,
    CONCAT(' ', DATE(t.transactionTime)) AS 'Transaction Created Date',
    t.clientId AS User,
    t.userName AS Username,
    CASE
        WHEN t.typeID = 1 AND t.isTip IS NOT NULL THEN 'Tip Adjusted'
        ELSE t.transactionTypeName
    END AS Type,
    t.modeName AS Mode,
    CASE
        WHEN t.typeID = 15 AND t.emiType = 'debit' THEN t.additionalAmount / POWER(10, a.decimalAmountLength)
        ELSE t.amount / POWER(10, a.decimalAmountLength)
    END AS Amount,
    t.authCode AS 'Auth Code',
    t.maskedCardNumber AS Card,
    t.drCrCode AS 'Card Type',
    t.cardTypeName AS 'Brand Type',
    CONCAT(' ', t.rrn) AS RRN,
    t.invoiceNumber AS 'Invoice#',
    t.serialNumber AS 'Device Serial',
    CASE
        WHEN t.settlementStatusName IS NULL THEN 'UNSETTLED'
        ELSE t.settlementStatusName
    END AS Status,
    CONCAT(' ', t.settlementTxnTime) AS 'Settled On',
    CASE
        WHEN
            t.tempMerchantCode IS NOT NULL
                AND t.tempMerchantCode != 0
        THEN
            CASE
                WHEN t.tempMerchantCode != t.merchantCode THEN CONCAT(' ', t.tempMerchantCode)
                ELSE CONCAT(' ', t.merchantCode)
            END
        WHEN t.merchantCode IS NULL THEN CONCAT(' ', mmut.merchantCode)
        ELSE CONCAT(' ', t.merchantCode)
    END AS MID,
    CASE
        WHEN
            t.tempTID IS NOT NULL AND t.tempTID != 0
        THEN
            CASE
                WHEN t.tempTID != t.terminalID THEN t.tempTID
                ELSE t.terminalID
            END
        WHEN t.terminalID IS NULL THEN mmut.terminalID
        ELSE t.terminalID
    END AS TID,
    t.batchNumber AS Batch,
    t.billNumber AS 'Reference#',
    CASE
        WHEN
            t.statusID NOT IN (2 , 7, 10)
                AND t.typeID NOT IN (3 , 10, 11, 16, 7)
        THEN
            CASE
                WHEN
                    t.tgID IS NOT NULL
                        AND tdc.description != ''
                THEN
                    tdc.description
                WHEN t.tgID IS NULL AND tr.description != '' THEN tr.description
            END
        ELSE NULL
    END AS 'Additional Information',
    t.latitude AS Latitude,
    t.longitude AS Longitude,
    t.cardHolderName AS Payer,
    t.securityToken AS 'TID Location',
    CONCAT(' ', TIME(t.transactionTime)) AS 'Transaction Created Time',
    t.txnStatusName AS 'Transaction Status',
    t.merchantName AS 'ME Name',
    t.acquirerName AS Acquirer,
    IFNULL(t.responseCode, '91') AS 'Response Code',
    t.currencyCode AS Currency,
    t.deviceID AS 'Device No',
    t.referenceTxnID AS 'Reference Txn Id',
    t.tgName AS tg,
    t.dbaName AS 'DBA Name',
    t.refundStatus AS 'Refund Status',
    CONCAT(' ', DATE(t.txnResponseTime)) AS 'Transaction Response Date',
    CONCAT(' ', TIME(t.txnResponseTime)) AS 'Transaction Response Time',
    m.enterpriseId,
    CASE
        WHEN
            settlementStatusID != 4
        THEN
            CASE
                WHEN t.forSettlement = 1 THEN 'Auto Settlement'
                WHEN t.forSettlement = 0 THEN 'Manual Settlement'
                WHEN t.forSettlement = 2 THEN 'Customized Settlement'
            END
        ELSE 'NA'
    END AS 'Settlement Flag'
FROM
    transactions t
        LEFT JOIN
    acquirer a ON t.acquirerName = a.name
        LEFT JOIN
    (SELECT 
        id, enterpriseId, status
    FROM
        merchant
    WHERE
        status = 'A') m ON t.merchantID = m.id
        LEFT JOIN
    emiData emi ON t.id = emi.transactionId
        LEFT JOIN
    transaction_status ts ON t.statusID = ts.id
        LEFT JOIN
    transaction_type tt ON tt.id = t.typeID
        LEFT JOIN
    txn_decline_codes tdc ON t.tgId = tdc.tgID
        AND t.responseCode = tdc.responseCode
        LEFT JOIN
    txn_decline_codes tr ON t.responseCode = tr.responseCode
        AND tr.tgID IS NULL
        LEFT JOIN
    (SELECT 
        mmut.id, mmut.userName, mmut.terminalID, mdat.merchantCode
    FROM
        mapping_merchant_user_terminal mmut
    INNER JOIN mapping_division_acquirer_tg mdat ON mdat.id = mmut.divAcqTgId
    WHERE
        mmut.status = 'A'
    GROUP BY mmut.userName
    HAVING mmut.id = MIN(mmut.id)) mmut ON mmut.userName = t.userName
WHERE
    t.transactionTime >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        AND t.transactionTime < CURDATE()
AND t.typeID = 1
        AND t.acquirerName = 'IDFC'
        AND t.statusId IN (2,7,10)
        AND t.isVoided = 0
      --  AND t.forSettlement =1 
       -- AND t.settlementTxnTime is NULL;
'''
