#!/bin/bash

dir=$2
cd $dir
tabcmd login -s http://************:8080 -u tabadmin -p $1
foldername=$(date -d "yesterday" '+%Y%m%d')
echo $foldername
[ ! -d "$foldername" ] && mkdir -p "$foldername"
cd $foldername
tabcmd export "/BharatPEDaily/Sheet1?:refresh=yes" --csv -f "BharatPEDaily.csv"
cd ..

reportName="BharatPE_Daily"

python3 /home/<USER>/Reports/Scripts/BharatPE_Daily/main.py $reportName $foldername $dir
filename="BharatPE_Daily.csv"

python3 /home/<USER>/Reports/Scripts/BharatPE_Daily/mail.py $foldername $filename $dir

cd $dir
cd $foldername
echo "Completed"
