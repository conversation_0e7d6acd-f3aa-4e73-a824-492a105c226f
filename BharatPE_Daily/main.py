from sys import exit,argv
import os
import pandas as pd
import numpy as np
import string
from datetime import date,timedelta
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%d%m%Y")
reportName = argv[1]
foldername = argv[2]
dir = argv[3]
os.chdir(os.path.join(dir,foldername))
print(f'current Directory {os.getcwd()}')
def Convert_pect(df,col):
    df[col] = pd.Series(["{0:.2f}%".format(val * 100) for val in df[col]], index = df.index)

# First Report
BharatPE_raw = pd.read_csv("BharatPEDaily.csv")
BhartaPE_Orderd = BharatPE_raw[['ID','Transaction Created Date', 'User', 'Username', 'partnerMid', 'partnerTid','tspMid', 'tspTid','Type', 'Mode', 'Amount', 'Auth Code', 'Card', 'Card Type'
 ,'Brand Type', 'RRN', 'Invoice#','Device Serial', 'Status','Settled On','MID','TID', 
 'Batch','Reference#', 'Additional Information','Latitude', 'Longitude','Payer','Transaction Created Time',
  'Transaction Status' ,'ME Name', 'Acquirer','Response Code','Currency','Device No','Reference Txn Id',
  'tg','DBA Name', 'Refund Status','Transaction Response Date','Transaction Response Time', 'Settlement Flag','Provider ID','IMSI Number']]
file = f'BharatPE_{yesterday_file_name}_{235959}.csv'
#BhartaPE_Orderd.to_csv("BharatPE_Daily.csv",index=False)
BhartaPE_Orderd.to_csv(file,index=False)

exit()

