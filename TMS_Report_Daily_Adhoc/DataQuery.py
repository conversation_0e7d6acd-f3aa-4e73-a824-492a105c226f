import os 
import sys
import datetime as dt
from datetime import timedelta,datetime,date

out_dir="/home/<USER>/Reports/TMS_Report_Daily_Adhoc"
in_dir = os.getcwd()
today = date.today()
yesterday = today - timedelta(days = 1)
yesterday_file_name = yesterday.strftime("%d%m%Y")
os.chdir(out_dir)
if not os.path.exists(yesterday_file_name):
    os.makedirs(yesterday_file_name)
#os.chdir(in_dir)
Transacting_output_FileName = f'MOSAMBEE_TMS_TRANSACTION_REPORT_{(datetime.now() - timedelta(1)).strftime("%d%m%Y")}.csv'
Non_Transacting_output_FileName = f'MOSAMBEE_TMS_NON_TRANSACTION_REPORT_{(datetime.now() - timedelta(1)).strftime("%d%m%Y")}.csv'

Transacting_output = os.path.join(out_dir,yesterday_file_name,Transacting_output_FileName)
Non_Transacting_output = os.path.join(out_dir,yesterday_file_name,Non_Transacting_output_FileName)

def format_int_with_commas(x):
    """
    Formats an integer with commas as thousand separators.
    """
    return f"{x:,}"

def date_serial_number(serial_number: int):
    """
    Convert an Excel serial number to a Python datetime object
    :param serial_number: the date serial number
    :return: a datetime object
    """
    # Excel stores dates as "number of days since 1900"
    delta = dt.datetime(1899, 12, 30) + dt.timedelta(days=serial_number)
    return delta

#in_dir = sys.argv[1]
#out_dir = sys.argv[2]
config = {
    "host": "***********",
    "port": "3331",
    "user": "tableau",
    "password": "Tableau@1234$",
    "database": "sfn_transaction",
}


NonTransactingQuery = """
select
@sr:=@sr+1 as `sr no`,
terminals as 'Tid' ,
'NA' as 'Device_serial',
'NA' as 'Machine Name/device_version' ,
'NA' as 'Application Version',
MID AS 'mid',
businessName AS 'name',
'HDFC' AS 'acquirer',
'' as 'type',
mcc as 'mcc',
`city` as 'city',
`state` as 'state',
installation_date as "Installation Date",
last_login_date AS 'Heartbeat/Last login date',
last_login_date AS 'last_txn_date',
'' as 'latitude',
'' as 'longitude',
'N' as 'upi_enabled',
'N' as 'bqr_enabled',
'N' as 'smspay_enabled',
'N' as 'nfc_enabled',
'N' as 'emi_enabled',
integrated as 'integrated',
'0' as 'total_txn_count',
'0' as 'total_txn_volume',
'0' as 'dc_txn_count',
'0' as 'dc_txn_volume',
'0' as 'cc_txn_count',
'0' as 'cc_txn_volume',
'0' as 'upi_txn_count',
'0' as 'upi_txn_volume',
'0' as 'bqr_txn_count',
'0' as 'bqr_txn_volume',
'0' as 'emi_txn_count',
'0' as 'emi_txn_volume',
'0' as 'sms_txn_count',
'0' as 'sms_txn_count_volume',
'0' as `total_txn_count>=5000`,
'0' as `total_txn_volume>=5000`,
'0' as `total_txn_count>=1000`,
'0' as `total_txn_volume>=1000`
from (SELECT @sr:= 0) AS sr,(
select
  terminals,installation_date,userId,last_login_date,merchantsId,userIds,mcc,city,state,mid,businessName,recordCreated,integrated
from
(SELECT
        mmut.terminalID AS 'terminals',
            mdat.merchantCode AS 'MID',
            mmut.divisionId AS 'merchantsId',
             mmut.userName as 'userNames',
             u.id as userIds,
             m.businessName as businessName,
             m.mcc as mcc,
             c.name AS `city`,
s.name AS `state`,
        m.recordCreated,
        u.id as userId,
        '' as recordcreatedLogs1,
                date_format(mmut.recordCreated, '%Y-%m-%d %H:%i:%s') AS 'installation_date',
        CASE
                WHEN MAX(mmut.lastCardTxn) IS NULL OR 0 THEN mmut.recordCreated
                ELSE MAX(mmut.lastCardTxn)
            END AS last_login_date,
        CASE WHEN isIntegrated = '1' THEN 'Integrated' ELSE 'non-Integrated' END  as integrated
    FROM mapping_merchant_user_terminal mmut
    INNER JOIN mapping_division_acquirer_tg mdat ON mmut.divacqTgId = mdat.id
    INNER JOIN mapping_acquirer_tg mat ON mat.id = mdat.acqTgId
    INNER JOIN tg tt ON mat.tgID = tt.id
    INNER JOIN users u ON u.userName=mmut.userName
inner join merchant m on m.id = mmut.divisionId
    inner join address a on m.addressID = a.id
    inner join city c on a.cityID = c.id
    inner join state s on a.stateID = s.id
    INNER JOIN login_logs l ON l.userId = u.id
    WHERE
        mmut.status = 'A'
            AND tt.name LIKE 'HDFC' and tt.typeID=1
    GROUP BY mmut.terminalID -- , l.userID
    ) a
WHERE a.terminals IN (SELECT t.terminalId FROM transactions t force index (transactions_trantime)  WHERE t.statusID IN (2 , 7)
AND t.tgID IN (4 , 21, 32, 33, 34, 35, 36, 45, 46, 47, 48, 49, 79, 80, 81)
AND t.acqId != 23
AND t.terminalID not like '68%'
AND t.transactionTime BETWEEN '2025-04-01 00:00:00' AND '2025-04-27 23:59:59'
GROUP BY t.terminalId
) IS NOT TRUE
group by userIds
 
UNION
(
select
  terminals,installation_date,userId,last_login_date,merchantsId,userIds,mcc,city,state,mid,businessName,recordCreated,integrated
from
(SELECT
        mmut.terminalID AS 'terminals',
            mdat.merchantCode AS 'MID',
            mmut.divisionId AS 'merchantsId',
             mmut.userName as 'userNames',
             u.id as userIds,
             m.businessName as businessName,
             m.mcc as mcc,
             c.name AS `city`,
s.name AS `state`,
        m.recordCreated,
        u.id as userId,
        date_format(mmut.recordCreated, '%Y-%m-%d %H:%i:%s') AS 'installation_date',
           CASE
                WHEN MAX(mmut.lastCardTxn) IS NULL OR 0 THEN mmut.recordCreated
                ELSE MAX(mmut.lastCardTxn)
            END AS last_login_date,
        CASE WHEN isIntegrated = '1' THEN 'Integrated' ELSE 'non-Integrated' END  as integrated
    FROM mapping_merchant_user_terminal mmut
    INNER JOIN mapping_division_acquirer_tg mdat ON mmut.divacqTgId = mdat.id
    INNER JOIN mapping_acquirer_tg mat ON mat.id = mdat.acqTgId
    INNER JOIN tg tt ON mat.tgID = tt.id
    INNER JOIN users u ON u.userName=mmut.userName
inner join merchant m on m.id = mmut.divisionId
    inner join address a on m.addressID = a.id
    inner join city c on a.cityID = c.id
    inner join state s on a.stateID = s.id
    left join login_logs l on l.userId=u.id
    WHERE
        mmut.status = 'A'
            AND tt.name LIKE 'HDFC' and tt.typeID=1
    GROUP BY mmut.terminalID -- , l.userID
    ) a
WHERE a.terminals IN (SELECT  t.terminalId FROM transactions t force index (transactions_trantime) WHERE t.statusID IN (2 , 7)
AND t.tgID IN (4 , 21, 32, 33, 34, 35, 36, 45, 46, 47, 48, 49, 79, 80, 81)
AND t.acqId != 23
and t.terminalID not like '68%'
AND t.transactionTime BETWEEN '2025-04-01 00:00:00' AND '2025-04-27 23:59:59'
GROUP BY t.terminalId
) IS NOT TRUE
group by userIds
)
) a
where terminals not like '68%'
  group by userId
  order by `sr no`
;
"""
